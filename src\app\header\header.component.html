<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons [slot]="closeSlot" *ngIf="showBackButton || closeLabel">
      <ion-back-button *ngIf="showBackButton" [defaultHref]="backHref"></ion-back-button>
      <ion-button color="medium" *ngIf="closeLabel" (click)="onClose()">{{ closeLabel }}</ion-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
    <ion-buttons slot="end" *ngIf="showUtilities">
      <ion-toggle label="Theme" (click)="switchTheme()">Theme</ion-toggle>
      <ion-button (click)="changeLanguage('an.json')">
        <ion-icon name="flag" slot="icon-only"></ion-icon>
        <img src="assets/english.png" alt="English" />
      </ion-button>
      <ion-button (click)="changeLanguage('fr.json')">
        <ion-icon name="flag" slot="icon-only"></ion-icon>
        <img src="assets/french.png" alt="French" />
      </ion-button>
      <ion-button (click)="changeLanguage('ar.json')">
        <ion-icon name="flag" slot="icon-only"></ion-icon>
        <img src="assets/arab.png" alt="Arabic" />
      </ion-button>
      <ion-button>
        <ion-icon name="help-circle"></ion-icon> {{ translate('HELP') }}
      </ion-button>
      <ion-button (click)="logout()">
        <ion-icon name="log-out"></ion-icon> {{ translate('LOGOUT') }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>