import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { TablesUpgrades } from 'src/app/upgrades/tables/tables';
import { DbnameVersionService } from './dbname-version.service';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class DbCreationTablesService {
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;

   constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
    
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }

async initializeDatabase() {
  if (this.sqliteService.platform === 'web') {
    // Vérifie si la base est déjà stockée dans IndexedDB
    const dbExists = await this.sqliteService.sqliteConnection.isDatabase(this.databaseName);
    if (dbExists.result) {
      console.log(`🔄 La base de données '${this.databaseName}' existe déjà, ouverture directe.`);
      await this.openDatabase();
      return;
    }
  }

  console.log(`🆕 Création de la base de données '${this.databaseName}'`);

  await this.sqliteService.addUpgradeStatement({
    database: this.databaseName,
    upgrade: this.versionUpgrades
  });

  await this.openDatabase();
  this.dbVerService.set(this.databaseName, this.loadToVersion);

  const isData = await this.mDb.query("SELECT * FROM sqlite_sequence");

  if (isData.values!.length === 0) {
    await this.createInitialData();
  }

  // Sauvegarde la base dans IndexedDB après l'initialisation
  if (this.sqliteService.platform === 'web') {
    await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    console.log("✅ Base sauvegardée dans IndexedDB");
  }
}

  
  
  
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, true, "secret",
          this.loadToVersion, false);

    } else {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, false, "no-encryption",
          this.loadToVersion, false);
    }
  }
  

  private async createInitialData(): Promise<void> {
    // Ajouter ici les instructions pour insérer les données initiales dans la base de données
    // Exemple :
    // await this.mDb.execute('INSERT INTO my_table (column1, column2) VALUES (?, ?)', [value1, value2]);
  }
  public getDatabase(): SQLiteDBConnection {
    return this.mDb;
  }
}
