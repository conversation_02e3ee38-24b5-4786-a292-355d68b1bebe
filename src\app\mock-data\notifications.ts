
import { Notification } from '../models/notifications';
export const mockNotification: Notification[] = [
    {
        id: 1,
        notificationText: 'Notification 1',
        notificationDay: 15,
        notificationMonth: 7,
        notificationYear: 2024,
        notificationMinutes: 30,
        notificationHour: 10,
        status: 1
    },
    {
        id: 2,
        notificationText: 'Notification 2',
        notificationDay: 20,
        notificationMonth: 7,
        notificationYear: 2024,
        notificationMinutes: 15,
        notificationHour: 14,
        status: 0
    },
    {
        id: 3,
        notificationText: 'Notification 3',
        notificationDay: 25,
        notificationMonth: 7,
        notificationYear: 2024,
        notificationMinutes: 15,
        notificationHour: 9,
        status: 1
    },
    {
        id: 4,
        notificationText: 'Notification 4',
        notificationDay: 1,
        notificationMonth: 8,
        notificationYear: 2024,
        notificationMinutes: 45,
        notificationHour: 16,
        status: 0
    },
    {
        id: 5,
        notificationText: 'Notification 5',
        notificationDay: 5,
        notificationMonth: 8,
        notificationYear: 2024,
        notificationMinutes: 50,
        notificationHour: 12,
        status: 1
    }
];
