

  export class sector {
    id!: number;
    name!: string;
  }  
  export class specialitie {
    id!: number;
    name!: string;
  }     
  export class localitie {
    id!: number;
    name!: string;
  }
  export class potential {
    id!: number;
    name!: string;
  }
  export class activitie {
    id!: number;
    name!: string;
  }
  export class establishment {
    id!: number;
    name!: string;
  }
  export class product {
    id!: number;
    name!: string;
  }
  export class Prospect {
  
  id!: number;
  firstname!: string;
  lastname!: string;
  grade?:string;
  note?:string;
  typeId?: number;
  sectorName?: string; // Add new properties
  sectorId?: number; 
  localityName?: string;
  localityId?: number;
  activity?: string;
  address?: string;
  gsm?:string;
  establishmentName?: string;
  establishmentId?: number;
  secretary?:string;
  specialityId?: number;
  specialityName?: string;
  potentialName?: string;
  potentialId?: number;
  telephone?: string;
  phone?: string;
  email?: string;

  

  }
  
  
  