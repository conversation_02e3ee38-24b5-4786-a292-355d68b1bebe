/* Conteneur de la date cliquable */
.date-header {
    color: #3572E0;            /* même bleu que ton ancien header */
    font-size: 1.25rem;        /* taille un peu plus grosse */
    margin: 1rem 0;            /* espacement autour */
  }
  .date-header a {
    color: #3572E0;
    text-decoration: none;
  }
  .date-header a:hover {
    text-decoration: underline;
  }
  
  /* Table responsive à l’ancienne */
  .responsive-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1%;
  }
  .responsive-table th,
  .responsive-table td {
    border: 1px solid #ddd;
    padding: 0.75rem;
    text-align: left;
  }
  .responsive-table th {
    background-color: #3572E0; /* header bleu */
    color: #fff;               /* texte blanc */
    font-weight: 600;
  }
  .responsive-table tbody tr:nth-child(even) {
    background-color: #f5f5f5; /* bandes grises claires */
  }
  
  /* Si tu veux un fond général pour chaque visite */
  .visit-block {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
  }
  
  /* Boutons « Modifier » / « Supprimer » */
  .btn-outline-primary {
    border-color: #3572E0;
    color: #3572E0;
  }
  .btn-outline-primary:hover {
    background-color: #3572E0;
    color: #fff;
  }
  .btn-outline-danger {
    border-color: #e74c3c;
    color: #e74c3c;
  }
  .btn-outline-danger:hover {
    background-color: #e74c3c;
    color: #fff;
  }
  