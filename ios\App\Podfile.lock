PODS:
  - Capacitor (5.0.5):
    - CapacitorCordova
  - CapacitorApp (5.0.2):
    - Capacitor
  - CapacitorCommunitySqlite (5.0.5-2):
    - Capacitor
    - SQLCipher
    - ZIPFoundation
  - CapacitorCordova (5.0.5)
  - CapacitorDialog (5.0.4):
    - Capacitor
  - CapacitorHaptics (5.0.2):
    - Capacitor
  - CapacitorKeyboard (5.0.2):
    - Capacitor
  - CapacitorStatusBar (5.0.2):
    - Capacitor
  - CapacitorToast (5.0.4):
    - Capacitor
  - SQLCipher (4.5.4):
    - SQLCipher/standard (= 4.5.4)
  - SQLCipher/common (4.5.4)
  - SQLCipher/standard (4.5.4):
    - SQLCipher/common
  - ZIPFoundation (0.9.16)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCommunitySqlite (from `../../node_modules/@capacitor-community/sqlite`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDialog (from `../../node_modules/@capacitor/dialog`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - "CapacitorToast (from `../../node_modules/@capacitor/toast`)"

SPEC REPOS:
  trunk:
    - SQLCipher
    - ZIPFoundation

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCommunitySqlite:
    :path: "../../node_modules/@capacitor-community/sqlite"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDialog:
    :path: "../../node_modules/@capacitor/dialog"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CapacitorToast:
    :path: "../../node_modules/@capacitor/toast"

SPEC CHECKSUMS:
  Capacitor: b1248915663add1bd6567e2b67c1c1fa3abcf5e8
  CapacitorApp: 28fef1fd75b2b3686e875216806fb8416d421097
  CapacitorCommunitySqlite: 409b6d635462e9e409c2a9f3b3ce2fa9538d0055
  CapacitorCordova: f8c06b897c74ee8f7701fe10e6443b40822bc83a
  CapacitorDialog: fe8b913cdf8c73cabe556974565fc44f2a09c75f
  CapacitorHaptics: 864585542a435bd41eaabf7f30d9ff5ec03024d3
  CapacitorKeyboard: e628d4e66d621c69e449945ebabded17c5b9c2e8
  CapacitorStatusBar: 48f2899f6846cc7d8431b251ebfc58e1c10e3d58
  CapacitorToast: fcc4329f01d06cf02f190f8f5ce7f16509a2263c
  SQLCipher: 905b145f65f349f26da9e60a19901ad24adcd381
  ZIPFoundation: d170fa8e270b2a32bef9dcdcabff5b8f1a5deced

PODFILE CHECKSUM: 9b2e6fd0d2a733925d5cca8776db855ba0f43dcf

COCOAPODS: 1.10.0
