ion-content {
  --background: #ffffff;
}

.log_form {
  padding: 16px;
}

.status {
  text-align: center;
  padding: 10px;
  margin-bottom: 10px;
  font-weight: bold;
}

.status.new {
  width: 300px;
  margin: auto;
  background-color: #639fa8;
  border-radius: 20px;
}

.status.waiting {
  background-color: #ffc107;
  color: black;
}

.status.accepted {
  background-color: #007bff;
  color: black;
}

.status.review {
  background-color: #6c757d;
  color: black;
}

.status.refused {
  background-color: #dc3545;
  color: black;
}

.warning {
  text-align: center;
  padding: 10px;
  background-color: #f8d7da;
  color: #721c24;
  margin-bottom: 10px;
  border: 1px solid #f5c6cb;
}

ion-grid {
  border: 1px solid #dee2e6;
  margin-top: 10px;
}

ion-row {
  border-bottom: 1px solid #dee2e6;
}

ion-col {
  text-align: center;
  padding: 10px;
}

.selectedDayHeader {
  background-color: #007bff;
  color: white;
}

.selectedDay {
  background-color: #e9ecef;
  text-align: center;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.prospect-link {
  color: #007bff;
  text-decoration: none;
}

.prospect-link:hover {
  text-decoration: underline;
}

.notes {
  margin-top: 10px;
}

.note-item {
  background-color: #fff3cd;
  color: #856404;
  padding: 10px;
  border: 1px solid #ffeeba;
  margin-bottom: 10px;
  text-align: center;
}

.item-input {
  margin-top: 10px;
}

.autocompleteStyle {
  position: relative;
}

.autocompleteStyle ion-list {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #dee2e6;
  z-index: 1000;
}

ion-item ion-label {
  margin-bottom: 5px;
}

ion-select {
  width: 100%;
}

ion-button {
  margin-top: 10px;
}

ion-button:disabled {
  opacity: 0.5;
}

.ion-autocomplete {
  cursor: pointer;
}

.action-button {
  background-color:#f8f8f8;
  .center-content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;
  }
}

.table-header {
  background-color: #007bff;
  color: white;
  text-align: center;
  font-weight: bold;
  
  ion-col {
    border-right: 1px solid white;
    &:last-child {
      border-right: none;
    }
  }
}

.table-body {
  background-color: #f8f8f8;

  ion-col {
    border-right: 1px solid #e0e0e0; 
    &:last-child {
      border-right: none; 
    }
    padding: 8px; 
  }
}

.add-prospect-button,
.add-all-prospect-button {
  text-align: center; 
  padding: 8px; 
  
}

.add-prospect-button {
  display: flex;
  justify-content: center; 
  align-items: center; 
}

/* Grid Cards Styling */

      .card {
        background-color: #ffffff; 
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); 

        ion-card {
          height: 100%; 

          ion-card-header {
            background-color:#007bff; 
            color: #333333;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;

            ion-card-title {
              font-size: 1.2rem;
              font-weight: bold;
            }
          }

          ion-card-content {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;

            canvas {
              width: auto !important;
              height: auto !important;
            }
          }
        }
      }
    

