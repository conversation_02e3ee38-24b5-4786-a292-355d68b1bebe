import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Notification } from 'src/app/models/notifications';
import { DateService } from 'src/app/services/date.service';
import { NotificationService } from 'src/app/services/notification.service';
import { takeUntil } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { LoginService } from 'src/app/services/login-service.service';
import { ThemeService } from 'src/app/services/theme.service';
import { HeaderComponent } from '../../header/header.component';


@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
  standalone:true, 
  imports:[IonicModule,FormsModule,CommonModule,HeaderComponent]
})
export class NotificationsComponent  implements OnInit {
  isDarkTheme = false;
  notifications: Notification[] = [];
  selectedDate: Date = new Date();
  destroy$ = new Subject<void>();
  private translationSubscription?: Subscription
  private languageSubscription?: Subscription
  currentLanguage = "an.json"
  translations: any = {};


  constructor(private themeService: ThemeService,private LoginService : LoginService,private notificationsService: NotificationService, private dateService:DateService, private translationService:TranslationService){}
  ngOnInit() {
  // ✅ S'abonner aux changements de traductions
  this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
    console.log("🔄 notification - Traductions mises à jour:", translations)
    this.translations = translations
  })

  // ✅ S'abonner aux changements de langue
  this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
    console.log("🌐 notification - Langue changée vers:", language)
    this.currentLanguage = language
  })
    this.selectedDate = new Date();
    this.getAllNotifications();
  }

   getAllNotifications() {

    this.notificationsService.getAllNotifications()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: notifications => this.notifications = notifications,
        error: error => console.error('Error fetching notifications:', error)
      });
  }
  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
    this.destroy$.next()
    this.destroy$.complete()
  }
  async getNotificationsByDate() {
    this.notifications = await this.notificationsService.getNotificationsByDate(this.dateService.getDayFromDate (this.selectedDate),this.dateService.getMonthFromDate (this.selectedDate),this.dateService.getYearFromDate (this.selectedDate));
  

  }
  
  onDateChange(event: any) {
    let selectedDateString = new Date(event.detail.value);
    this.selectedDate = new Date(selectedDateString);
    this.getNotificationsByDate();
  
  }
 
  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ notification - Traduction manquante: ${key}`)
    }
    return translation || key
  }
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 notification - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ notification - Langue changée avec succès")
    } catch (error) {
      console.error("❌ notification - Erreur changement langue:", error)
    }
  }
  private loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;
        
      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  } 
 
  
  
    logout() {
      this.LoginService.logout();
    }
    onThemeChange(): void {
      this.themeService.switchTheme();
    }
 
}
