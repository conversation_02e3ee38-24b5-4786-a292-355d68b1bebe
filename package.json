{"name": "Bird-Note-Crm", "version": "7.0.0", "description": "Bird Note Crm", "author": "Intellitech", "license": "MIT", "homepage": "./", "scripts": {"ng": "ng", "start": "ionic serve", "build:web": "npm run copy:sql:wasm && ng build", "build:native": "npm run remove:sql:wasm && ng build", "watch": "npm run copy:sql:wasm && ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "ionic:serve:before": "npm run copy:sql:wasm", "copy:sql:wasm": "copyfiles -u 3 node_modules/sql.js/dist/sql-wasm.wasm src/assets", "remove:sql:wasm": "rimraf src/assets/sql-wasm.wasm", "ionic:ios": "npm run remove:sql:wasm && ionic capacitor build ios", "ionic:android": "npm run remove:sql:wasm && ionic capacitor build android", "electron:install": "cd electron && npm install && cd ..", "electron:prepare": "npm run remove:sql:wasm && ng build && npx cap sync @capacitor-community/electron && npx cap copy @capacitor-community/electron", "electron:start": "npm run electron:prepare && cd electron && npm run electron:start"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@capacitor-community/electron": "^5.0.1", "@capacitor-community/sqlite": "^6.0.1", "@capacitor/android": "^6.1.0", "@capacitor/app": "6.0.0", "@capacitor/camera": "^6.0.1", "@capacitor/core": "^6.1.0", "@capacitor/dialog": "^6.0.0", "@capacitor/filesystem": "^6.0.0", "@capacitor/geolocation": "^6.1.0", "@capacitor/haptics": "6.0.0", "@capacitor/ios": "^6.1.0", "@capacitor/keyboard": "6.0.1", "@capacitor/preferences": "^6.0.1", "@capacitor/status-bar": "6.0.0", "@capacitor/toast": "^6.0.1", "@ionic/angular": "^8.0.0", "@ionic/pwa-elements": "^3.3.0", "@ionic/storage-angular": "^4.0.0", "chart.js": "^4.4.3", "crypto-browserify": "^3.12.0", "ionic-selectable": "^5.0.3", "ionicons": "^7.4.0", "primeicons": "^7.0.0", "primeng": "^17.18.9", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.0", "@angular-eslint/builder": "^18.0.0", "@angular-eslint/eslint-plugin": "^18.0.0", "@angular-eslint/eslint-plugin-template": "^18.0.0", "@angular-eslint/schematics": "^18.0.0", "@angular-eslint/template-parser": "^18.0.0", "@angular/cli": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/compiler-cli": "^18.0.0", "@angular/language-service": "^18.0.0", "@capacitor/cli": "6.1.0", "@ionic/angular-toolkit": "^11.0.1", "@types/google.maps": "^3.58.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.14.9", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "copyfiles": "^2.4.1", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "rimraf": "^5.0.7", "ts-node": "^10.9.2", "typescript": "~5.4.0"}, "repository": {"type": "git", "url": "https://github.com/jepiqueau/ionic7-angular-sqlite-starter.git"}, "bugs": {"url": "https://github.com/jepiqueau/ionic7-angular-sqlite-starter.git/issues"}}