export class Expense  {
    id!:number;
    activityId!:number;
    expenseDate!:number;
    description!:string;
    montant!: number;
    expenseTypeId!:number;
    attachementBase64: string
    attachmentName: string;
    status!: string;
    synchronized!:number ;
    constructor(){
      this.attachementBase64='';
      this.attachmentName='';

    }
  
  }
  export class ExpenseType  {
    id!:number;
    name!:string;
    amount!:number;
    requiredAttachment!:boolean; }
