
  
  // Styles pour la section logo
  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--ion-color-light);
  }
  
  .logo-container {
    cursor: pointer;
    transition: transform 0.2s ease;
    user-select: none;
    
    &:hover {
      transform: scale(1.05);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  .app-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: 10px;
    border-radius: 8px;
  }
  
  .version-text {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
    margin: 0;
    text-align: center;
    font-weight: 500;
  }
  
  // Animation pour le feedback visuel lors des clics
  @keyframes logoClick {
    0% { transform: scale(1); }
    50% { transform: scale(0.9); }
    100% { transform: scale(1); }
  }
  
  .logo-container.clicked {
    animation: logoClick 0.2s ease;
  }
  
  // Responsive design
  @media (max-width: 480px) {
    .login-container {
      padding: 15px;
    }
    
    .app-logo {
      width: 60px;
      height: 60px;
    }
    
    .version-text {
      font-size: 0.8rem;
    }
  }
  