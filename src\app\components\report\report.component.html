<app-header [title]="translate('REPORT')"></app-header>

<br><br><br>

<ion-content >
  <ion-row>
    <ion-col>
      <ion-item button detail (click)="openDatePicker()">
        <ion-label>
          {{ LABELS.WEEK_OF }}: {{ weekMondayDay | date:'dd/MM/yyyy' }}
        </ion-label>
        <ion-icon slot="end" name="calendar-outline"></ion-icon>
      </ion-item>
  
      <ion-datetime
        *ngIf="showDatePicker"
        presentation="date"
        [isDateEnabled]="isMonday"
        [value]="weekMondayDay.toISOString()"
        (ionChange)="onWeekChange($event)"
        (ionCancel)="showDatePicker = false"
        (ionConfirm)="showDatePicker = false"
      ></ion-datetime>
    </ion-col>
  </ion-row>
  
  
  <app-filter
  [_sectorSelected]="_sectorSelected"
  [_localitySelected]="_localitySelected"
  [_specialitySelected]="_specialitySelected"
  [_potentialSelected]="_potentialSelected"
  [_activitySelected]="_activitySelected"
  [_establishmentSelected]="_establishmentSelected"
  [prospectNameFilter]="keywordsFilter"
  [sectors]="sectors"
  [localities]="localities"
  [specialities]="specialities"
  [potentials]="potentials"
  [activities]="activities"
  [establishments]="establishments"
  [LABELS]="LABELS"
  [filterProspects]="filterProspects.bind(this)"
  [filterLocalitiesBySector]="filterLocalitiesBySector"
  [filterEstablishmentByActivity]="filterEstablishmentByActivity"
  [syncSelections]="syncSelections.bind(this)"
  ></app-filter>

<br>
<br>
<ion-grid>
  <ion-row>
    <!-- Show Only Planned Prospects -->
    <ion-col>
      <ion-item>
        <ion-toggle
          [(ngModel)]="showOnlyPlanifiedProspects"
          (ionChange)="filterProspects(_sectorSelected, _localitySelected, _establishmentSelected, _specialitySelected, _potentialSelected, _activitySelected, keywordsFilter, showOnlyPlanifiedProspects, showOnlyNotVisited, null)"
          color="success">
        </ion-toggle>
        <ion-label>
          <strong>{{ LABELS.VIEW_TODAY_PLANNED_PROSPECTS }}</strong>
        </ion-label>
      </ion-item>
    </ion-col>

    <!-- Show Only Not Visited Prospects -->
    <ion-col>
      <ion-item>
        <ion-toggle
          [(ngModel)]="showOnlyNotVisited"
          (ionChange)="filterProspects(_sectorSelected, _localitySelected, _establishmentSelected, _specialitySelected, _potentialSelected, _activitySelected, keywordsFilter, showOnlyPlanifiedProspects, showOnlyNotVisited, null)"
          color="success">
        </ion-toggle>
        <ion-label>
          <strong>{{ LABELS.SHOW_ONLY_PROSPECT_NOT_VISITED_WEEK }}</strong>
        </ion-label>
      </ion-item>
    </ion-col>

    <!-- Show Map -->
    <ion-col *ngIf="prospects.length && googleMapIsAvailable">
      <ion-item>
        <ion-toggle [(ngModel)]="showMap" (ionChange)="setShowMap()" color="success"></ion-toggle>
        <ion-label><strong>{{ LABELS.SHOW_MAP }}</strong></ion-label>
      </ion-item>
    </ion-col>
  </ion-row>
    <!-- the map container -->
    <ion-row *ngIf="showMap">
      <ion-col>
        <div #mapContainer style="width:100%; height:400px;"></div>
      </ion-col>
    </ion-row>
</ion-grid>


 
<ion-scroll direction="x" class="prospects-scroll">
  <div class="prospects-inner">
    <!-- “Add prospect” button -->
    <ion-button expand="block" fill="clear" routerLink="/prospect-detail">
      <img src="assets/plus.png" class="add-img" />
      <span class="prospect-name">{{ LABELS.ADD_PROSPECT }}</span>
    </ion-button>

    <!-- Your real prospects -->
    <div
      *ngFor="let prospect of prospects"
      class="add-card prospect-card"
      [class.selected]="selectedProspect.id === prospect.id"
      (click)="selectProspect(prospect)"
      (dblclick)="openProspectDetails(prospect.id)"
    >
      <img class="prospect-img" src="assets/doctor_v5.png" />
      <p class="prospect-name"[style.color]="prospect.backgroundColor">{{ prospect.name }}</p>
      <p class="prospect-speciality">{{ prospect.speciality }}</p>
    </div>
  </div>
</ion-scroll>



  <!-- Footer Showing Count of Prospects -->
  <ion-footer style="margin-top: 22px; font-size: 18px; text-align: center; font-weight: bold;">
    <span *ngIf="!isListOfVisitedProspects">
      {{ LABELS.NUMBER_OF_PROSPECTS }}: {{ prospects.length }}
    </span>
    <span *ngIf="isListOfVisitedProspects">
      {{ LABELS.NUMBER_OF_VISITED_PROSPECTS }}: {{ prospects.length }}
    </span>
  </ion-footer>

  <ion-card *ngIf="selectedProspect && note?.note && note?.link && loadingPlan">
    <ion-card-content>
      <ion-grid>
        <ion-row class="ion-align-items-center">
          <!-- Left Column: Image -->
          <ion-col size="2">
            <ion-img
              src="assets/hello.gif"
              style="width: 50px; text-align: left;"
            ></ion-img>
          </ion-col>
  
          <!-- Right Column: Note and Link -->
          <ion-col size="10" style="color: white; background-color: #e6b500; font-weight: bold;">
            <div>
              Note: {{ note.note }}
              <br />
              <br />
              Lien: <a>{{ note.link }}</a>
            </div>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  
  <ion-grid *ngIf="selectedProspect && loadingPlan"   >
    <ion-row class="ion-align-items-center">
      <!-- Visit History Toggle -->
      <ion-col>
        <ion-item lines="none">
          <ion-toggle
            [(ngModel)]="showHistory"
            (ionChange)="setShowHistory()"
            color="success"
          ></ion-toggle>
          <ion-label>
            <strong>{{ LABELS.VIEW_VISIT_HISTORY }}</strong>
          </ion-label>
        </ion-item>
      </ion-col>
  
      <!-- PO History Toggle -->
      <ion-col>
        <ion-item lines="none">
          <ion-toggle
            [(ngModel)]="showPoHistory"
            (ionChange)="setShowPoHistory()"
            color="success"
          ></ion-toggle>
          <ion-label>
            <strong>{{ LABELS.VIEW_PO_HISTORY }}</strong>
          </ion-label>
        </ion-item>
      </ion-col>
  
      <!-- Filters Toggle -->
      <ion-col>
        <ion-item lines="none">
          <ion-toggle
            [(ngModel)]="showFilters"
            (ionChange)="showHideFilter()"
            color="success"
          ></ion-toggle>
          <ion-label>
            <strong>{{ LABELS.SHOW_RANGE_FILETR }}</strong>
          </ion-label>
        </ion-item>
      </ion-col>
    </ion-row>
  </ion-grid>
  <app-visit-section
  *ngIf="showHistory || showPoHistory"
  [visitsHistory]="visitsHistory"
  [modifyDate]="modifyDate"
  [openReportPeriod]="openReportPeriod"
  [historyIn]="historyIn"
  [userId]="userId"
></app-visit-section>


  <ion-list *ngIf="selectedProspect && showFilters && loadingPlan">
    <ion-item *ngFor="let range of ranges; let i = index">
      <ion-checkbox
        slot="start"
        [(ngModel)]="range.checked"
        (ionChange)="filterProductsByRanges()"
      ></ion-checkbox>
      <ion-label>{{ range.name }}</ion-label>
    </ion-item>
  </ion-list>

  <div style="text-align: center" *ngIf="showLoading">
    <ion-spinner name="crescent" style="width: 50px; height: 50px;"></ion-spinner>
  </div>  

  <div *ngIf="showDetailedProspectView && selectedProspect && weekMondayDay && prospects.length > 0 && !showLoading && loadingPlan">
    <br>
    <div style="margin-bottom: 10px;">
      <!-- Visit Tab -->
      <ion-button
      id="tab-visit-button"
      fill="solid"
      [color]="selectedTab === 'VISIT' ? 'warning' : 'primary'"
      (click)="onClickTab('VISIT')"
    >
      {{ LABELS.VISIT }}
    </ion-button>
      <!-- Order Tab -->
      <ion-button
      id="tab-order-button"
        *ngIf="currentUser?.work_type === 'PHARMACEUTICAL' && canOrder"
        [ngClass]="{ 'ion-color-calm': selectedTab !== 'ORDER', 'ion-color-energized': selectedTab === 'ORDER' }"
        [color]="selectedTab === 'ORDER' ? 'warning' : 'primary'"
        (click)="onClickTab('ORDER'); onPurchaseOrderTabClick()"
        fill="solid"
      >
        {{ LABELS.ORDER }}
      </ion-button>
    
      <!-- Pointing Position Tab -->
      <ion-button
      id="tab-position-button"
        [color]="selectedTab === 'POSITION' ? 'warning' : 'primary'"
        (click)="onClickTab('POSITION'); onPositionClick()"
        fill="solid"
      >
        {{ LABELS.POINTING_POSITION }}
      </ion-button>
    </div>
    

    <ion-row>
      <!-- Monday -->
      <ion-col>
        <ion-button
        id="btn-day-monday"
          expand="block"
          fill="solid"
          [color]="currentTab === 0 ? 'warning' : 'primary'"
          (click)="selectDay(0)"
        >
          {{ LABELS.MONDAY }} {{ mondayDate | date: 'dd/MM' }}
        </ion-button>
      </ion-col>
    
      <!-- Tuesday -->
      <ion-col>
        <ion-button
        id="btn-day-tuesday"
          expand="block"
          fill="solid"
          [color]="currentTab === 1 ? 'warning' : 'primary'"
          (click)="selectDay(1)"
        >
          {{ LABELS.TUESDAY }} {{ tuesdayDate | date: 'dd/MM' }}
        </ion-button>
      </ion-col>
    
      <!-- Wednesday -->
      <ion-col>
        <ion-button
        id="btn-day-wednesday"
          expand="block"
          fill="solid"
          [color]="currentTab === 2 ? 'warning' : 'primary'"
          (click)="selectDay(2)"
        >
          {{ LABELS.WEDNESDAY }} {{ wednesdayDate | date: 'dd/MM' }}
        </ion-button>
      </ion-col>
    
      <!-- Thursday -->
      <ion-col>
        <ion-button
        id="btn-day-thursday"
          expand="block"
          fill="solid"
          [color]="currentTab === 3 ? 'warning' : 'primary'"
          (click)="selectDay(3)"
        >
          {{ LABELS.THURSDAY }} {{ thursdayDate | date: 'dd/MM' }}
        </ion-button>
      </ion-col>
    
      <!-- Friday -->
      <ion-col>
        <ion-button
        id="btn-day-friday"
          expand="block"
          fill="solid"
          [color]="currentTab === 4 ? 'warning' : 'primary'"
          (click)="selectDay(4)"
        >
          {{ LABELS.FRIDAY }} {{ fridayDate | date: 'dd/MM' }}
        </ion-button>
      </ion-col>
    
      <!-- Saturday -->
      <ion-col *ngIf="currentUser?.working_days === 6">
        <ion-button
        id="btn-day-saturday"
          expand="block"
          fill="solid"
          [color]="currentTab === 5 ? 'warning' : 'primary'"
          (click)="selectDay(5)"
        >
          {{ LABELS.SATURDAY }} {{ saturdayDate | date: 'dd/MM' }}
        </ion-button>
      </ion-col>
    </ion-row>
    
    
 
  
  <ng-container *ngIf="lockAfterSync">
    <div style="background-color: #FF5C4D; margin-top: 8px;">
      <h3 class="title">{{ LABELS.REPORT_IS_SENT }}</h3>
    </div>
  </ng-container>
  
  <ng-container *ngIf="lockAfterPeriod">
    <div style="background-color: #FF5C4D; margin-top: 8px;">
      <h3 class="title">
        {{ LABELS.CHECK_PERIOD }} {{ LABELS.REPORT }} {{ openReportPeriod }} {{ LABELS.DAY }} {{ LABELS.AFTER_CURRENT_DATE }}
      </h3>
    </div>
  </ng-container>


  <div *ngIf="selectedTab === 'VISIT' && loadingPlan && selectedProspect">
    <!-- Lock the Section -->
    <div [ngStyle]="{ 'pointer-events': lockAfterPeriod || lockAfterSync ? 'none' : '' }">
  
      <!-- VISIT Tab -->
      <div >
        <ion-grid class="responsive-table">
          <!-- HEADER: exactly 7 columns -->
          <ion-row class="header">
            <!-- 1) Order / reset icon -->
            <ion-col size="2">
              <ion-img
                src="assets/reset.png"
                style="width:15%"
                (click)="showResetOrderDialog(visit)"
              ></ion-img><br/>
              {{ LABELS.RANK }}
            </ion-col>
        
            <!-- 2) Product -->
            <ion-col size="2">
              {{ LABELS.PRODUCT }}
            </ion-col>
        
            <!-- 3) Comment -->
            <ion-col size="4">
              {{ LABELS.COMMENT }}
            </ion-col>
        
            <!-- 4) Emergency icon -->
            <ion-col size="1">
              <ion-img
                src="assets/emergency.png"
                style="width:15%"
              ></ion-img>
            </ion-col>
        
            <!-- 5) Smiley column header (blank) -->
            <ion-col size="1"></ion-col>
        
            <!-- 6) Prescription or Stock (toggles) -->
            <ion-col size="2" *ngIf="canPres">
              {{ LABELS.NUMBER_PRESCRIPTION }}
            </ion-col>
            <ion-col size="2" *ngIf="canOrder && !canPres">
              {{ LABELS.STOCK }}
            </ion-col>
        
            <!-- 7) Sample quantity ("Échantillons") -->
            <ion-col size="2">
              {{ LABELS.EMG }}
            </ion-col>
          </ion-row>
        
          <!-- DATA ROWS: also 7 columns -->
        <ng-container *ngFor="let visitProduct of visit.visitProducts">
          <ion-row *ngIf="visitProduct.show">
            <!-- 1) Rank -->
            <ion-col size="2" id="product_{{ visitProduct.product_id }}">
              <ion-input
                type="number"
                [(ngModel)]="visitProduct.rank"
                readonly
                (click)="fillRank(visitProduct.product_id, visit); setNotSavedVisitData()"
              ></ion-input>
            </ion-col>
        
            <!-- 2) Product name & price -->
            <ion-col size="2">
              <a (click)="openProductDetails(visitProduct.product_id)">
                <u>{{ visitProduct.productName }}</u>
              </a><br/>
              ({{ visitProduct.productPrice }} DT)
            </ion-col>
        
            <!-- 3) Comment textarea -->
          <!-- Ligne ~200 - Remplacer la section ion-col size="4" existante -->
          <ion-col size="4">
            <ion-textarea
              [id]="'product_comment_' + visitProduct.product_id"
              [(ngModel)]="visitProduct.comment"
              (ionFocus)="completeComment(visitProduct.product_id)"
              (ionInput)="completeComment(visitProduct.product_id)"
              (ionBlur)="clearCommentSuggestions()"
              placeholder="Your comment…"
              rows="2">
            </ion-textarea>
          
          </ion-col>
            
   
        
            <!-- 4) Urgent checkbox -->
            <ion-col size="1">
              <ion-checkbox
                [(ngModel)]="visitProduct.urgent"
                (ionChange)="setNotSavedVisitData()"
              ></ion-checkbox>
            </ion-col>
        
            <!-- 5) Smiley picker -->
            <ion-col size="1">
              <div class="smiley-container">
                <!-- active -->
                <img
                  class="active-smiley"
                  [src]="'assets/smily/' + visitProduct.smily + '.png'"
                />
                <!-- tooltip -->
                <div class="smiley-tooltip">
                  <img
                    *ngFor="let smily of [0,1,2,3,4,5]"
                    class="tooltip-smiley"
                    [src]="'assets/smily/' + smily + '.png'"
                    (click)="setSmily(visitProduct.product_id, smily, visit.visitProducts)"
                  />
                </div>
              </div>
            </ion-col>
        
            <!-- 6) Number prescriptions or stock -->
            <ion-col size="2" *ngIf="canPres">
              <ion-input
                type="number"
                [(ngModel)]="visitProduct.prescription_quantity"
                (keyup)="updateVisits(visitProduct, visit)"
                (change)="setNotSavedVisitData()"
              ></ion-input>
            </ion-col>
            <ion-col size="2" *ngIf="canOrder && !canPres">
              <ion-input
                type="number"
                [(ngModel)]="visitProduct.sale_quantity"
                (keyup)="updateVisits(visitProduct, visit)"
                (change)="setNotSavedVisitData()"
              ></ion-input>
            </ion-col>
        
            <!-- 7) Sample quantity -->
            <ion-col size="2">
              <ion-input
                type="number"
                [(ngModel)]="visitProduct.sample_quantity"
                (keyup)="updateVisits(visitProduct, visit)"
                (change)="setNotSavedVisitData()"
              ></ion-input>
            </ion-col>
          </ion-row>
        </ng-container>
        </ion-grid>
        
  
        <ion-grid class="footer-table">
          <!-- first footer row -->
          <ion-row *ngIf="visit.type === 'visit'">
            <!-- General remark: wide cell -->
            <ion-col size="8">
              <ion-label class="footer-label">{{ LABELS.GENERAL_REMARK }}:</ion-label>
              <ion-textarea
                [(ngModel)]="visitData.generalNote"
                (ngModelChange)="setNotSavedVisitData()"
                (ionFocus)="completeGeneralComment()"
                (ionInput)="completeGeneralComment()"
                (ionBlur)="clearCommentSuggestions()"
                rows="1">
              </ion-textarea>
             
            </ion-col>
        
            <ion-col size="2" class="footer-col">
              <ion-label class="footer-label">{{ LABELS.TAG_USER }}:</ion-label>
            
              <!-- 1) Show the users you’ve already tagged -->
              <div *ngFor="let u of tagedUsers" class="tagged-user">
                <span class="tagged-name">{{ u.name }}</span>
                <button class="remove-btn" (click)="deleteUser(u)">&times;</button>
              </div>
                        
              <!-- 2) The search-as-you-type input -->
              <ion-item lines="inset">
                <ion-input
                  type="search"
                  [(ngModel)]="userSearchText"
                  (ionFocus)="filterUsers(userSearchText)"
                  (ionInput)="filterUsers(userSearchText)"
                  (ionBlur)="clearUsersFilter()"      
                  placeholder="{{ LABELS.RESEARCH }}"
                ></ion-input>
              </ion-item>
              <ion-list *ngIf="filtredUsers.length">
                <ion-item
                  *ngFor="let u of filtredUsers"
                  (click)="chooseUser(u)"
                >
                  {{ u.name }}
                </ion-item>
              </ion-list>
            </ion-col>
            
        
            <!-- # of patients: narrow cell -->
            <ion-col size="2">
              <ion-label class="footer-label">{{ LABELS.NOMBRE_OF_PATIENT }}:</ion-label>
              <ion-input
                type="number"
                [(ngModel)]="visitData.patientNumber"
                (ngModelChange)="setNotSavedVisitData()"
              ></ion-input>
            </ion-col>
          </ion-row>
        
          <!-- second footer row -->
          <ion-row *ngIf="visit.type === 'visit'">
            <ion-col size="6">
              <ion-label class="footer-label">{{ LABELS.DOUBLE_VISIT }}:</ion-label>
              <ion-select
              id="select-double-visit"
                [(ngModel)]="visitData.selectedCompanion"
                (ionChange)="setNotSavedVisitData()"
              >
                <ion-select-option [value]="null">
                  
                </ion-select-option>
                <ion-select-option
                  *ngFor="let d of delegates"
                  [value]="d"
                >{{ d.name }}</ion-select-option>
              </ion-select>
            </ion-col>
            <ion-col size="2">
              <ion-label class="footer-label">{{ LABELS.CONTACT_TYPE }}:</ion-label>
              <ion-select [(ngModel)]="visitData.selectedContactType" (ionChange)="setNotSavedVisitData()">
              <ion-select-option [value]="null"></ion-select-option>
              <ion-select-option *ngFor="let ct of contactTypes" [value]="ct">{{ ct.name }}</ion-select-option>
              </ion-select>
              </ion-col>
            <ion-col size="2">
              <ion-label class="footer-label">{{ LABELS.GADGET }}:</ion-label>
              <ion-select
              id="gadget-select"
                [(ngModel)]="visitData.selectedGadget"
                (ionChange)="setNotSavedVisitData()"
              >
                <ion-select-option [value]="null">
                  
                </ion-select-option>
                <ion-select-option
                  *ngFor="let g of gadgets"
                  [value]="g"
                >{{ g.name }}</ion-select-option>
              </ion-select>
            </ion-col>
                        <ion-col size="2" *ngIf="visitData.selectedGadget">
              <ion-label class="footer-label">{{ LABELS.QUANTITY }}:</ion-label>
              <ion-input
                type="number"
                [(ngModel)]="visitData.gadgetQuantity"
                (ngModelChange)="setNotSavedVisitData()"
              ></ion-input>
            </ion-col>
          </ion-row>
        
          <!-- save button -->
          <ion-row>
            <ion-col>
              <ion-button
              id="save-visit-button"
                *ngIf="selectedTab==='VISIT'"
                expand="block"
                (click)="saveVisitAndPo()"
              >{{ LABELS.SAVE }}</ion-button>
            </ion-col>
          </ion-row>
        </ion-grid>
        
      </div>
    </div>
  </div>
  






  <div *ngIf="currentUser?.work_type === 'PHARMACEUTICAL' && selectedTab === 'ORDER'">
  <div>
    <!-- Add Purchase Order -->
    <ion-button *ngIf="canOrder" expand="block" class="activated" [style.pointerEvents]="lockAfterPeriod || lockAfterSync ? 'none' : ''"
      style="width: 60%; margin: auto;" (click)="addPO(undefined, 0, false)">
      {{ LABELS.ADD_PURCHASE_ORDER }}
    </ion-button>
    <br />
  
    <!-- Purchase Order Template -->
    <span *ngIf="isPurchaseOrderTemplate">
      <strong>{{ LABELS.IS_PURCHASE_ORDER_TEMPLATE }}</strong>
    </span>
  
    <!-- Purchase Orders -->
    <div *ngFor="let purchaseOrder of purchaseOrders; let i = index">
      <!-- Purchase Order List -->
      <ion-button
      *ngIf="purchaseOrder.purchaseOrderId === 0"
      fill="clear"
      color="danger"
      (click)="withdrawPo(i)"
    >
      <ion-icon name="close-circle"></ion-icon>
    </ion-button>
      <ion-grid *ngIf="canOrder" [style.pointerEvents]="lockAfterPeriod || lockAfterSync ? 'none' : ''" class="responsive-table">

        <ion-row>
          <ion-col>
            <ion-label>{{ LABELS.PRODUCT }}</ion-label>
            <ion-toggle
              [(ngModel)]="showBuyingPrice"
              (ionChange)="toogleProductPrice(showBuyingPrice)"
            ></ion-toggle>
            <span class="po-header-label">{{ LABELS.SHOW_WHOLESALE_PRICE }}</span>
          </ion-col>
          <ion-col *ngIf="canOrder" style="width: 10%">
            {{ LABELS.ORDERED_QUANTITY }}
          </ion-col>
          <ion-col *ngIf="canOrder" style="width: 10%">
            {{ LABELS.GRATUITY }}
          </ion-col>
          <ion-col *ngIf="canOrder" style="width: 10%">
            {{ LABELS.GRATUITY_LABO }}
          </ion-col>
        </ion-row>
  
        <!-- Visit Products -->
        <ion-row *ngFor="let visitProduct of purchaseOrder.visitProducts" [hidden]="!visitProduct.show">
          <ion-col>
            <!-- Product name as a link -->
            <a
              (click)="openProductDetails(visitProduct.product_id)"
              class="product-link"
            >
              {{ visitProduct.productName }}
            </a>
          
            <!-- Price and stock info -->
            <div class="product-info">
              ({{ visitProduct.productPrice }} DT),
              <span
                *ngIf="visitProduct.stock === 0"
                class="stock-zero"
              >
                {{ LABELS.STOCK }}: 0
              </span>
              <span
                *ngIf="visitProduct.stock > 0"
                class="stock-normal"
              >
                {{ LABELS.STOCK }}: {{ visitProduct.stock }}
              </span>
            </div>
          
          </ion-col>
          
          <!-- 2) QTE col -->
          <ion-col *ngIf="canOrder" style="width: 10%">
            <ion-input
              type="number"
              [(ngModel)]="visitProduct.order_quantity"
              (ionChange)="
                setNotSavedPoData();
                updateVisits(visitProduct, purchaseOrder);
                calculateTotalPrice();
                setFreeQuantity(visitProduct)
              "
            ></ion-input>
          </ion-col>

          <!-- 3) Gratuité col -->
          <ion-col *ngIf="canOrder" style="width: 10%">
            <ion-input
              type="number"
              [(ngModel)]="visitProduct.freeOrder"
              (ionChange)="
                setNotSavedPoData();
                updateVisits(visitProduct, purchaseOrder)
              "
            ></ion-input>
          </ion-col>

          <!-- 4) Gratuité Labo col -->
          <ion-col *ngIf="canOrder" style="width: 10%">
            <ion-input
              type="number"
              [(ngModel)]="visitProduct.labGratuity"
              (ionChange)="
                setNotSavedPoData();
                updateVisits(visitProduct, purchaseOrder)
              "
            ></ion-input>
          </ion-col>
        </ion-row>
      </ion-grid>
      <br />


      <ion-grid *ngIf="canOrder" [ngStyle]="{'pointer-events': (lockAfterPeriod || lockAfterSync) ? 'none' : 'auto'}">
        <ion-row>
          <ion-col>
            <ion-card>
              <ion-card-header>
                <ion-card-title>{{ LABELS.ORDER_TOTAL }}</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <b>{{ purchaseOrder.orderTotalprice | number }} DT</b>
              </ion-card-content>
            </ion-card>
          </ion-col>
      
          <ion-col>
            <ion-card>
              <ion-card-header>
                <ion-card-title>{{ LABELS.WHOLESALER }}</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <ion-item>
                  <ion-input 
                    [(ngModel)]="purchaseOrder.selectedWholeSalerName"
                    (click)="onClickWolesalers(purchaseOrder)"
                    (blur)="clearWholesalersFilter(purchaseOrder)"
                    (keyup)="filterWholesalers(purchaseOrder)"
                    placeholder="{{ LABELS.WHOLESALER }}">
                  </ion-input>
                </ion-item>
                <ion-list *ngIf="purchaseOrder.filtredWholeSalers?.length">
                  <ion-item *ngFor="let wholeSaler of purchaseOrder.filtredWholeSalers" 
                            (click)="chooseWholesaler(wholeSaler, purchaseOrder)">
                    {{ wholeSaler.name }}
                  </ion-item>
                </ion-list>
              </ion-card-content>
            </ion-card>
          </ion-col>
      
          <ion-col *ngIf="purchaseOrder.selectedWholesaler">
            <ion-card>
              <ion-card-header>
                <ion-card-title>{{ LABELS.ORDER }}</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <ion-button id="take-picture-button" expand="full" color="primary" (click)="takePictureFromCamera(purchaseOrder)">
                  {{ LABELS.TAKE_PICTURE }}
                </ion-button>
                <p>{{ LABELS.OR_CHOOSE_FILE }}</p>
                <input id="poFile-{{purchaseOrder.index}}" accept="image/*" type="file"
                  (change)="choosePoFile(purchaseOrder); setNotSavedPoData()" />
                <ion-button color="danger" (click)="showPODeleteConfirm(purchaseOrder)">
                  <ion-icon name="trash"></ion-icon>
                </ion-button>
              </ion-card-content>
            </ion-card>
          </ion-col>
      
          <ion-col *ngIf="purchaseOrder.selectedWholesaler">
            <ion-card>
              <ion-card-header>
                <ion-card-title>{{ LABELS.PO_BY_PONE }}</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <ion-toggle [(ngModel)]="purchaseOrder.orderPlacementByPhoneChecked"
                            (ionChange)="setOrderPlacement(purchaseOrder)">
                </ion-toggle>
                <ion-button id="call-button" color="tertiary">
                  <ion-icon name="call"></ion-icon>
                </ion-button>                
              </ion-card-content>
            </ion-card>
          </ion-col>
      
          <ion-col *ngIf="purchaseOrder.selectedWholesaler">
            <ion-card>
              <ion-card-header>
                <ion-card-title>{{ LABELS.DOC_TO_GENERATE }}</ion-card-title>
              </ion-card-header>
              <ion-card-content>
                <ion-toggle [(ngModel)]="purchaseOrder.generatePo"
                            (ionChange)="setGeneratePo(purchaseOrder)">
                  {{ LABELS.PURCHASE_ORDER }}
                </ion-toggle>
                <ion-toggle [(ngModel)]="purchaseOrder.generateDo"
                            (ionChange)="setGenerateDo(purchaseOrder)">
                  {{ LABELS.DELIVARY_ORDER }}
                </ion-toggle>
              </ion-card-content>
            </ion-card>
          </ion-col>
        </ion-row>
        <ion-row class="ion-justify-content-center" *ngIf="purchaseOrder?.attachment?.base64Image">
          <ion-col size="auto">
            <ion-img 
              [src]="purchaseOrder.attachment.base64Image"
              (click)="showAttachmentImage(purchaseOrder.attachment)"
              style="max-width: 200px; margin: auto; cursor: pointer;">
            </ion-img>
          </ion-col>
        </ion-row>
        
      </ion-grid>
      
  
      <!-- Recovery Section -->
      <ion-item lines="none">
        <ion-label>
          <strong>{{ LABELS.RECOVERY }}</strong>
        </ion-label>
        <ion-toggle
          [(ngModel)]="purchaseOrder.showRecovery"
          (ionChange)="setShowRecovery(purchaseOrder)"
        ></ion-toggle>
      </ion-item>
  
      <ion-grid *ngIf="purchaseOrder.showRecovery">
        <ion-row>
          <ion-col>{{ LABELS.DATE }}</ion-col>
          <ion-col>{{ LABELS.AMOUNT }}</ion-col>
          <ion-col>{{ LABELS.PAYMENT }}</ion-col>
          <ion-col>{{ LABELS.DESCRIPTION }}</ion-col>
          <ion-col>{{ LABELS.ATTACHMENT }}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-input
              type="date"
              [(ngModel)]="purchaseOrder.recovery.date"
              (ionChange)="setNotSavedPoData()"
            ></ion-input>
          </ion-col>
          <ion-col>
            <ion-input
              type="number"
              [(ngModel)]="purchaseOrder.recovery.amount"
              (ionChange)="setNotSavedPoData()"
            ></ion-input>
          </ion-col>
          <ion-col>
            <ion-select
              [(ngModel)]="purchaseOrder.recovery.payment"
              (ionChange)="setNotSavedPoData()"
            >
              <ion-select-option
                *ngFor="let payment of payments"
                [value]="payment.id"
              >
                {{ payment.value }}
              </ion-select-option>
            </ion-select>
          </ion-col>
          <ion-col>
            <ion-textarea
              [(ngModel)]="purchaseOrder.recovery.description"
              (ionChange)="setNotSavedPoData()"
            ></ion-textarea>
          </ion-col>
          <ion-col>
            <ion-button (click)="takePictureFromCamera(purchaseOrder.recovery)">
              {{ LABELS.TAKE_PICTURE }}
            </ion-button>
            <input
              type="file"
              id="recoveryFile"
              (change)="chooseRecoveryFile(purchaseOrder)"
            />
          </ion-col>
        </ion-row>
      </ion-grid>
  
      <br />
      <ion-grid *ngIf="purchaseOrder.showRecovery && purchaseOrder.recoveryList?.length">
        <ion-row>
          <ion-col>{{ LABELS.DATE }}</ion-col>
          <ion-col>{{ LABELS.AMOUNT }}</ion-col>
          <ion-col>{{ LABELS.DESCRIPTION }}</ion-col>
          <ion-col>{{ LABELS.ATTACHMENT }}</ion-col>
          <ion-col>{{ LABELS.ACTION }}</ion-col>
        </ion-row>
        <ion-row *ngFor="let recovery of purchaseOrder.recoveryList; let i = index">
          <ion-col>{{ recovery.date | date: 'dd/MM/yyyy' }}</ion-col>
          <ion-col>{{ recovery.amount }}</ion-col>
          <ion-col>{{ recovery.description }}</ion-col>
          <ion-col>
            <ion-button
              *ngIf="recovery.attachment?.base64Image"
              (click)="showAttachmentImage(recovery.attachment)"
            >
              {{ LABELS.VIEW_IMAGE }}
            </ion-button>
          </ion-col>
          <ion-col>
            <ion-button  id="update-recovery-button-{{ i }}" (click)="selectToUpdateRecovery(recovery, purchaseOrder)">
              <ion-icon name="create"></ion-icon>
            </ion-button>
            <ion-button id="delete-recovery-button-{{ i }}" (click)="deleteRecovery(i, recovery, purchaseOrder)">
              <ion-icon name="trash"></ion-icon>
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  
    <!-- Save Button -->
    <ion-button
    id="save-order-button"
      *ngIf="selectedTab === 'ORDER' && purchaseOrders?.length" expand="block" class="main_btn" (click)="saveVisitAndPo()">
      {{ LABELS.SAVE }}
    </ion-button>
  </div>
  </div>





<ion-content 
  [scrollY]="true" 
  *ngIf="selectedTab === 'POSITION'" 
  class="text-center"
>
  <div 
    [style.pointerEvents]="(lockAfterPeriod || lockAfterSync) ? 'none' : 'auto'" 
    style="width: 90%; margin: auto;"
  >

    <!-- top action buttons -->
    <ion-row class="ion-justify-content-center" style="margin-bottom: 8px;">
      <ion-col size="6">
        <ion-button
          expand="block"
          class="main_btn"
          style="min-height: 33px; line-height: 35px;"
          (click)="getCurrentPosition()"
        >
          {{ LABELS.UPDATE_MY_POSITION }}
        </ion-button>
      </ion-col>
      <ion-col size="6">
        <ion-button
        id="save-current-position-button"
          expand="block"
          class="main_btn"
          style="min-height: 33px; line-height: 35px;"
          (click)="saveCurrentPosition()"
        >
          {{ LABELS.SAVE_MY_LOCATION }}
        </ion-button>
      </ion-col>
      <ion-col size="6">
        <ion-button
          id="save-current-and-position-button"
          expand="block"
          class="main_btn"
          style="min-height: 33px; line-height: 35px;"
          (click)="saveCurrentandPosition()"
        >
          {{ LABELS.SAVE_LOCATION_AND_UPDATE_PROSPECT }}
        </ion-button>
      </ion-col>
    </ion-row>

    <!-- map toggle is handled elsewhere; when showMap=true we render this -->
    <ion-row *ngIf="googleMapIsAvailable">
      <ion-col>
        <!-- THIS is the container your renderMap() will target -->
        <div 
          #mapContainerBottom  
          style="width: 100%; height: 600px; border: 1px solid #ccc;"
        ></div>
      </ion-col>
    </ion-row>

  </div>
</ion-content>
</div>
</ion-content>
<!-- comments suggestion “bottom sheet” -->
<!-- Ligne ~3900 - Remplacer la div comment-suggestion-sheet existante -->
<!-- Product comment suggestions -->
<div class="comment-suggestion-sheet" *ngIf="showProductCommentSuggestion">
  <div class="suggestion-header">
  </div>
  <ion-scroll direction="x" class="comment-scroll">
    <div
      class="comment-pill"
      *ngFor="let suggestion of productCommentSuggestions"
      (mousedown)="chooseProductComment(suggestion)"
    >
      {{ suggestion }}
    </div>
  </ion-scroll>
</div>

<!-- General comment suggestions -->
<div class="comment-suggestion-sheet general-comments" *ngIf="showGeneralCommentSuggestion">
  <div class="suggestion-header">
  </div>
  <ion-scroll direction="x" class="comment-scroll">
    <div
      class="comment-pill"
      *ngFor="let suggestion of generalCommentSuggestions"
      (mousedown)="chooseGeneralComment(suggestion)"
    >
      {{ suggestion }}
    </div>
  </ion-scroll>
</div>
  




