<app-header [title]="translate('MARKETING ACTION')"></app-header>
<ion-card *ngIf="nextActionRules.length > 0">
  <ion-card-header>
    <ion-card-title>{{ translate('NEXT_ACTION_RULE') }}</ion-card-title>
  </ion-card-header>
  <ion-card-content>
    <div class="rules-container">
      <div *ngFor="let rule of nextActionRules" class="rule-block">
        <div class="rule-header">
          {{ translate('TOTAL_REVENUE') }}: <strong>{{ rule.totalRevenue }}</strong>,
          {{ translate('PERIOD') }}: <strong>{{ rule.period }}</strong>,
          {{ translate('ACTION') }}: <strong>{{ rule.action }}</strong>
        </div>

        <div *ngIf="rule.prospects && rule.prospects.length > 0">
          <ion-list>
            <ion-item *ngFor="let prospect of rule.prospects">
              {{ prospect.firstname }} {{ prospect.lastname }} ({{ prospect.total_revenue }})
            </ion-item>
          </ion-list>
        </div>

        <div *ngIf="!rule.prospects || rule.prospects.length === 0" class="no-prospects">
          {{ translate('NO_MATCHING_PROSPECTS') }}
        </div>
      </div>
    </div>
  </ion-card-content>

</ion-card>


<ion-content>
  <ion-button expand="block" (click)="displayAddForm()">{{translate('ADD_MARKETING_ACTION')}}</ion-button>

  <div *ngIf="displayForm">
    <form [formGroup]="marketingForm" (ngSubmit)="save()">
      <ion-item>
        <ion-label position="floating">{{translate('ACTION_NAME')}}</ion-label>
        <ion-input type="text" formControlName="marketingActionName" name="name" required></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="floating">{{translate('BUDGET')}}</ion-label>
        <ion-input type="number" formControlName="budget" name="budget" required></ion-input>
      </ion-item>

      <ion-item>
        <ion-label>{{translate('PRODUCT')}}</ion-label>
        <ion-select multiple="true" formControlName="productIds" name="productIds">
          <ion-select-option *ngFor="let product of products" [value]="product.id">{{ product.name }}</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item>
        <ion-label>{{translate('PROSPECT')}}</ion-label>
        <ion-select multiple="true" formControlName="prospectIds" name="prospectIds">
          <ion-select-option *ngFor="let prospect of prospects" [value]="prospect.id">{{ prospect.firstname }} {{ prospect.lastname }}</ion-select-option>
        </ion-select>
      </ion-item>

      <ion-item>
        <ion-label position="floating">{{translate('DATE')}}</ion-label>
        <ion-input type="date" formControlName="selectedDate" name="date" required></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="floating">{{translate('DESCRIPTION')}}</ion-label>
        <ion-input type="text" formControlName="description" name="description"></ion-input>
      </ion-item>
      <ion-button expand="block" type="submit">{{translate('SAVE')}}</ion-button>
      <ion-button expand="block" color="medium" (click)="cancel()">{{translate('CANCEL')}}</ion-button>
    </form>
    <div *ngIf="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
  </div>

  <ion-grid>
    <ion-row class="table-header" *ngIf="marketing_action.length > 0" >
      <ion-col size="2">{{translate('MARKETING_ACTION')}}</ion-col>
      <ion-col size="2">{{translate('DATE')}}</ion-col>
      <ion-col size="2">{{translate('BUDGET')}}</ion-col>
      <ion-col size="2">{{translate('DESCRIPTION')}}</ion-col>
      <ion-col size="2">{{translate('STATUS')}}</ion-col>
      <ion-col size="2">{{translate('ACTION')}}</ion-col>
    </ion-row>
    <ion-row *ngFor="let action of marketing_action">
      <ion-col size="2">{{ action.name }}</ion-col>
      <ion-col size="2">{{ action.marketingAction_date | date }}</ion-col>
      <ion-col size="2">{{ action.budget }}</ion-col>
      <ion-col size="2">{{ action.description }}</ion-col>
      <ion-col size="2">{{ action.status }}</ion-col>
      <ion-col size="2">
        <ion-button fill="clear" (click)="editAction(action)">
          ✏️
        </ion-button>
        <ion-button fill="clear" color="danger" (click)="deleteAction(action.id)">
          ❌
        </ion-button>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="marketing_action.length === 0">
      <ion-col size="12" class="ion-text-center">
        {{ translate('NO') }} {{ translate('MARKETING_ACTIONS') }}
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
