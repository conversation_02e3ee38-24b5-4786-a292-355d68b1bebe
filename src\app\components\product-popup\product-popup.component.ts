import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';

@Component({
  selector: 'app-product-popup',
  templateUrl: './product-popup.component.html',
  styleUrls: ['./product-popup.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule]
})
export class ProductPopupComponent implements OnInit {
  @Input() product: any;
  @Input() documents: any[] = [];
  
  currentSlide = 0;

  constructor(private modalCtrl: ModalController) {}

  ngOnInit() {
    // Debug pour voir les données reçues
    console.log('Product:', this.product);
    console.log('Documents:', this.documents);
    console.log('Documents length:', this.documents?.length);
  }

  close() {
    this.modalCtrl.dismiss();
  }

  saveTracking() {
    this.modalCtrl.dismiss({ saveTracking: true });
  }

  // Vérifier s'il y a des documents
  get hasDocuments(): boolean {
    return this.documents && Array.isArray(this.documents) && this.documents.length > 0;
  }

  // Vérifier s'il y a plusieurs documents
  get hasMultipleDocuments(): boolean {
    return this.hasDocuments && this.documents.length > 1;
  }

  // Navigation pour les documents
  nextSlide() {
    if (this.currentSlide < this.documents.length - 1) {
      this.currentSlide++;
    }
  }

  prevSlide() {
    if (this.currentSlide > 0) {
      this.currentSlide--;
    }
  }

  goToSlide(index: number) {
    if (index >= 0 && index < this.documents.length) {
      this.currentSlide = index;
    }
  }

  // Obtenir le document actuel
  get currentDocument() {
    return this.hasDocuments ? this.documents[this.currentSlide] : null;
  }
}