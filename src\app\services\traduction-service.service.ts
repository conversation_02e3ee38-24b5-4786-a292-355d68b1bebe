import { Injectable } from "@angular/core"
import  { HttpClient } from "@angular/common/http"
import {  Observable, BehaviorSubject } from "rxjs"
import { map, tap } from "rxjs/operators"

@Injectable({
  providedIn: "root",
})
export class TranslationService {
  private translations: any = {}

  // ✅ BehaviorSubject pour les traductions
  private translationsSubject = new BehaviorSubject<any>({})
  public translations$ = this.translationsSubject.asObservable()

  // ✅ BehaviorSubject pour la langue courante
  private currentLanguageSubject = new BehaviorSubject<string>("an.json")
  public currentLanguage$ = this.currentLanguageSubject.asObservable()

  constructor(private http: HttpClient) {
    // ✅ Charger la langue sauvegardée au démarrage du service
    this.initializeLanguage()
  }

  private async initializeLanguage() {
    const savedLanguage = localStorage.getItem("selectedLanguage") || "an.json"
    console.log("🚀 Service - Initialisation avec langue:", savedLanguage)
    await this.changeLanguage(savedLanguage)
  }

  loadTranslations(lang: string): Observable<any> {
    console.log("🔄 Service - Chargement des traductions:", `assets/langues/${lang}`)

    return this.http.get(`assets/langues/${lang}`).pipe(
      map((translations: any) => {
        console.log("✅ Service - Traductions reçues:", translations)
        this.translations = translations

        // ✅ Notifier tous les composants
        this.translationsSubject.next(translations)

        return translations
      }),
      tap({
        error: (error) => {
          console.error("❌ Service - Erreur chargement traductions:", error)
        },
      }),
    )
  }

  getTranslation(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ Service - Traduction manquante pour la clé: ${key}`)
    }
    return translation || key
  }

  translate(key: string): string {
    return this.getTranslation(key)
  }

  // ✅ Méthode centralisée pour changer la langue
  changeLanguage(lang: string): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log("🌐 Service - Changement langue vers:", lang)

      // ✅ Sauvegarder dans localStorage (une seule fois, centralisé)
      localStorage.setItem("selectedLanguage", lang)

      // ✅ Notifier tous les composants du changement de langue
      this.currentLanguageSubject.next(lang)

      this.loadTranslations(lang).subscribe({
        next: (translations) => {
          console.log("✅ Service - Langue changée vers:", lang)
          resolve(translations)
        },
        error: (error) => {
          console.error("❌ Service - Erreur changement langue:", error)
          reject(error)
        },
      })
    })
  }

  // ✅ Obtenir la langue courante
  getCurrentLanguage(): string {
    return this.currentLanguageSubject.value
  }

  // ✅ Obtenir la langue sauvegardée
  getSavedLanguage(): string {
    return localStorage.getItem("selectedLanguage") || "an.json"
  }

  // ✅ Obtenir les traductions actuelles
  getCurrentTranslations(): any {
    return this.translations
  }
}
