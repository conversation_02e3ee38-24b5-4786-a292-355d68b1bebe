import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { takeUntil } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { LoginService } from 'src/app/services/login-service.service';
import { ThemeService } from 'src/app/services/theme.service';
import { BudgetAllocation } from 'src/app/models/budget-allocations';
import { BudgetAllocationService } from 'src/app/services/budget-allocation.service';
import { HeaderComponent } from '../../header/header.component';
@Component({
  selector: 'app-budget-allocation',
  templateUrl: './budget-allocation.component.html',
  styleUrls: ['./budget-allocation.component.scss'],
  standalone: true, 
  imports: [IonicModule, FormsModule, CommonModule,HeaderComponent]
})
export class BudgetAllocationComponent implements OnInit {
  isDarkTheme = false;
  budgetAllocations: BudgetAllocation[] = [];
  destroy$ = new Subject<void>();
  private translationSubscription?: Subscription
  private languageSubscription?: Subscription
  currentLanguage = "an.json"
  translations: any = {};
  
  constructor(
    private themeService: ThemeService,
    private loginService: LoginService,
    private budgetAllocationService: BudgetAllocationService,
    private translationService: TranslationService
  ) {}
  
  ngOnInit() {
    this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
      console.log("🔄 budget - Traductions mises à jour:", translations)
      this.translations = translations
    })
  
    // ✅ S'abonner aux changements de langue
    this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
      console.log("🌐 budget - Langue changée vers:", language)
      this.currentLanguage = language
    })
    this.getAllBudgetAllocations();

    
  }

  getAllBudgetAllocations() {
    this.budgetAllocationService.getAllBudgetAllocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (budgetAllocations: BudgetAllocation[]) => this.budgetAllocations = budgetAllocations,
        error: (error: any) => console.error('Error fetching budget allocations:', error)
      });
  }
  
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ budget - Traduction manquante: ${key}`)
    }
    return translation || key
  }
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 budget - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ budget - Langue changée avec succès")
    } catch (error) {
      console.error("❌ budget - Erreur changement langue:", error)
    }
  }
  
  private loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;
      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  }
  
  logout() {
    this.loginService.logout();
  }
  
  onThemeChange(): void {
    this.themeService.switchTheme();
  }
}