import { APP_INITIALIZER, CUSTOM_ELEMENTS_SCHEMA, NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';

import { IonicModule, IonicRouteStrategy } from '@ionic/angular';

import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';

import { SQLiteService } from './services/sqlite.service';
import { InitializeAppService } from './services/initialize.app.service';
import { AuthorPostsService } from './services/author-posts.service';
import { DepartmentEmployeesService } from './services/department-employees.service';
import { DbnameVersionService } from './services/dbname-version.service';
import { NotificationService } from './services/notification.service';
import { BudgetAllocationService } from './services/budget-allocation.service';
import { MessageService } from './services/message.service';
import { ExpenseService } from './services/expense.service';
import { ProspectService } from './services/prospect-service.service';
import { UserService } from './services/user-service.service';
import { GoalsComponent } from './components/goals/goals.component';
import { TranslationService } from './services/traduction-service.service';
import { HttpClientModule } from '@angular/common/http';
import { IonicStorageModule } from '@ionic/storage-angular';
import { ActivityService } from './services/activity.service';
import { PlanningService } from './services/planning.service';
import { FilterComponent } from './components/filter/filter.component';
import { VisitService } from './services/visit.service';
import { ProspectDetailComponent } from './components/prospect-detail/prospect-detail.component';
import { ReportComponent } from './components/report/report.component';
import { MessageComponent } from './components/message/message.component';
import { ActivityCalenderComponent } from './components/activity-calender/activity-calender.component';
import { NotificationsComponent } from './components/notifications/notifications.component';
import { ProductPopupComponent } from './components/product-popup/product-popup.component';
import { NextActionRuleService } from './services/next-action-rule.service';
import { ConsoleComponent } from './components/console/console-component.component';

export function initializeFactory(init: InitializeAppService) {
  return () => init.initializeApp();
}

@NgModule({
  declarations: [
    AppComponent, // Declare components here
    
    
  ],
  imports: [
    BrowserModule,
    IonicModule.forRoot(),
    AppRoutingModule,
    HttpClientModule,
    ReportComponent,
    MessageComponent,
    FilterComponent,
    ProductPopupComponent,
    ProspectDetailComponent,
    NotificationsComponent,
    ActivityCalenderComponent,
    ConsoleComponent,
    IonicStorageModule.forRoot(),
  ],
  exports: [FilterComponent, ProductPopupComponent], // Export components for use in other modules
  providers: [
    SQLiteService,
    NotificationService,
    BudgetAllocationService,
    NextActionRuleService,
    MessageService,
    UserService,
    ExpenseService,
    InitializeAppService,
    AuthorPostsService,
    DepartmentEmployeesService,
    ActivityService,
    PlanningService,
    VisitService,
    DbnameVersionService,
    ProspectService,
    TranslationService,
    {
      provide: RouteReuseStrategy,
      useClass: IonicRouteStrategy,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: initializeFactory,
      deps: [InitializeAppService],
      multi: true,
    },
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
})
export class AppModule {}
