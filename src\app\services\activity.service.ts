import { Injectable } from '@angular/core';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { environment } from 'src/environments/environment';
import { SQLiteService } from './sqlite.service';
import { DbnameVersionService } from './dbname-version.service';
import { mockActivities, mockActivityType } from '../mock-data/activity';
import { Observable, Observer } from 'rxjs';
import { Activity, ActivityType } from '../models/activity';
import { TablesUpgrades } from '../upgrades/tables/tables';

@Injectable({
  providedIn: 'root'
})
export class ActivityService {
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;

  /* tout comme author post service  */
  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }

  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    
}

  
  async getActivity(jsonActivity: Activity): Promise <Activity> {
    let activity = await this.sqliteService.findOneBy(this.mDb, "activity", {id: jsonActivity.id});
    if (!activity) {
      if (jsonActivity.activityTypeId && jsonActivity.hourNumber && jsonActivity.activityDate){
        let activity = new Activity();
        activity.id = jsonActivity.id;
        activity.activityDate = jsonActivity.activityDate;
        activity.activityTypeId = jsonActivity.activityTypeId;
        activity.comment = jsonActivity.comment ;
        activity.status= jsonActivity.status ;
        activity.hourNumber=jsonActivity.hourNumber
        activity.synchronized= jsonActivity.synchronized
        activity=this.sqliteService.camelToSnake(activity)
        await this.sqliteService.save(this.mDb, "activity", activity);
        activity = await this.sqliteService.findOneBy(this.mDb, "activity", { id: jsonActivity.id });
        if (activity) {
          return activity;
        } else {
          return Promise.reject(`failed to getActivity for id ${jsonActivity.id}`);
        }
      } else {
        let activity = new Activity();
        activity.id = -1;
        return activity;
      }
    } else {
      return activity;
    } 
  }

  async getActivityType(jsonActivityType: ActivityType): Promise<ActivityType> {
    try {
      // Attempt to find the activity type in the database
      let type = await this.sqliteService.findOneBy(this.mDb, "activity_type", { id: jsonActivityType.id });
      
      if (!type) {
        // Check if the name exists in the JSON payload
        if (jsonActivityType.name) {
          let type = new ActivityType();
          type.id = jsonActivityType.id;
          type.name = jsonActivityType.name;
          
          try {
            // Save the new activity type in the database
            await this.sqliteService.save(this.mDb, "activity_type", type);
          } catch (error) {
            // Narrow the error type to handle it properly
            if (error instanceof Error) {
              console.error(`Error saving activity type with id ${jsonActivityType.id}:`, error.message);
              return Promise.reject(new Error(`Failed to save activity type with id ${jsonActivityType.id}. ${error.message}`));
            } else {
              console.error('Unknown error occurred while saving activity type.');
              return Promise.reject(new Error('Unknown error occurred while saving activity type.'));
            }
          }
  
          // Attempt to retrieve the newly saved type
          type = await this.sqliteService.findOneBy(this.mDb, "activity_type", { id: jsonActivityType.id });
          if (type) {
            return type;
          } else {
            // Log and reject if type was not found after save
            console.error(`Failed to retrieve saved activity type with id ${jsonActivityType.id}`);
            return Promise.reject(new Error(`Failed to retrieve activity type with id ${jsonActivityType.id} after saving.`));
          }
        } else {
          // Return a default activity type if no name is provided in the input
          let activityType = new ActivityType();
          activityType.id = -1; // Indicate an invalid type
          return activityType;
        }
      } else {
        // Return the found type if it already exists
        return type;
      }
    } catch (error) {
      // Narrow the error type to handle it properly
      if (error instanceof Error) {
        console.error(`Error retrieving activity type with id ${jsonActivityType.id}:`, error.message);
        return Promise.reject(new Error(`Error occurred while retrieving activity type with id ${jsonActivityType.id}. ${error.message}`));
      } else {
        console.error('Unknown error occurred while retrieving activity type.');
        return Promise.reject(new Error('Unknown error occurred while retrieving activity type.'));
      }
    }
  }
  

  getAllActivityTypes(): Observable<ActivityType[]> {
    const selectQuery = 'SELECT * FROM activity_type;';
      return new Observable((observer: Observer<ActivityType[]>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const activityType = result.values as ActivityType[];
          observer.next(activityType);
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }
  getAllActivities(): Observable<Activity[]> {
    const selectQuery = 'SELECT * FROM activity;';
    return new Observable((observer: Observer<Activity[]>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const values = this.sqliteService.snakeToCamel(result.values)
          const activities = values as Activity[];
          observer.next(activities);
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }
  deleteActivity(id: number): Observable<void> {
    return new Observable<void>((observer) => {
      // Start the delete process with basic error handling
      this.sqliteService.remove(this.mDb, 'activity', { id: id })
        .then(() => {
          return this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        })
        .then(() => {
          // Successfully deleted and saved to store
          observer.next();
          observer.complete();
        })
        .catch(error => {
          // Log the error with more detail
          console.error(`Error occurred while deleting activity with id ${id}:`, error);
  
          // Provide a more meaningful error message to the observer
          const errorMessage = `Failed to delete activity with id ${id}. Error: ${error.message || error}`;
          observer.error(new Error(errorMessage));
        });
    });
  }
   
  async updateActivity(activity: any): Promise<Activity> {
    activity=this.sqliteService.camelToSnake(activity)
    await this.sqliteService.save(this.mDb, 'activity', activity, { id: activity.id });
    this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    const result = await this.sqliteService.findOneBy(this.mDb, 'activity', { id: activity.id });
    if (result) {
      return result;
    } else {
      return Promise.reject(`failed to updateactivity for id ${activity.id}`);
    }
  }
  // filre 
 
  async getActivityByDay(currentDate: Date) {
    const selectQuery = `
SELECT * FROM activity WHERE (activity_date = ?) AND (status<>'DELETED')  ORDER BY activity_date `;
    const res = await this.mDb.query(selectQuery,[currentDate.setHours(0,0,0,0)]);
    const values = this.sqliteService.snakeToCamel(res.values)
    return values || [];
  }
}
