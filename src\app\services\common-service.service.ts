import { Inject, Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { BehaviorSubject } from 'rxjs';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { TablesUpgrades } from 'src/app/upgrades/tables/tables';
import { environment } from 'src/environments/environment';
import { DbnameVersionService } from './dbname-version.service';
import { DateService } from 'src/app/services/date.service';
import { CODES } from '../constants';



@Injectable({
  providedIn: 'root'
})
export class CommonService {
 
  private currentUserSubject = new BehaviorSubject<any>(null);
  currentUser$ = this.currentUserSubject.asObservable();
  public commentsDictionary: string[] = [];
  private openReportPeriod: number = 0;
  private openExpensePeriod: number = 0;
  logger: any;
  private versionUpgrades =TablesUpgrades;
  private dataBase!: SQLiteDBConnection;
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;

  constructor(private sqliteService: SQLiteService, @Inject(CODES) private CODES: any,   private dbVerService: DbnameVersionService,    private dateService: DateService,

  ) {
    this.databaseName = environment.databaseNames.find(x => x.name.includes('tables'))?.name || 'default';

  }
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;

}
 /* async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService
      .addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
    // create and/or open the database
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);  
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, true, "secret", this.loadToVersion, false);
    } else {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, false, "no-encryption", this.loadToVersion, false);
    }
  }
  */

  async getCurrentUser(this: any)
{
  try {
    const rs = await this.mDb.query("SELECT * FROM user ")
    if (rs && rs.values && rs.values.length > 0) {
      const currentUser = rs.values[0]
      this.currentUserSubject.next(currentUser)

      // CORRECTION: Parser le JSON pour accéder directement aux propriétés
      if (currentUser.comments_dictionary && currentUser.comments_dictionary !== "") {
        try {
          // Parser le JSON
          const parsedComments = JSON.parse(currentUser.comments_dictionary)

          // Remplacer la chaîne par l'objet JSON parsé
          currentUser.comments_dictionary = parsedComments

          // Maintenant vous pouvez accéder directement aux propriétés
          console.log("dddddicccc", currentUser.comments_dictionary.medical)
          console.log("pharmaceutical:", currentUser.comments_dictionary.pharmaceutical)

          // Pour la compatibilité avec l'ancien système, garder aussi le tableau
          this.commentsDictionary = []
        } catch (e) {
          // Fallback vers l'ancien format si ce n'est pas du JSON valide
          console.log("⚠️ Not valid JSON, using old format")
          this.commentsDictionary = currentUser.comments_dictionary.split("\n")
        }
      }

      this.openReportPeriod = currentUser.open_report_period
      this.openExpensePeriod = currentUser.open_expense_period
      return currentUser;
    }
  } catch (error: any) {
    console.error("Error getting current user: ", error.message)
  }
}


  checkOpenPeriod(selectedDate: Date, type: string): boolean {
    let openPeriod: number = 0;

    if (['REPORT', 'PLANNING', 'ACTIVITY'].includes(type)) {
      openPeriod = this.openReportPeriod;
    } else if (type === 'EXPENSE') {
      openPeriod = this.openExpensePeriod;
    }

    if (openPeriod !== 0) {
      const currentDate = new Date();
      selectedDate.setHours(0, 0, 0, 0);
      const period = Math.floor((currentDate.getTime() - selectedDate.getTime()) / (1000 * 60 * 60 * 24));

      if ((type === 'REPORT' && openPeriod > 0 && (period >= openPeriod || period < 0))
        || (openPeriod > 0 && period >= openPeriod)) {
        return true;
      }
    }
    return false;
  }

  async deleteRecord(tableName: string, columnName: string, column: any, condition?: string) {
    try {
      const rs = await this.mDb.query(`SELECT synchronized FROM ${tableName} WHERE ${columnName} = ?`, [column]);

      let deleteQuery: string;
      if (rs && rs.values && rs.values[0].synchronized === 0) {
        deleteQuery = `DELETE FROM ${tableName} WHERE ${columnName} = ?`;
      } else {
        deleteQuery = `UPDATE ${tableName} SET status = 'DELETED' WHERE ${columnName} = ?`;
      }
      if (condition) {
        deleteQuery += condition;
      }

      const result = await this.mDb.run(deleteQuery, [column]);

      if (result.changes !== undefined ) {
        console.info(`Record is deleted with success from ${tableName}`);
      } else {
        console.info(`Record was not deleted from table: ${tableName}`);
      }
    } catch (error: any) {
      console.error('Error deleting record: ', error.message);
    }
  }


  async deletePo(index: number, purchaseOrder: any) {
    try {
      if (purchaseOrder.purchaseOrderId !== 0) {
        await this.deleteRecord('purchase_order', 'id', purchaseOrder.purchaseOrderId);
        
        if (index !== null) {
          purchaseOrder.splice(index, 1);  // Assuming purchaseOrders is a class member or an input
        }

        const rs = await this.mDb.query('SELECT * FROM recovery WHERE purchase_order_id = ?', [purchaseOrder.purchaseOrderId]);
        if (rs && rs.values && rs.values.length > 0) {
          for (let i = 0; i < rs.values.length; i++) {
            await this.deleteRecord('recovery', 'id', rs.values[i].id);
            const res = await this.mDb.query('SELECT * FROM attachment a INNER JOIN recovery r ON r.attachment_id = a.id WHERE r.id = ?', [rs.values[i].id]);
            if (res.values && res.values.length > 0) {
              await this.deleteRecord('attachment', 'id', res.values[0].id);
            }
          }
          purchaseOrder.recoveryList = [];
        }
      }
    } catch (error: any) {
      this.logger.error('Error deleting purchase order: ', error.message);
    }
  }

  // Other methods...

  async getFilterQuery(
    sector: any, locality: any, establishment: any, speciality: any, potential: any,
    activity: any, product: any, potentialProduct: any, prospectName: string, dateSelected: Date,
    order: any, onlyPlanifiedProspects: boolean, onlyNonVistedProspects: string,
    onlyNonPlannedProspects: string, limit: boolean = true
  ) {
    let baseQuery = `SELECT p.*, s.name as speciality, pt.id as prospectTypeId, s.action FROM prospect p `
      + `INNER JOIN speciality s ON (p.speciality_id = s.id) `
      + `INNER JOIN prospect_type pt ON (p.type_id = pt.id) `
      + `INNER JOIN sector sec ON (sec.id = p.sector_id) `
      + `INNER JOIN locality l ON (l.id = p.locality_id) `
      + `LEFT OUTER JOIN establishment e ON (e.id = p.establishment_id) `;

    let whereClause = "";

    if (onlyPlanifiedProspects) {
      baseQuery += "INNER JOIN planning n ON p.id = n.prospect_id ";
    }

    if (sector && sector.id) {
      whereClause += "p.sector_id = ?";
    }
    if (speciality && speciality.id) {
      whereClause = this.appendAndIfRequired(whereClause);
      whereClause += "p.speciality_id = ?";
    }
    if (potential && potential.id) {
      whereClause = this.appendAndIfRequired(whereClause);
      whereClause += "p.potential = ?";
    }
    if (activity && activity.id) {
      whereClause = this.appendAndIfRequired(whereClause);
      whereClause += "p.activity = ?";
    }
    if (locality && locality.id) {
      whereClause = this.appendAndIfRequired(whereClause);
      whereClause += "p.locality_id = ?";
    }
    if (establishment && establishment.id) {
      whereClause = this.appendAndIfRequired(whereClause);
      whereClause += "p.establishment_id = ?";
    }
    if (product && potentialProduct) {
      whereClause = this.appendAndIfRequired(whereClause);
      whereClause += "p.id IN (SELECT DISTINCT pp.prospect_id FROM potential_product pp WHERE pp.product_id = ? AND pp.potential_id = ?)";
    }
    if (prospectName) {
      whereClause = this.appendAndIfRequired(whereClause);
      whereClause += "((firstname || ' ' || lastname LIKE ?) OR (lastname || ' ' || firstname LIKE ?) OR firstname LIKE ? OR lastname LIKE ? OR s.name LIKE ? OR sec.name LIKE ? OR l.name LIKE ? OR e.name LIKE ?)";
    }
    if (dateSelected) {
      dateSelected.setHours(0, 0, 0, 0);
      const days = this.dateService.getAllDaysOfWeek(dateSelected);
      const year = dateSelected.getFullYear();
      const month = dateSelected.getMonth() + 1;
      const firstDateOfMonth = new Date(dateSelected.getFullYear(), dateSelected.getMonth(), 1);
      const lastDateOfMonth = new Date(dateSelected.getFullYear(), dateSelected.getMonth() + 1, 0);
      const weekEndDate = this.dateService.getSundayDate(dateSelected);

      if (onlyNonPlannedProspects === "WEEK") {
        whereClause = this.appendAndIfRequired(whereClause);
        whereClause += `p.id NOT IN (SELECT DISTINCT pl.prospect_id FROM planning pl WHERE pl.status <> 'DELETED' AND pl.planning_date >= ${dateSelected.getTime()} AND pl.planning_date <= ${weekEndDate.getTime()})`;
      }
      if (onlyNonVistedProspects === "MONTH") {
        whereClause = this.appendAndIfRequired(whereClause);
        whereClause += `p.id NOT IN (SELECT prospect_id FROM visit WHERE status <> 'DELETED' AND visit_date >= ${firstDateOfMonth.getTime()} AND visit_date <= ${lastDateOfMonth.getTime()})`;
      } else if (onlyNonVistedProspects === "WEEK") {
        whereClause = this.appendAndIfRequired(whereClause);
        whereClause += `p.id NOT IN (SELECT prospect_id FROM visit WHERE status <> 'DELETED' AND visit_date >= ${dateSelected.getTime()} AND visit_date <= ${weekEndDate.getTime()})`;
      }
      if (onlyPlanifiedProspects) {
        const date = new Date();
        const planningClause = `n.planning_date IN (${date.setHours(0, 0, 0, 0)}) AND n.status <> 'DELETED'`;
        whereClause = this.appendAndIfRequired(whereClause);
        whereClause += planningClause;
      }
    }

    whereClause = this.appendAndIfRequired(whereClause);
    whereClause += "p.status <> 'DELETED' AND p.status <> 'NOT_AFFECTED'";

    let orderByClause = " ORDER BY firstname, lastname";

    if (order) {
      if (order.value === 'SPECIALITY_ORDER') {
        orderByClause = " ORDER BY speciality_id";
      }
      if (order.value === 'NAME_ORDER') {
        orderByClause = " ORDER BY firstname, lastname";
      }
      if (order.value === 'SECTOR_ORDER') {
        orderByClause = " ORDER BY sector_id";
      }
    }

    let query = baseQuery + " WHERE " + whereClause + orderByClause;
    if (limit) {
      query += " LIMIT 300";
    }

    return query;
  }

  appendAndIfRequired(whereClause: string): string {
    if (whereClause) {
      whereClause += " AND ";
    }
    return whereClause;
  }

  getFilterQueryParams(
    sector: any, locality: any, establishment: any, speciality: any,
    potential: any, activity: any, keywords: string, product: any, potentialProduct: any
  ): any[] {
    const params = [];
    if (sector && sector.id) {
      params.push(Number(sector.id));
    }
    if (speciality && speciality.id) {
      params.push(Number(speciality.id));
    }
    if (potential && potential.id) {
      params.push(Number(potential.id));
    }
    if (activity && activity.id) {
      params.push((activity.id));
    }
    if (locality && locality.id) {
      params.push(Number(locality.id));
    }
    if (establishment && establishment.id) {
      params.push(Number(establishment.id));
    }
    if (product && potentialProduct) {
      params.push(Number(product.id));
      params.push(Number(potentialProduct.id));
    }
    if (keywords) {
      params.push(`%${keywords}%`);
      params.push(`%${keywords}%`);
      params.push(`%${keywords}%`);
      params.push(`%${keywords}%`);
      params.push(`%${keywords}%`);
      params.push(`%${keywords}%`);
      params.push(`%${keywords}%`);
      params.push(`%${keywords}%`);
    }
    return params;
  }

  getAllPotential() {
    return [
      { id: 'A', value: 'A' },
      { id: 'B', value: 'B' },
    ];
  }

  getAllActivities() {
    return [
      { id: 'P', value: 'P' },
      { id: 'H', value: 'H' },
    ];
  }

  getAllOrders() {
    return [
      { id: 1, value: 'SPECIALITY_ORDER' },
      { id: 2, value: 'NAME_ORDER' },
      { id: 3, value: 'SECTOR_ORDER' },
    ];
  }

  convertStatusToString(status: string): string {
    switch (status) {
      case 'ACCEPTED':
        return 'Accepté';
      case 'WAITING_FOR_VALIDATION':
        return 'En attente de validation';
      case 'REFUSED':
        return 'Refusé';
      case 'NEW':
        return 'Nouveau';
      case 'SENT':
        return 'Envoyé';
      default:
        return status;
    }
  }

  /*checkNumbersIsUnique(visitsOfDaySelected: any[], purchaseOrders: any[], selectedTab: string): string {
    let result = '';
    let oneRankExist = false;
    let oneCommentExist = false;
    let wholesalerExist = false;
    let oneOrderExist = false;

    for (const visit of visitsOfDaySelected) {
      if (visit.comment.length > 0) {
        oneCommentExist = true;
      }
      if (visit.rank !== undefined) {
        oneRankExist = true;
      }
    }
    for (const order of purchaseOrders) {
      oneOrderExist = false;
      wholesalerExist = false;
      if (order.selectedWholesaler != null) {
        wholesalerExist = true;
      }
    }
    return result;
  }*/


  checkNumbersIsUnique(visitsOfDaySelected: any[], purchaseOrders: any[], selectedTab: string): string {
    let result = ""
    let oneRankExist = false
    let wholesalerExist = false
    let oneOrderExist = false

    // Vérifier les rangs dans les visites (garder cette logique)
    for (const visit of visitsOfDaySelected) {
      if (visit.rank !== undefined) {
        oneRankExist = true
      }
    }

    // Vérifier les commandes et grossistes dans les bons de commande
    for (let i = 0; i < purchaseOrders.length; i++) {
      oneOrderExist = false
      wholesalerExist = false
      if (purchaseOrders[i].selectedWholesaler !== null) {
        wholesalerExist = true
      }
      for (let j = 0; j < purchaseOrders[i].visitProducts.length; j++) {
        if (purchaseOrders[i].visitProducts[j].order_quantity !== undefined) {
          oneOrderExist = true
        }
      }
      if (!wholesalerExist) {
        break
      }
      if (!oneOrderExist) {
        break
      }
    }

    // Vérifier les conditions selon l'onglet sélectionné
    if (selectedTab === "VISIT") {
      if (!oneRankExist) {
        result = this.CODES.RANKS_IS_EMPTY
      } else {
        // ✅ SUPPRIMER l'ancienne validation des commentaires ici
        // La validation des commentaires est maintenant faite dans validateCommentsAccordingToJSON()
        result = this.CODES.RANKS_IS_UNIQUE
      }
    } else if (selectedTab === "ORDER") {
      if (!oneOrderExist) {
        result = this.CODES.QUANTITY_IS_REQUIRED
      } else if (!wholesalerExist) {
        result = this.CODES.WHOLESALER_IS_REQUIRED
      } else {
        result = this.CODES.QUANTITY_AND_WHOLESALER_EXISTS
      }
    }

    return result
  }
}