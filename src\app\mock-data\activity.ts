import { Activity, ActivityType } from "../models/activity";

export const mockActivities: Activity[] = [
    {
      id: 1,
      activityDate: new Date(2024,7,15).getTime(),
      hourNumber: 8,
      activityTypeId: 1,
      comment: 'Completed task A',
      synchronized: 0,
      status: 'pending'
    },
    {
      id: 2,
      activityDate: new Date(2024,7,15).getTime() , 
      hourNumber: 4,
      activityTypeId: 2,
      comment: 'Worked on project B',
      synchronized: 1,
      status: 'completed'
    },
    {
      id: 3,
      activityDate: new Date(2024,7,16).getTime() , 
      hourNumber: 6,
      activityTypeId: 3,
      comment: 'Meeting with team',
      synchronized: 0,
      status: 'in progress'
    },
    {
      id: 4,
      activityDate: new Date(2024,7,14).getTime() ,
      hourNumber: 7,
      activityTypeId: 4,
      comment: 'Reviewed documentation',
      synchronized: 1,
      status: 'pending'
    },
    {
      id: 5,
      activityDate: new Date(2024,8,15).getTime() ,
      hourNumber: 5,
      activityTypeId: 5,
      comment: 'Updated software',
      synchronized: 0,
      status: 'completed'
    }
  ];

  export const mockActivityType: ActivityType[] = [
    {
      id: 1,
      name: 'Development'
    },
    {
      id: 2,
      name: 'Research'
    },
    {
      id: 3,
      name: 'Meeting'
    },
    {
      id: 4,
      name: 'Documentation'
    },
    {
      id: 5,
      name: 'Maintenance'
    }
  ];
  