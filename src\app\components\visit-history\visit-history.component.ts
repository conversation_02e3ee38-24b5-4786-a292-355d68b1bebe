import { Component, OnInit } from '@angular/core';
import { <PERSON>ading<PERSON>ontroller, AlertController } from '@ionic/angular';
import { VisitHistoryService } from '../../services/visit-history-service.service';
import { DateService } from '../../services/date.service';
import { ChartService } from '../../services/chart-service.service';
import { TranslationService } from 'src/app/services/traduction-service.service'; // Import for translation
import { LoginService } from 'src/app/services/login-service.service'; // Import for login
import { ThemeService } from 'src/app/services/theme.service'; // Import for theme
import { IonicModule, ModalController } from '@ionic/angular';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SyncService } from '../../services/sync.service'; // Replace with the actual path to your sync service
import { UserService } from 'src/app/services/user-service.service';
import { HeaderComponent } from '../../header/header.component';

@Component({
  selector: 'app-visit-history',
  templateUrl: './visit-history.component.html',
  styleUrls: ['./visit-history.component.scss'],
  standalone: true,
  imports: [HeaderComponent,IonicModule, FormsModule, CommonModule, FormsModule, ReactiveFormsModule]
})
export class VisitHistoryComponent implements OnInit {
deleteVisitProduct(_t145: any,arg1: unknown, prospectName: any) {
throw new Error('Method not implemented.');
}
  public pageTitle: string = 'Visit History';
  public visitsHistory: Record<string, any> = {};
  public modifyDate: boolean = false;  // Add this property to resolve the error
  public keywordsFilter: string = '';
  public showVisits: boolean = true;
  public showOrders: boolean = true;
  public showCharts: boolean = false; // Initialize the showCharts property
  public selectedWholesaler: any = { id: null, name: 'All' };
  public wholesalers: any[] = [];
  public revenueBySpecialities: any[] = []; // Initialize revenueBySpecialities
  public startDate!: Date;
  public endDate: Date = new Date();
  public prospectSelectedId!: number;
  public selectedProspectId: number | null = null;
  public recapData : any;
  public selectedKpi: any = {};
  public translations: any = {}; // Translations object for translation service
  public kpiOptions = [
    { value: 'VISIT', label: 'Number of Visits' },
    { value: 'ORDER', label: 'Number of Orders' },
    { value: 'REVENUE', label: 'Revenue' },
    { value: 'SAMPLE_QUANTITY', label: 'Sample Quantity' }
  ];
  public currentUser: any;
  public selectedType!: null;
  public prospects: any[] = []; // Initialize prospects list
  public noVisits: boolean = true; // Initialize noVisits
openReportPeriod: any;
LABELS: any;
historyIn: any;
userId: any;

  constructor(
    private visitHistoryService: VisitHistoryService,
    private loadingCtrl: LoadingController,
    private alertCtrl: AlertController,
    private dateService: DateService,
    private chartService: ChartService,
    private translationService: TranslationService, // Inject for translation
    private loginService: LoginService, // Inject for login
    private themeService: ThemeService, // Inject for theme
    private syncService: SyncService,
    private userService: UserService
  ) {}

  async ngOnInit() {
    this.startDate = this.dateService.getMondayOfWeek();
    this.selectedKpi = { current: this.kpiOptions[0] };  // Initialize selectedKpi correctly
    await this.getAllWholesalers();
    await this.getAllProspects();
    await this.loadData();
    this.loadTranslations('an.json'); // Load default language translations
    this.visitHistoryService.testDatabaseConnection()
        .then(() => {
            console.log('Database connection is active.');
        })
        .catch((error) => {
            console.error('Error testing database connection:', error);
        });
  }


  
// Add the changeVisitDate method
async changeVisitDate(visitDate: any, visitId: number) {
  const alert = await this.alertCtrl.create({
    header: 'Change Visit Date',
    inputs: [
      {
        name: 'newDate',
        type: 'date',
        // Cast visitDate to a string to resolve the error
        value: new Date(visitDate as string).toISOString().substring(0, 10),
      },
    ],
    buttons: [
      {
        text: 'Cancel',
        role: 'cancel',
      },
      {
        text: 'OK',
        handler: async (data) => {
          const newDate = new Date(data.newDate);
          try {
            await this.visitHistoryService.updateVisitDate(visitId, newDate);
            this.loadData();  // Reload the data to reflect the change
          } catch (error) {
            console.error('Error updating visit date:', error);
          }
        },
      },
    ],
  });
  await alert.present();
}

  getObjectKeys(obj: any): string[] {
    return Object.keys(obj);
  }
  async loadData() {
    const loading = await this.loadingCtrl.create({
      message: 'Loading data...',
    });
    await loading.present();
    try {
      await this.getAllProspects();
      this.recapData = await this.visitHistoryService.getRecapTableData(this.selectedWholesaler,this.showOrders,this.showVisits, this.startDate, this.endDate, this.userService.getUserId() );

      this.visitsHistory = await this.visitHistoryService.getVisitsHistory(
        this.prospectSelectedId,
        this.startDate,
        this.endDate
      );
      this.noVisits = Object.keys(this.visitsHistory).length === 0;
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      await loading.dismiss();
    }
  }

  // Synchronize data
  synchronize(): void {
    if (this.prospectSelectedId !== null) {
      console.log('Synchronizing visit history for prospect ID:', this.prospectSelectedId);

      this.syncService.synchronizeVisitHistory(this.prospectSelectedId)
        .then((response: any) => { // Provide an appropriate type for 'response' if possible
          console.log('Synchronization successful', response);
        })
        .catch((error: any) => { // Provide an appropriate type for 'error' if possible
          console.error('Synchronization failed', error);
        });
    } else {
      console.error('No prospect selected for synchronization');
    }
  }

  // Translation Methods
  translate(key: string): string {
    return this.translations[key] || key;
  }

  changeLanguage(lang: string) {
    this.loadTranslations(lang);
  }

  private loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;
      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  }

  // Theme Methods
  switchTheme(): void {
    this.themeService.switchTheme();
  }

  onThemeChange(): void {
    this.themeService.switchTheme();
  }

  // Login Methods
  logout() {
    this.loginService.logout();
  }

  async getAllWholesalers() {
    const loading = await this.loadingCtrl.create({
      message: 'Loading wholesalers...',
    });
    await loading.present();
    try {
      this.wholesalers = await this.visitHistoryService.getWholesalers();
    } catch (error) {
      console.error('Error loading wholesalers:', error);
    } finally {
      await loading.dismiss();
    }
  }

  async displayCharts() {
    const start_date_timestamp = this.startDate.getTime();
    const end_date_timestamp = this.endDate.getTime();
    const selectedKpi = this.selectedKpi.current;
    const groupBy = 'SPECIALITY';
    const objectif = true;
    const selectedType = this.selectedType || null;
    const showOrders = this.showOrders;
    const showVisits = this.showVisits;

    try {
      await this.chartService.drawHistoryChart(
        'chartDivId',
        start_date_timestamp,
        end_date_timestamp,
        await this.userService.getUserId(),
        selectedKpi,
        groupBy,
        objectif,
        selectedType,
        showOrders,
        showVisits
      );
    } catch (error) {
      console.error('Error displaying charts:', error);
    }
  }

  // Date Picker Methods
  async openStartDatePicker() {
    const alert = await this.alertCtrl.create({
      header: 'Select Start Date',
      inputs: [
        {
          name: 'startDate',
          type: 'date',
          value: this.startDate.toISOString().substring(0, 10),
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
        },
        {
          text: 'OK',
          handler: (data) => {
            this.startDate = new Date(data.startDate);
            this.loadData();
          },
        },
      ],
    });
    await alert.present();
  }

  async openEndDatePicker() {
    const alert = await this.alertCtrl.create({
      header: 'Select End Date',
      inputs: [
        {
          name: 'endDate',
          type: 'date',
          value: this.endDate.toISOString().substring(0, 10),
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
        },
        {
          text: 'OK',
          handler: (data) => {
            this.endDate = new Date(data.endDate);
            this.loadData();
          },
        },
      ],
    });
    await alert.present();
  }

  toggleVisits() {
    this.showVisits = !this.showVisits;
    this.loadData();
  }

  toggleOrders() {
    this.showOrders = !this.showOrders;
    this.loadData();
  }

  // Add method to fetch prospects based on filter
  async getAllProspects() {
    try {
     
      this.prospects = await this.visitHistoryService.getAllProspects(this.keywordsFilter, this.selectedWholesaler, this.showVisits, this.showOrders, this.startDate, this.endDate, await this.userService.getUserId());
   
    } catch (error) {
      console.error('Error fetching prospects:', error);
    }
  }

  // Add method to handle selected prospect
  setSelectedProspect(prospectId: number) {
    this.selectedProspectId = prospectId;
    this.prospectSelectedId = prospectId;
    this.loadData();
  }

  // Access dynamically generated properties safely with bracket notation
  getCountVisit() {
    return this.recapData.countVisit || 0;
  }

  getOrders() {
    return this.recapData.orders || 0;
  }

  getSamples() {
    return this.recapData.samples || 0;
  }
  
}
