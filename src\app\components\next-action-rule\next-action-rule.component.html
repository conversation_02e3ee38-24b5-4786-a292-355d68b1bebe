<app-header [title]="translate('NEXT_ACTION_RULE')"></app-header>


<ion-content [class.dark-theme]="isDarkTheme">
  <div style="width: 70%; margin: auto;">
    <ion-grid>

      <ion-row style="background-color: #007bff; color: white; text-align: center; font-weight: bold;">
        <ion-col size="2">{{ translate('TOTAL_REVENUE') }}</ion-col>
        <ion-col size="5">{{ translate('PERIOD') }}</ion-col>
        <ion-col size="5">{{ translate('ACTION') }}</ion-col>
      </ion-row>


      <ion-row *ngFor="let action of nextActionRules; let i = index">
        <ion-col size="2" [ngStyle]="{
          'text-align': 'center',
          'background-color': i % 2 === 0 ? '#f4f5f8' : '#d9e0f5'
        }">
          <span style="color:black">{{ action.totalRevenue }}</span>
        </ion-col>
        <ion-col size="5" [ngStyle]="{
          'text-align': 'center',
          'background-color': i % 2 === 0 ? '#f4f5f8' : '#d9e0f5'
        }">
          <span style="color:black">{{ action.period  }}</span>
        </ion-col>
        <ion-col size="5" [ngStyle]="{
          'text-align': 'center',
          'background-color': i % 2 === 0 ? '#f4f5f8' : '#d9e0f5'
        }">
          <span style="color:black">{{ action.action }}</span>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
</ion-content>
