<app-header [title]="translate('PLANNING')"></app-header>

<ion-content>
  <form [formGroup]="planningForm" class="log_form" name="planning">
    <ion-row>
      <ion-col>
        <ion-label>{{ LABELS.WEEK_OF }} : {{ dateSelected | date: 'dd/MM/yyyy' }}</ion-label>
        <ion-item class="selectedDay" lines="none" detail (click)="openDatePicker()">
          
        </ion-item>
      </ion-col>
    </ion-row>

    <div *ngIf="status.length > 0">
      <!-- Status items -->
      <ion-item *ngIf="status === 'NEW'" class="status new">
        <ion-label>{{ LABELS.STATUS }}: {{ LABELS.NEW }}</ion-label>
      </ion-item>
      <ion-item *ngIf="status === 'WAITING_FOR_VALIDATION'" class="status waiting">
        <ion-label>{{ LABELS.STATUS }}: {{ LABELS.WAITING_FOR_VALIDATION }}</ion-label>
      </ion-item>
      <ion-item *ngIf="status === 'ACCEPTED'" class="status accepted">
        <ion-label>{{ LABELS.STATUS }}: {{ LABELS.ACCEPTED }}</ion-label>
      </ion-item>
      <ion-item *ngIf="status === 'TO_BE_REVIEWED'" class="status review">
        <ion-label>{{ LABELS.STATUS }}: {{ LABELS.TO_BE_REVIEWED }}</ion-label>
      </ion-item>
      <ion-item *ngIf="status === 'REFUSED'" class="status refused">
        <ion-label>{{ LABELS.STATUS }}: {{ LABELS.REFUSED }}</ion-label>
      </ion-item>

    </div>

    <div *ngIf="lockAfterSync && status !== 'TO_BE_REVIEWED'" class="warning">{{ LABELS.PLANNING_IS_SENT }}</div>
    <div *ngIf="lockAfterPeriod" class="warning">{{ LABELS.CHECK_PERIOD }} {{ LABELS.PLANNING }} {{ openReportPeriod }} {{ LABELS.DAY }} {{ LABELS.AFTER_CURRENT_DATE }}</div>

    <ion-grid>
      <!-- Table Header -->
      <ion-row class="table-header">
        <ion-col *ngFor="let date of dates; let i = index" [ngClass]="{'selectedDayHeader' : i === selectedRow}">
          {{ LABELS.DAYS[i] }} <br> {{ date | date: 'dd/MMM' }}
        </ion-col>
      </ion-row>

      <!-- Table Body - Displaying Prospects -->
      <ion-row class="table-body">
        <ion-col *ngFor="let date of dates; let dayIndex = index">
          <ion-list>
            <ion-item *ngFor="let item of plannedVisits[dayIndex]; let itemIndex = index">
              <ion-label (click)="openProspectDetails(item.prospectId)" class="prospect-link">
                - {{ item.prospectName }}
              </ion-label>
              <ion-button *ngIf="status === 'NEW' || status === 'TO_BE_REVIEWED' || status === 'WAITING_FOR_VALIDATION'"
                          (click)="clickShowConfDeleted(dayIndex, itemIndex, item)">
                <ion-icon name="trash"></ion-icon>
              </ion-button>
            </ion-item>
          </ion-list>
          <ion-button *ngIf="status.length === 0 || status === 'NEW' || status === 'TO_BE_REVIEWED' || status === 'WAITING_FOR_VALIDATION' || status === 'DELETED'"
                      (click)="showProspectsForPlanning(dayIndex,showRecommendedProspects, sectorSelected, localitySelected, establishmentSelected, specialitySelected,
                      potentialSelected, activitySelected, productSelected, potentialProductSelected, showOnlyNonVistedProspects
            )">
            <ion-icon slot="icon-only" name="add-circle"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>

      <!-- Add All Prospects Button -->
      <ion-row class="table-body" *ngIf="status.length === 0 || status === 'NEW' || status === 'TO_BE_REVIEWED' || status === 'WAITING_FOR_VALIDATION' || status === 'DELETED'">
        <ion-col [attr.colspan]="dates.length" class="add-all-prospect-button">
          <ion-button (click)="showProspectsForPlanning(-1 , showRecommendedProspects, sectorSelected, localitySelected, establishmentSelected, specialitySelected,
          potentialSelected, activitySelected, productSelected, potentialProductSelected, showOnlyNonVistedProspects)" fill="clear">
            <ion-icon slot="icon-only" name="add-circle"></ion-icon>
          </ion-button>
        </ion-col>
      </ion-row>

      <!-- Action Buttons -->
      <ion-row class="action-button" *ngIf="isPlanningFilled">
        <ion-col [attr.colspan]="dates.length" class="center-content">
          <ion-button *ngIf="status.length === 0 || status === 'NEW' || status === 'TO_BE_REVIEWED' || status === 'WAITING_FOR_VALIDATION'" 
                      (click)="deleteAll()" 
                      [disabled]="lockAfterPeriod || (lockAfterSync && status !== 'TO_BE_REVIEWED')" 
                      fill="outline" 
                      color="danger">
            {{ LABELS.DELETE_ALL }}
          </ion-button>
          <ion-button (click)="openDupplicationDatePicker()">{{ LABELS.DUPLICATE }}</ion-button>
          <ion-button *ngIf="!showCharts" (click)="showchart()">{{ LABELS.SHOW_CHARTS }}</ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>

    <div class="notes" *ngIf="notes">
      <ion-item class="note-item">
        <ion-label>Commentaire: {{ notes }}</ion-label>
      </ion-item>
    </div>
    
    <div *ngIf="displayAddForm ">
      <div  >
        <ion-row>
          <ion-col>
            <ion-item class="recommended-prospects-item">
              <ion-toggle labelPlacement="end" formControlName="showRecommendedProspects" (ionChange)="showProspectsForPlanning(selectedRow, showRecommendedProspects, sectorSelected, localitySelected, establishmentSelected, specialitySelected, potentialSelected, activitySelected, productSelected, potentialProductSelected, showOnlyNonVistedProspects)" color="secondary"></ion-toggle>
              <ion-label style="font-size: large; margin-right: 30px;"> <strong>{{LABELS.SHOW_RECOMMENDED_PROSPECTS}}</strong></ion-label>
            </ion-item>
          </ion-col>
        </ion-row>
      </div>

      <div *ngIf="  showRecommendedProspects">
        <ion-row>
          <ion-col>
            <ion-item>
              <ion-select formControlName="productSelected" (ionChange)="getRecommendedProspects(productSelected)" placeholder="PRODUCT">
                <ion-select-option *ngFor="let product of products" [value]="product">{{product.name}}</ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>
      </div>

      <div *ngIf=" !showRecommendedProspects">
        <ion-row>
          <ion-col>
            <ion-item class="only-prospects-visited-item">
              <ion-toggle labelPlacement="end"  formControlName="showOnlyNonVistedProspects" (ionChange)="getNormalSearchProspects(sectorSelected, localitySelected, establishmentSelected, specialitySelected, potentialSelected, activitySelected, productSelected, potentialProductSelected, showOnlyNonVistedProspects)" color="secondary"></ion-toggle>
              <ion-label style="font-size: large; margin-right: 30px;"> <strong>{{LABELS.SHOW_ONLY_PROSPECT_VISITED}}</strong></ion-label>
            </ion-item>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col>
            <ion-item>
              <ion-select
              formControlName="sectorSelected"
                (ionChange)="onSectorChange($event)"
                placeholder="SECTOR"
              >
                <ion-select-option *ngFor="let sector of sectors" [value]="sector">
                  {{ sector.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
          
          <ion-col>
            <ion-item>
              <ion-select
              formControlName="localitySelected"
                (ionChange)="onLocalityChange($event)"
                placeholder="LOCALITY"
              >
                <ion-select-option *ngFor="let locality of localities" [value]="locality">
                  {{ locality.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
          
          <ion-col>
            <ion-item>
              <ion-select
              formControlName="specialitySelected"
                (ionChange)="onSpecialityChange($event)"
                placeholder="SPECIALITY"
              >
                <ion-select-option *ngFor="let speciality of specialities" [value]="speciality">
                  {{ speciality.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
          
        </ion-row>

        <ion-row>
          <ion-col>
            <ion-item>
              <ion-select
              formControlName="potentialSelected"
                (ionChange)="onPotentialChange($event)"
                placeholder="POTENTIAL"
              >
                <ion-select-option *ngFor="let potential of potentials" [value]="potential">
                  {{ potential.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
          

          <ion-col>
            <ion-item>
              <ion-select
              formControlName="activitySelected"
                (ionChange)="onActivityChange($event)"
                placeholder="ACTIVITY"
              >
                <ion-select-option *ngFor="let activity of activities" [value]="activity">
                  {{ activity.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
          

          <ion-col>
            <ion-item>
              <ion-select
              formControlName="establishmentSelected"
                (ionChange)="onEstablishmentChange($event)"
                placeholder="ESTABLISHMENT"
              >
                <ion-select-option *ngFor="let establishment of establishments" [value]="establishment">
                  {{ establishment.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
          
        </ion-row>

        <ion-row>
          <ion-col>
            <ion-item>
              <ion-select
              formControlName="productSelected"
                (ionChange)="onProductChange($event)"
                placeholder="PRODUCT"
              >
                <ion-select-option *ngFor="let product of products" [value]="product">
                  {{ product.name }}
                </ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
          

          <ion-col *ngIf="isEnabled" >
            <ion-item>
              <ion-select formControlName="potentialProductSelected" (ionChange)="getNormalSearchProspects(sectorSelected, localitySelected, establishmentSelected, specialitySelected, potentialSelected, activitySelected, productSelected, potentialProductSelected, showOnlyNonVistedProspects)"  placeholder="Potentiel produit">
                <ion-select-option *ngFor="let potential of potentials" [value]="potential">{{potential.name}}</ion-select-option>
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>
 
      </div>
      
      
    
      <ion-row>
        <ion-col>
          <ion-list>
            <ion-item>
              <ion-select aria-label="prospect" placeholder="Select prospect" [multiple]="true"  formControlName="selectedProspect" (ionChange)="onProspectChange($event)">
                <ion-select-option *ngFor="let prospect of prospects" [value]="prospect">
                  {{prospect.firstname}}{{prospect.lastname}}
                </ion-select-option>
              </ion-select>
             
            </ion-item>
          </ion-list>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col>
          <ion-button expand="block" (click)="save()">ADD</ion-button>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col>
          <ion-button expand="block" (click)="cancel()">CANCEL</ion-button>
        </ion-col>
      </ion-row>
     
  </div> 
</form>
  
   
      <div [hidden]="!showCharts">
        <ion-grid>
          <ion-row>
            <ion-col size="6" class="card">
              <ion-card>
                <ion-card-header>
                  <ion-card-title>Visites Planifiées / Portefeuille</ion-card-title>
                </ion-card-header>
                <ion-card-content>
                  <canvas id="planningCoverageChart"></canvas>
                </ion-card-content>
              </ion-card>
            </ion-col>
    
            <ion-col size="6" class="card">
              <ion-card>
                <ion-card-header>
                  <ion-card-title>Planifiés par activité</ion-card-title>
                </ion-card-header>
                <ion-card-content>
                  <canvas id="planningByActivityChart"></canvas>
                </ion-card-content>
              </ion-card>
            </ion-col>
          </ion-row>
    
          <ion-row>
            <ion-col size="6" class="card">
              <ion-card>
                <ion-card-header>
                  <ion-card-title>Planifiés par secteur</ion-card-title>
                </ion-card-header>
                <ion-card-content>
                  <canvas id="planningBySectorChart"></canvas>
                </ion-card-content>
              </ion-card>
            </ion-col>
    
            <ion-col size="6" class="card">
              <ion-card>
                <ion-card-header>
                  <ion-card-title>Planifiés par potentiel</ion-card-title>
                </ion-card-header>
                <ion-card-content>
                  <canvas id="planningByPotentialChart"></canvas>
                </ion-card-content>
              </ion-card>
            </ion-col>
          </ion-row>
    
          <ion-row>
            <ion-col size="12" class="card">
              <ion-card>
                <ion-card-header>
                  <ion-card-title>Planifiés par spécialité</ion-card-title>
                </ion-card-header>
                <ion-card-content>
                  <canvas id="planningBySpecialityChart"></canvas>
                </ion-card-content>
              </ion-card>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>
       
 
</ion-content>
