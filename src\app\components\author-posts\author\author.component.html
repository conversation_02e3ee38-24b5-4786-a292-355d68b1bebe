<ion-list lines="full">

  <form [formGroup]="authorForm" (ngSubmit)="onSubmit()">
    <ion-item>
      <ion-input id="author-cmp-name" type="text" label="name" formControlName="name" required></ion-input>
    </ion-item>
    <ion-item>
      <ion-input id="author-cmp-email" type="text" label="email" formControlName="email" required></ion-input>
    </ion-item>
    <ion-item>
      <ion-input id="author-cmp-birthday" type="text" label="birthday" formControlName="birthday"></ion-input>
    </ion-item>
    <!-- add version 2-->
    <ion-item>
      <ion-input id="author-cmp-company" type="text" label= "company" formControlName="company"></ion-input>
    </ion-item>
    <ion-row>
      <ion-col>
        <ion-button type="submit" color="primary" shape="full" expand="block" [disabled]="!authorForm.valid">
          Submit
        </ion-button>
      </ion-col>
    </ion-row>
</form>
  </ion-list>
