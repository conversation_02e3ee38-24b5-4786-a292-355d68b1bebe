import {  Component, OnInit } from '@angular/core';
import {  SQLiteDBConnection } from '@capacitor-community/sqlite';
import { KpiOption, TypeOption } from 'src/app/models/goal';
import { mockKpiOptions } from 'src/app/mock-data/goal';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ChartService } from 'src/app/services/chart-service.service';
import { Chart, registerables } from 'chart.js';
import { DbCreationTablesService } from 'src/app/services/db-creation-tables.service';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { ThemeService } from 'src/app/services/theme.service';
import { environment } from 'src/environments/environment';
import { UserService } from 'src/app/services/user-service.service';
import { LoginService } from 'src/app/services/login-service.service';
import { HeaderComponent } from '../../header/header.component';
import { Subject, Subscription } from 'rxjs';

Chart.register(...registerables);

@Component({
  selector: 'app-goals',
  templateUrl: './goals.component.html',
 
  standalone: true,
  imports: [IonicModule, FormsModule, CommonModule,HeaderComponent]
})
export class GoalsComponent implements OnInit {
  smiley: string = '';
  sumGoals: number = 0;
  sumCount: number = 0;
  selectedKpi: { current: KpiOption } = { current: {} as KpiOption };
  selectedType: { type: TypeOption } = { type: {} as TypeOption };
  startDate: Date = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  endDate: Date = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);
  kpiOptions: KpiOption[] = mockKpiOptions;
  types: TypeOption[] = [];
  totalLabel: string = 'REVENUE';
  destroy$ = new Subject<void>();
  private translationSubscription?: Subscription
  private languageSubscription?: Subscription
  currentLanguage = "an.json"
  translations: any = {};
  isDarkTheme = false;
  
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  constructor(
    private chartService: ChartService,
    private dbCreationTablesService: DbCreationTablesService,
    private translationService: TranslationService,
    private themeService: ThemeService,
    private userService: UserService,
    private LoginService: LoginService,
   

   
    
  ) {  
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }

  async ngOnInit() {
    this.mDb =  this.dbCreationTablesService.getDatabase();
    console.log('Database before init:', this.mDb); 
    this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
      console.log("🔄 ActivityCalender - Traductions mises à jour:", translations)
      this.translations = translations
    })
  
    // ✅ S'abonner aux changements de langue
    this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
      console.log("🌐 ActivityCalender - Langue changée vers:", language)
      this.currentLanguage = language
    })
this.types = await this.getAllTypes();
    this.selectedKpi.current = this.kpiOptions[0];
    this.selectedType.type = this.types[0];
    this.drawCharts();
   // Default language

  }
  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
    this.destroy$.next()
    this.destroy$.complete()
  }

 

  

  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ goals - Traduction manquante: ${key}`)
    }
    return translation || key
  }
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 goals - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ goals - Langue changée avec succès")
    } catch (error) {
      console.error("❌ goals - Erreur changement langue:", error)
    }
  }

  private loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;
        
      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  }

  onKpiChange() {
    this.updateTotalLabelAndValue();
    this.drawCharts();
  }
  async getAllTypes(): Promise<TypeOption[]> {
    try {
      const query = 'SELECT * FROM prospect_type ORDER BY name';
      const res = await this.mDb.query(query);
  
      let types: TypeOption[] = [];
  
      if (res.values) {
        types = res.values.map((row: any) => ({
          id: row.id,
          name: row.name,
        }));
      }
  
      types.unshift({
        id: undefined,
        name: 'All',
      });
  
      return types;
    } catch (err: any) {
      console.error('Error in retrieving types: ', err.message || err);
      return []; // Return an empty array in case of an error
    }
  }
  

  updateTotalLabelAndValue() {
    switch (this.selectedKpi.current.value) {
      case 'ORDER':
        this.totalLabel = this.translate('NUMBER_OF_ORDERS');
        this.selectedKpi.current = this.kpiOptions[2];
        break;
      case 'VISIT':
        this.totalLabel = this.translate('NUMBER_OF_VISITS');
        this.selectedKpi.current = this.kpiOptions[1];
        break;
      default:
        this.totalLabel = this.translate('REVENUE');
        this.selectedKpi.current = this.kpiOptions[0];
    }
    
    console.log("kpi",this.selectedKpi.current)
  }

  synchronize() {
    // Implémenter la logique de synchronisation si nécessaire
  }

  async drawCharts() {
    if (typeof this.startDate === 'string') {
      this.startDate = new Date(this.startDate);
  }
  if (typeof this.endDate === 'string') {
      this.endDate = new Date(this.endDate);
  }
  const start_date_timestamp = new Date(this.startDate).getTime();
  const end_date_timestamp = new Date(this.endDate).getTime();
  const userId=this.userService.getUserId();  
  console.log("user : ",userId);
    await this.chartService.drawVisitedVsAllProspectChart('visitsByPortfolioChart', start_date_timestamp, end_date_timestamp, userId, true, true);
    
    await this.chartService.drawHistoryChart('visitsByActivityChart', start_date_timestamp, end_date_timestamp, userId, this.selectedKpi.current, "ACTIVITY", true);
    await this.chartService.drawHistoryChart('visitsBySpecialityChart', start_date_timestamp, end_date_timestamp, userId, this.selectedKpi.current, "SPECIALITY", true);
    await this.chartService.drawHistoryChart('visitsByProspectTypeChart', start_date_timestamp, end_date_timestamp, userId, this.selectedKpi.current, "PROSPECTTYPE", true);
    await this.chartService.drawHistoryChart('visitsByPotentialChart', start_date_timestamp, end_date_timestamp, userId, this.selectedKpi.current, "POTENTIAL", true);
    await this.chartService.drawHistoryChart('visitsBySectorChart', start_date_timestamp, end_date_timestamp, userId, this.selectedKpi.current, "SECTOR", true);
    await this.chartService.drawHistoryChart('visitsByProductChart', start_date_timestamp, end_date_timestamp, userId, this.selectedKpi.current, "PRODUCT", true);

    const goalQuery = `
      SELECT SUM(value) as goalValue FROM goal_item gi 
      INNER JOIN goal g ON g.id = gi.goal_id 
      WHERE g.type = ? AND g.first_date <= ? AND g.last_date >= ? 
    `;

    let countQuery = `
      SELECT COUNT(DISTINCT v.prospect_id) as countVisit, 
             SUM(prd.buying_price * vp.order_quantity) as revenue,
             SUM(vp.order_quantity) as countOrder 
      FROM visit v 
      INNER JOIN visit_product vp ON v.id = vp.visit_id 
      INNER JOIN product prd ON prd.id = vp.product_id 
      INNER JOIN prospect pros ON v.prospect_id = pros.id 
      INNER JOIN prospect_type pt ON pt.id = pros.type_id 
      WHERE v.status <> 'DELETED' 
        AND v.visit_date >= ? 
        AND v.visit_date <= ?  
        AND v.user_id = ?
    `;

    if (this.selectedType.type.id != undefined) {
      countQuery += ` AND pt.id = ${this.selectedType.type.id}`;
    }

    try {
      console.log("user_id",userId);
      const res = await this.mDb.query(countQuery, [start_date_timestamp, end_date_timestamp, userId]);
      console.log("final query ",countQuery);
      console.log('res :',res);
      if (res.values) {
        switch (this.selectedKpi.current.value) {
          case 'VISIT':
            this.sumCount = res.values[0].countVisit;

            break;
          case 'ORDER':
            this.sumCount = res.values[0].countOrder;
            break;
          case 'REVENUE':
            this.sumCount = res.values[0].revenue;
            break;
        }

        const goalRes = await this.mDb.query(goalQuery, [this.selectedKpi.current.value, start_date_timestamp, end_date_timestamp]);
        console.log('goalRes :',this.selectedKpi.current.value);
        console.log('goalRes :',goalRes);
        if (goalRes.values) {
          this.sumGoals = goalRes.values[0].goalValue;
        }
      }

      if (this.sumGoals === 0) {
        this.smiley = '';
      } else {
        const ratio = this.sumCount / this.sumGoals;
        this.smiley = 'confusion.png';
        if (ratio >= 0.6 && ratio < 0.8) {
          this.smiley = 'sourire.png';
        } else if (ratio >= 0.8 && ratio < 1) {
          this.smiley = 'heureux.png';
        } else if (ratio >= 1) {
          this.smiley = 'fete.png';
        }
        document.getElementById('smiley_div')!.innerHTML = `<img style="height:5%;width:5%;" src="assets/obj_smiley/${this.smiley}" id="smiley"/>`;
      }
    } catch (err: any) {
      console.error('Error in query execution: ', err.message || err);
    }
    
  }

  getCardTitle(baseTitle: string): string {
    return `${this.translate(this.selectedKpi.current.label)} ${this.translate(baseTitle)}`;
  }
  switchTheme(): void {
    this.themeService.switchTheme();
  }
  
  


  onStartDateChange(event: any) {
    this.startDate = new Date(event.detail.value);
    this.drawCharts();
  }

  onEndDateChange(event: any) {
    this.endDate = new Date(event.detail.value);
    this.drawCharts();
  }
  logout() {
    this.LoginService.logout();
  }
  onThemeChange(): void {
    this.themeService.switchTheme();
  }
  
}