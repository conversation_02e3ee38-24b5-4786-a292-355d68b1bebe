export interface AuthResponse {
    userId: number;
    firstLastName: string;
    workType: string;
    workingDays: number;
    syncCycle: number;
    autoSync: boolean;
    lockAfterSync: boolean;
    multiWholesaler: boolean;
    openExpensePeriod: number | null;
    openReportPeriod: number | null;
    commentsDictionary: string;
    token: string;
    // Optional fields
    lastSynchronisation?: string;
    lastReceiveDate?: number;
    firstSync?: boolean;
    time?: string;
    // Remove username and password
    sqlQueriesToExecute?: string;
  forceSendingLog?: boolean;
  }