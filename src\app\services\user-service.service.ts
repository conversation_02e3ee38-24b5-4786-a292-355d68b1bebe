import { Injectable } from '@angular/core';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { Observable, from } from 'rxjs';
import { SQLiteService } from './sqlite.service';
import { UserVersionUpgrades } from '../upgrades/user/user';
import { DbnameVersionService } from './dbname-version.service';
import { DateService } from './date.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private mDb!: SQLiteDBConnection;
  public databaseName: string ;

  userId!: number;
  userConfig: any;

  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
    private dateservice: DateService
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
  }
  /*async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService
      .addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
    // create and/or open the database
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);
    const isData = await this.mDb.query("select * from sqlite_sequence");
    // create database initial data
    if (isData.values!.length === 0) {
      await this.createInitialData();
    }
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, true, "secret",
          this.loadToVersion, false);

    } else {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, false, "no-encryption",
          this.loadToVersion, false);
    }
  }
  private async createInitialData(): Promise<void> {
    // create 
    for (const ID of [this.userId]) {
      await this.getUserId();
    }
  }*/
  setUserId(_userId: number): Observable<any> {
    this.userId = _userId;
    if (!this.mDb) {
      throw new Error('Database connection is not initialized.');
    }
    return from(this.mDb.query('SELECT * FROM user WHERE user_id = ?', [_userId]).then(result => {
      // Vérifiez si 'result.values' est défini avant d'accéder à sa longueur
      if (result.values && result.values.length > 0) {
        this.userConfig = result.values[0];
        return this.userConfig;
      } else {
        
      }
    }));
  }

  getUserId(): number  {
    return this.userId;
  }

  getUserConfiguration(): any {
    return this.userConfig;
  }
}