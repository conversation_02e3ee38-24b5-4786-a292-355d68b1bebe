<div *ngIf="syncState.isReceiving || syncState.isSending" class="sync-status-bar">
  <ion-progress-bar type="indeterminate" color="primary"></ion-progress-bar>
  <div class="sync-message">
    <ion-icon name="sync-outline" class="spinning"></ion-icon>
    <span>{{ syncState.message }}</span>
  </div>
</div>
<app-header 
  [title]="translate('HOME')" 
  [class.syncing]="syncState.isReceiving || syncState.isSending">
</app-header>

<ion-content >
  <div class="dashboard-grid">
    
    <ion-card [routerLink]="['/report']">
      <ion-card-header>
        <ion-card-title>{{translate('RAPPORT')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/report_v5.png" alt="Rapport"/>
        <p>0 {{translate('VISIT_MADE')}}</p>
      </ion-card-content>
    </ion-card>
    <ion-card [routerLink]="['/planning']">
      <ion-card-header>
        <ion-card-title>{{translate('PLANNING_')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/planning_v5.png" alt="Planifications"/>
        <p>0 {{translate('REALIZED')}} / 20 {{translate('PLANNED')}}</p>
      </ion-card-content>
    </ion-card>
    <ion-card [routerLink]="['/visit-history']">
      <ion-card-header>
        <ion-card-title>{{translate('VISIT_HISTORY')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/historic_v5.png" alt="Historique"/>
      </ion-card-content>
    </ion-card>
    <ion-card [routerLink]="['/calendar']">
      <ion-card-header>
        <ion-card-title>{{translate('CALENDAR')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/calendar2.png" alt="Calendrier"/>
      </ion-card-content>
    </ion-card>
    <ion-card [routerLink]="['/goals']">
      <ion-card-header>
        <ion-card-title>{{translate('GOAL')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/goal6.png" alt="Objectif"/>
      </ion-card-content>
    </ion-card>
    <ion-card [routerLink]="['/expense-list']">
      <ion-card-header>
        <ion-card-title>{{translate('EXPENSE_REPORTS')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/expenses.png" alt="Notes de frais"/>
      </ion-card-content>
    </ion-card>
    <ion-card [routerLink]="['/action-marketing']">
      <ion-card-header>
        <ion-card-title>{{translate('MARKETING ACTION')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/promotion.png" alt="Marketing"/>
      </ion-card-content>
    </ion-card>
    <ion-card [routerLink]="['/message']">
      <ion-card-header>
        <ion-card-title>{{translate('MESSAGE')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/message.png" alt="Message"/>
      </ion-card-content>
    </ion-card>
    <ion-card (click)="send()">
      <ion-card-header>
        <ion-card-title>{{translate('SEND_DATA')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/send_v5.png" alt="send_donnees"/>
      </ion-card-content>
    </ion-card>
<ion-card (click)="receive()" >
  <ion-card-header>
    <ion-card-title>{{translate('UPDATE')}}</ion-card-title>
  </ion-card-header>
  <ion-card-content>
    <img src="assets/synchronize.png" alt="maj_donnees"/>
  </ion-card-content>
</ion-card>



    <!-- 
    <ion-card (click)="receive()" >
      <ion-card-header>
        <ion-card-title>{{translate('UPDATE')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/synchronize.png" alt="maj_donnees"/>
      </ion-card-content>
    </ion-card>
    -->
    
    <ion-card [routerLink]="['/notifications']">
      <ion-card-header>
        <ion-card-title>{{translate('NOTIFICATION')}}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <img src="assets/notification_v5.png" alt="Notification"/>
      </ion-card-content>
    </ion-card>

  <ion-card [routerLink]="['/budget-allocation']">
    <ion-card-header>
      <ion-card-title>{{translate('BUDGET_ALLOCATION')}}</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <img src="assets/allocation.png" alt="Budget Allocation"/>
    </ion-card-content>
  </ion-card>

  <ion-card [routerLink]="['/next-action-rule']">
    <ion-card-header>
      <ion-card-title>{{translate('NEXT_ACTION_RULE')}}</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <img src="assets/rule.png" alt="Next action rule"/>
    </ion-card-content>
  </ion-card>
  <ion-card [routerLink]="['/CHAT_BOT']">
    <ion-card-header>
      <ion-card-title>{{translate('CHAT_BOT')}}</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <img src="assets/chat-bot.png" alt="Chat-bot"/>
    </ion-card-content>
  </ion-card>
  <ion-card [routerLink]="['/formation']">
    <ion-card-header>
      <ion-card-title>{{translate('FORMATION')}}</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <img src="assets/formation.png" alt="Next action rule"/>
    </ion-card-content>
  </ion-card>
</div>

 

 

