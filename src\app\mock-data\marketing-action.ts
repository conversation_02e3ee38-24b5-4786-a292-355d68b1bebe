import { marketingAction } from "../models/marketing-action";

export  const mockMarketingActions : marketingAction[]=[
    {
        id: 1,
        name: "Email Campaign",
        budget: 5000,
        product_id: "prod_001",
        prospect_id: "pros_001",
        marketingAction_date:  new Date(2024,8,15).getTime(),
        synchronized: 0,
        status: "planned",
        description: "Initial email campaign targeting new customers."
      },
      {
        id: 2,
        name: "Social Media Ads",
        budget: 12000,
        product_id: "prod_002",
        prospect_id: "pros_002",
        marketingAction_date:  new Date(2024,8,15).getTime(), 
        synchronized: 1,
        status: "in_progress",
        description: "Targeted ads on Facebook and Instagram."
      },
      {
        id: 3,
        name: "Webinar",
        budget: 3000,
        product_id: "prod_003",
        prospect_id: "pros_003",
        marketingAction_date:  new Date(2024,7,15).getTime(), 
        synchronized: 1,
        status: "completed",
        description: "Product demo webinar for prospective customers."
      },
      {
        id: 4,
        name: "Direct Mail",
        budget: 8000,
        product_id: "prod_004",
        prospect_id: "pros_004",
        marketingAction_date:  new Date(2024,7,16).getTime(), 
        synchronized: 0,
        status: "planned",
        description: "Sending out direct mail to local prospects."
      }
    
]