// Header and Toolbar
ion-header {
    ion-toolbar {
      background-color: #2c68d3; // Couleur de fond similaire à celle de l'image
      color: white; // Texte en noir
  
      .flag-icon {
        width: 20px;
        height: 20px;
        margin: 0 5px;
      }
  
      ion-button {
        --background: none;
        --background-focused: none;
        --background-hover: none;
        --background-activated: none;
        --color: black; // Texte en noir
  
        ion-icon {
          margin-right: 5px;
        }
  
        ion-label {
          margin: 0 5px;
        }
      }
    }
  }
  
  // Content Styling
  .trouble-shooting-container {
    ion-header ion-toolbar {
      background-color: #2c68d3;
      color: white; // Texte en noir
  
      ion-title {
        text-align: center;
      }
    }
  
    .request-input {
      margin: 20px 0;
  
      ion-label {
        font-size: 1.2rem;
        color: black; // Texte en noir
      }
  
      ion-textarea {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        color: black; // Texte en noir
      }
    }
  
    .actions {
      display: flex;
      justify-content: space-around;
      margin: 20px 0;
  
      ion-button {
        flex: 1;
        margin: 0 5px;
        background-color: #2c68d3;
        color: white; // Texte en noir
  
        &.expanded {
          width: 100%;
        }
      }
    }
  
    ion-item {
      --background: none;
      margin: 20px 0;
  
      ion-label {
        font-size: 1.2rem;
        color: black; // Texte en noir
      }
    }
  
    .result, .console {
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 10px;
      margin: 20px 0;
      color: black; // Texte en noir
  
      h2 {
        font-size: 1.4rem;
        margin-bottom: 10px;
      }
  
      pre {
        background-color: #eee;
        padding: 10px;
        border-radius: 5px;
      }
    }
  }
  