<app-header [title]="translate('VISIT HISTORY')"></app-header>

<ion-content class="doc_page main_bg" scroll="true">

  <ion-grid>
    <ion-row>
      <ion-col>
        <ion-label (click)="openStartDatePicker()" style="font-size: large; text-align: center;">
          {{ translate('START_DATE') }}: {{ startDate | date: 'dd/MM/yyyy' }}
        </ion-label>
      </ion-col>

      <ion-col>
        <ion-label (click)="openEndDatePicker()" style="font-size: large; text-align: center;">
          {{ translate('END_DATE') }}: {{ endDate | date: 'dd/MM/yyyy' }}
        </ion-label>
      </ion-col>
    </ion-row>
  </ion-grid>

  <ion-grid>
    <ion-row>
      <ion-col *ngIf="getCountVisit() != 0">
        <ion-label>{{ translate('NUMBER_OF_VISITS') }}</ion-label>
      </ion-col>
      <ion-col *ngIf="getCountVisit() != 0">
        <ion-label>{{ translate('NUMBER_OF_ORDERS') }}</ion-label>
      </ion-col>
      <ion-col *ngIf="getCountVisit() != 0">
        <ion-label>{{ translate('NUMBER_OF_SAMPLE') }}</ion-label>
      </ion-col>
    </ion-row>

    <ion-row *ngIf="getCountVisit() != 0">
      <ion-col>{{ getCountVisit() }}</ion-col>
      <ion-col>{{ getOrders() }}</ion-col>
      <ion-col>{{ getSamples() }}</ion-col>
    </ion-row>
  </ion-grid>

  <ion-grid *ngIf="getCountVisit() != 0 && showOrders">
    <ion-row>
      <ion-col>{{ translate('SPECIALITY') }}</ion-col>
      <ion-col>{{ translate('WHOLESALER_REVENUE') }}</ion-col>
      <ion-col>{{ translate('PHARMACY_REVENUE') }}</ion-col>
    </ion-row>

    <ion-row *ngFor="let revenueBySpeciality of recapData.revenueBySpecialities">
      <ion-col>{{ revenueBySpeciality.specialityName }}</ion-col>
      <ion-col>{{ revenueBySpeciality.buyingPrice.toFixed(3) }}</ion-col>
      <ion-col>{{ revenueBySpeciality.sellingPrice.toFixed(3) }}</ion-col>
    </ion-row>
  </ion-grid>

  <ion-grid>
    <ion-row>
      <ion-col size="4">
        <ion-toggle [(ngModel)]="showVisits" (ionChange)="toggleVisits()"></ion-toggle>
        <ion-label style="font-size: large; margin-right: 30px;">
          <strong>{{ translate('SHOW_VISITS') }}</strong>
        </ion-label>
      </ion-col>

      <ion-col size="4">
        <ion-toggle [(ngModel)]="showOrders" (ionChange)="toggleOrders()"></ion-toggle>
        <ion-label style="font-size: large; margin-right: 30px;">
          <strong>{{ translate('SHOW_ORDERS') }}</strong>
        </ion-label>
      </ion-col>

      <ion-col size="4">
        <ion-toggle [(ngModel)]="showCharts" (ionChange)="displayCharts()"></ion-toggle>
        <ion-label style="font-size: large; margin-right: 30px;">
          <strong>{{ translate('SHOW_CHART') }}</strong>
        </ion-label>
      </ion-col>
    </ion-row>

    <ion-row *ngIf="showCharts">
      <ion-col size="6">
        <ion-select [(ngModel)]="selectedKpi.current" (ionChange)="displayCharts()">
          <ion-select-option *ngFor="let kpiOption of kpiOptions" [value]="kpiOption.value">
            {{ kpiOption.label }}
          </ion-select-option>
        </ion-select>
      </ion-col>
    </ion-row>
  </ion-grid>

  <!-- Charts Section -->
  <ion-grid *ngIf="showCharts">
    <ion-row>
      <ion-col size="6">
        <ion-card>
          <ion-card-header>{{ translate('VISITS_MADE_BY_PORTFOLIO') }}</ion-card-header>
          <ion-card-content>
            <canvas id="prospectPortfolioCoverageChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card>
          <ion-card-header>{{ selectedKpi.current.label }} {{ translate('BY_ACTIVITY') }}</ion-card-header>
          <ion-card-content>
            <canvas id="activityChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card>
          <ion-card-header>{{ selectedKpi.current.label }} {{ translate('BY_SPECIALITY') }}</ion-card-header>
          <ion-card-content>
            <canvas id="specialityVisitsChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card>
          <ion-card-header>{{ selectedKpi.current.label }} {{ translate('BY_POTENTIAL') }}</ion-card-header>
          <ion-card-content>
            <canvas id="potentialChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card>
          <ion-card-header>{{ selectedKpi.current.label }} {{ translate('BY_PRODUCT') }}</ion-card-header>
          <ion-card-content>
            <canvas id="productChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card>
          <ion-card-header>{{ selectedKpi.current.label }} {{ translate('BY_SECTOR') }}</ion-card-header>
          <ion-card-content>
            <canvas id="sectorChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>

  <!-- Filters -->
  <ion-grid>
    <ion-row>
      <ion-col size="6">
        <ion-input type="text" placeholder="{{ translate('GLOBAL_FILTER') }}" [(ngModel)]="keywordsFilter"
          (ionChange)="getAllProspects()">
        </ion-input>
      </ion-col>

      <ion-col size="6" *ngIf="showOrders && !showVisits">
        <ion-select [(ngModel)]="selectedWholesaler" (ionChange)="getAllProspects()">
          <ion-select-option *ngFor="let wholesaler of wholesalers" [value]="wholesaler">
            {{ wholesaler.name }}
          </ion-select-option>
        </ion-select>
      </ion-col>
    </ion-row>
  </ion-grid>

  <!-- Prospects List -->
  <ion-content scrollX="true">
    <ion-grid>
      <ion-row style="background-color: white;">
        <ion-col *ngFor="let item of prospects" size="2">
          <ion-card 
            style="height: 150px; width: 150px; text-align: center; padding: 10px;" 
            (click)="setSelectedProspect(item.id)"
            [ngClass]="{'selected-prospect': item.id === selectedProspectId}"
          >
            <img src="assets/doctor_v5.png" style="width: 60px; height: 60px; margin-bottom: 10px;" />
            <ion-card-header>
              <ion-card-title style="font-size: small;">{{ item.name }}</ion-card-title>
              <ion-card-subtitle style="font-size: x-small;">{{ item.speciality }}</ion-card-subtitle>
            </ion-card-header>
          </ion-card>
        </ion-col>
      </ion-row>
      
    </ion-grid>
  </ion-content>

  <ion-row class="text-center" style="float: right;">
    <ion-col>
      <span><strong>{{ translate('NUMBER_OF_PROSPECTS') }}: </strong></span>
      <span>{{ prospects.length }}</span>
    </ion-col>
  </ion-row>

  <!-- Visit Details Section, visible only if showVisits is enabled -->
  <div *ngIf="showVisits">
    <div *ngFor="let visitDate of getObjectKeys(visitsHistory)">
      <div *ngFor="let prospectName of getObjectKeys(visitsHistory[visitDate])">
        <div *ngIf="visitsHistory[visitDate][prospectName].data">
          <!-- Change Visit Date -->
          <p>
            <strong>
              <a (click)="changeVisitDate(visitDate, visitsHistory[visitDate][prospectName].data.visitId)">
                {{ visitDate | date: 'dd-MM-yyyy' }}
              </a>
            </strong>
          </p>
    
          <!-- General Information -->
          <p *ngIf="visitsHistory[visitDate][prospectName].visit?.length > 0 || visitsHistory[visitDate][prospectName].pos">
            <strong *ngIf="historyIn !== 'REPORT'">{{ prospectName }}:</strong>
            <span *ngIf="historyIn !== 'REPORT'" style="color:#caab01">
              <strong>{{ visitsHistory[visitDate][prospectName].data.specialityName }}, {{ visitsHistory[visitDate][prospectName].data.localityName }}</strong>
            </span>
            <span *ngIf="visitsHistory[visitDate][prospectName].data.generalNote">
              <strong>|</strong> RG: {{ visitsHistory[visitDate][prospectName].data.generalNote }}
            </span>
            <span *ngIf="visitsHistory[visitDate][prospectName].data.recoveryStatus !== 'DELETED'">
              <strong>|</strong>
            </span>
          </p>
    
          <!-- Visit Table -->
          <table *ngIf="visitsHistory[visitDate][prospectName].visit?.length > 0" style="margin-bottom: 1%;">
            <thead>
              <tr class="header">
                <th scope="col" style="width: 10%">{{ translate('DELEGATE') }}</th>
                <th scope="col" style="width: 12%">{{ translate('PRODUCT') }}</th>
                <th scope="col" style="width: 15%">{{ translate('COMMENT') }}</th>
                <th scope="col" style="width: 5%" title="Nombre échantillons">{{ translate('EMG') }}</th>
                <th scope="col" style="width: 5%" title="Prescription">{{ translate('NUMBER_PRESCRIPTION') }}</th>
                <th scope="col" style="width: 5%" title="Quantité vendue">{{ translate('STOCK') }}</th>
                <th scope="col" style="width: 5%">{{ translate('ACTION') }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let history of visitsHistory[visitDate][prospectName].visit">
                <td [attr.data-label]="translate('DELEGATE')">{{ history.user_name }}</td>
                <td [attr.data-label]="translate('PRODUCT')">{{ history.productName }}</td>
                <td [attr.data-label]="translate('COMMENT')" *ngIf="history.comment?.length">{{ history.comment }}</td>
                <td [attr.data-label]="translate('COMMENT')" *ngIf="!history.comment">&nbsp;</td>
                <td [attr.data-label]="translate('EMG')" *ngIf="history.sample_quantity > 0">{{ history.sample_quantity }}</td>
                <td [attr.data-label]="translate('EMG')" *ngIf="!history.sample_quantity">&nbsp;</td>
                <td [attr.data-label]="translate('NUMBER_PRESCRIPTION')" *ngIf="history.prescription_quantity > 0">{{ history.prescription_quantity }}</td>
                <td [attr.data-label]="translate('NUMBER_PRESCRIPTION')" *ngIf="!history.prescription_quantity">&nbsp;</td>
                <td [attr.data-label]="translate('STOCK')" *ngIf="history.sale_quantity > 0">{{ history.sale_quantity }}</td>
                <td [attr.data-label]="translate('STOCK')" *ngIf="!history.sale_quantity">&nbsp;</td>
                <td [attr.data-label]="translate('ACTION')" *ngIf="history.user_id === userId">
                  <a *ngIf="!visitsHistory[visitDate][prospectName].blocked && historyIn !== 'REPORT'"
                     (click)="deleteVisitProduct(history, visitDate, prospectName)"
                     class="btn btn-small btn-success">
                    <img src="assets/delete.png" class="icon-trash" alt="Delete"/>
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Synchronize Button -->
  <ion-button expand="block" (click)="synchronize()" *ngIf="!noVisits && prospectSelectedId != null">
    {{ translate('SYNCHRONIZE') }}
  </ion-button>

  <!-- No Visits Message -->
  <p class="text-center" *ngIf="noVisits">{{ translate('NO_VISIT_FOUND') }}</p>

</ion-content>
