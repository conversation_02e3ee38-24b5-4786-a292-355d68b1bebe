@import '../../../global.scss';

.center-datetime {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

#add-expense-button {
  background-color: $primary-color;
  color: $card-header-color;
  width: 100%;
  margin: 10px 0;

  &.dark-theme {
    background-color: $dark-primary-color;
    color: $dark-card-header-color;
  }
}

#expense-form {
  padding: 10px;
  margin: 10px 0;
  background-color: $secondary-color;
  border-radius: 5px;

  ion-item {
    margin-bottom: 10px;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }

  ion-button {
    margin: 0;
  }

  &.dark-theme {
    background-color: $dark-secondary-color;
  }
}

#expense-cards {
  margin-top: 20px;

  ion-card {
    margin-bottom: 10px;
    background-color: $card-bg-color;
    border-radius: 8px;

    &.dark-theme {
      background-color: $dark-card-bg-color;
    }

    ion-card-header {
      background-color: $card-header-bg-color;
      color: $card-header-color;
      text-align: center;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;

      &.dark-theme {
        color: $dark-card-header-color;
      }
    }

    ion-card-title {
      font-size: 1.2em;
    }

    ion-card-content {
      padding: 10px;

      p {
        margin: 5px 0;
      }
    }
  }
}
.budget-info {
  padding: 16px;
  margin: 10px 0;
  border-radius: 8px;
  background-color: var(--ion-color-light);
}

.budget-display {
  margin-top: 15px;
  padding: 15px;
  border-radius: 8px;
  background-color: var(--ion-color-step-50);
  border-left: 4px solid var(--ion-color-primary);
}

.budget-details {
  margin-bottom: 15px;
}

.budget-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 5px 0;
}

.budget-label {
  font-weight: 500;
  color: var(--ion-color-dark);
  flex: 1;
}

.budget-value {
  font-weight: bold;
  font-size: 1.1em;
  color: var(--ion-color-primary);
  text-align: right;
}

.budget-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 6px;
  font-weight: 500;
  margin-top: 10px;
}

.message-success {
  background-color: var(--ion-color-success-tint);
  color: var(--ion-color-success-shade);
  border: 1px solid var(--ion-color-success);
}

.message-danger {
  background-color: var(--ion-color-danger-tint);
  color: var(--ion-color-danger-shade);
  border: 1px solid var(--ion-color-danger);
}

.message-warning {
  background-color: var(--ion-color-warning-tint);
  color: var(--ion-color-warning-shade);
  border: 1px solid var(--ion-color-warning);
}

.error-row {
  margin-top: 10px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background-color: var(--ion-color-danger-tint);
  border: 1px solid var(--ion-color-danger);
  border-radius: 6px;
  color: var(--ion-color-danger-shade);
  font-weight: 500;
}

.error-text {
  flex: 1;
}

.date-error {
  --border-color: var(--ion-color-danger) !important;
  --color: var(--ion-color-danger) !important;
}

.no-budget {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  padding: 12px;
  border-radius: 6px;
  background-color: var(--ion-color-warning-tint);
  border: 1px solid var(--ion-color-warning);
  color: var(--ion-color-warning-shade);
  font-weight: 500;
}

.titre_h1 {
  margin: 0 0 10px 0;
  color: var(--ion-color-primary);
  font-size: 1.3em;
}
.sync-message-row {
  margin-top: 10px;
}

.sync-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background-color: #f8d7da; /* rouge clair */
  border: 1px solid #dc3545; /* rouge */
  border-radius: 6px;
  color: #000000; /* noir */
  font-weight: 500;
}
.budget-message span,
.budget-message ion-icon {
  color: white !important;
}