<app-header [title]="translate('NOTIFICATION')"></app-header>

<ion-content [class.dark-theme]="isDarkTheme">
  <div class="header-container">
    <ion-item lines="none">
      <ion-label>{{translate('START_DATE') }} :</ion-label>
      <ion-datetime displayFormat="DD/MM/YYYY" [value]="selectedDate.toISOString()" (ionChange)="onDateChange($event)" ></ion-datetime>
    </ion-item>
  </div>
  <div style="width: 70%; margin: auto;">
    <ion-grid>
      <ion-row class="header">
        <ion-col size="4" style="width:24%">{{ translate('DATE') }}</ion-col>
        <ion-col>{{ translate('MESSAGE') }}</ion-col>
      </ion-row>

      <ion-row *ngFor="let notification of notifications">
        <ion-col size="4" style="text-align: center; background-color: #f4f5f8;" *ngIf="notification.status === 1" >
          <span style="color:black" >
            {{ notification.notificationDay + '/' + notification.notificationMonth + '/' + notification.notificationYear
            + ' ' + notification.notificationHour + ':' + notification.notificationMinutes }}
          </span>
        </ion-col>
        <ion-col size="4" style="text-align: center; background-color:#d9e0f5;" *ngIf="notification.status === 0">
          <span style="color:black">{{ notification.notificationDay + '/' + notification.notificationMonth + '/' +
            notification.notificationYear }}</span>
        </ion-col>
        <ion-col style="text-align: left; background-color: #f4f5f8;" *ngIf="notification.status === 1">
          <span style="color:black">{{ notification.notificationText }}</span>
        </ion-col>
        <ion-col style="text-align: left; background-color:#d9e0f5;" *ngIf="notification.status === 0">
          <b><span style="color:black">{{ notification.notificationText }}</span></b>
        </ion-col>
      </ion-row>
    </ion-grid>