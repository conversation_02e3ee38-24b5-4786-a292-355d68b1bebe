import { Message } from '../models/message';

export const MOCK_MESSAGES: Message[] = [
  {
    id: 1,
    text: 'Meeting with team',
    type: 'Work',
    date: new Date('2023-07-08').getTime(),
    user_id: 1,
    status: 'NEW',
    synchronized: 0
  },
  {
    id: 2,
    text: 'Doctor Appointment',
    type: 'Personal',
    date: new Date('2023-07-09').getTime(),
    user_id: 1,
    status: 'NEW',
    synchronized: 0
  },
  {
    id: 3,
    text: 'Buy groceries',
    type: 'Personal',
    date: new Date('2023-07-10').getTime(),
    user_id: 1,
    status: 'NEW',
    synchronized: 0
  },
  {
    id: 4,
    text: 'Project deadline',
    type: 'Work',
    date: new Date('2023-07-11').getTime(),
    user_id: 1,
    status: 'NEW',
    synchronized: 0
  },
  {
    id: 5,
    text: 'Dinner with friends',
    type: 'Social',
    date: new Date('2023-07-12').getTime(),
    user_id: 1,
    status: 'NEW',
    synchronized: 0
  }
];
