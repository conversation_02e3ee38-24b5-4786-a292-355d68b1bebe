# First stage: Build the Ionic application
FROM node:18-alpine AS build

WORKDIR /app

RUN npm install -g @ionic/cli

COPY package*.json ./

# Install dependencies
RUN npm install

# Copy all project files
COPY . .

# Build the Ionic application
RUN  npm run build:web

# Second stage: Serve the built application using Nginx
FROM nginx:alpine

# Copy the built files from the 'build' stage
COPY --from=build /app/www /usr/share/nginx/html

# Expose port 8090
EXPOSE 8090

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
