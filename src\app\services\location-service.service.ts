import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
//import { GoogleMap } from '@capacitor/google-maps';

@Injectable({
  providedIn: 'root',
})
export class LocationService {
  //private autocompleteService!: google.maps.places.AutocompleteService;
  //private detailsService!: google.maps.places.PlacesService;

  constructor() {
    //this.init();
  }

 /* private init(): boolean {
    try {
      this.autocompleteService = new google.maps.places.AutocompleteService();
      this.detailsService = new google.maps.places.PlacesService(document.createElement('div'));
      return true;
    } catch (err) {
      console.error('Error creating new google services', err);
      return false;
    }
  }

  searchAddress(input: string): Observable<google.maps.places.AutocompletePrediction[]> {
    return new Observable((observer) => {
      this.autocompleteService.getPlacePredictions(
        { input: input, componentRestrictions: { country: 'tn' } },
        (result, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK) {
            if (result !== null) {
              observer.next(result);
            }
            observer.complete();
          } else {
            observer.error(status);
          }
        }
      );
    });
  }

  getDetails(placeId: string): Observable<google.maps.places.PlaceResult> {
    return new Observable((observer) => {
      this.detailsService.getDetails({ placeId: placeId }, (result, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK) {
          if (result !== null) {
            observer.next(result);
          }
          observer.complete();
        } else {
          observer.error(status);
        }
      });
    });
  }*/
}