@import '../../../global.scss';

.center-datetime {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

#add-expense-button {
  background-color: $primary-color;
  color: $card-header-color;
  width: 100%;
  margin: 10px 0;

  &.dark-theme {
    background-color: $dark-primary-color;
    color: $dark-card-header-color;
  }
}

#expense-form {
  padding: 10px;
  margin: 10px 0;
  background-color: $secondary-color;
  border-radius: 5px;

  ion-item {
    margin-bottom: 10px;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }

  ion-button {
    margin: 0;
  }

  &.dark-theme {
    background-color: $dark-secondary-color;
  }
}

#expense-cards {
  margin-top: 20px;

  ion-card {
    margin-bottom: 10px;
    background-color: $card-bg-color;
    border-radius: 8px;

    &.dark-theme {
      background-color: $dark-card-bg-color;
    }

    ion-card-header {
      background-color: $card-header-bg-color;
      color: $card-header-color;
      text-align: center;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;

      &.dark-theme {
        color: $dark-card-header-color;
      }
    }

    ion-card-title {
      font-size: 1.2em;
    }

    ion-card-content {
      padding: 10px;

      p {
        margin: 5px 0;
      }
    }
  }
}
