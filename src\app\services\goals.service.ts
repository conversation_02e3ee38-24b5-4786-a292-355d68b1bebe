import { Injectable } from '@angular/core';
import { KpiOption, TypeOption } from 'src/app/models/goal';
@Injectable({
  providedIn: 'root'
})
export class GoalsService {


  private goals: { [key: string]: number } = {
    'VISIT': 100,
    'ORDER': 50,
    'CA': 100000
  };

  getGoal(kpi: KpiOption, type: TypeOption): number {
    // Implement your logic to get the objective based on KPI and Type
    return this.goals[kpi.value] || 0;
  }
  
}
