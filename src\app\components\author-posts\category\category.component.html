<ion-list lines="full">

  <form [formGroup]="categoryForm" (ngSubmit)="onSubmit()">
    <ion-item>
      <ion-input id="category-cmp-name" type="text" label="name" formControlName="name" required></ion-input>
    </ion-item>
    <ion-row>
      <ion-col>
        <ion-button type="submit" color="primary" shape="full" expand="block" [disabled]="!categoryForm.valid">
          Submit
        </ion-button>
      </ion-col>
    </ion-row>
  </form>
</ion-list>
