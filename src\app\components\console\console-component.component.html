<div *ngIf="showConsole" class="console-container">
  <div class="console-header">
    <ion-label>Console Output</ion-label>
    <div class="console-actions">
      <ion-button size="small" fill="clear" (click)="clearConsole()">
        <ion-icon name="trash-outline"></ion-icon>
      </ion-button>
      <ion-button size="small" fill="clear" (click)="copyMessages()">
        <ion-icon name="copy-outline"></ion-icon>
      </ion-button>
    </div>
  </div>
  
  <div class="console-content" id="console-log-area">
    <div *ngFor="let message of messages" 
         class="console-message" 
         [style.color]="message.color">
      <span class="timestamp">{{ message.timestamp | date:'HH:mm:ss.SSS' }}</span>
      <span class="message-type">[{{ message.type.toUpperCase() }}]</span>
      <span class="message-content">{{ message.message }}</span>
    </div>
  </div>
</div>