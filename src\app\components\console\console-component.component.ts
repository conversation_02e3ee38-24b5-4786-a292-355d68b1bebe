import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ConsoleService, ConsoleMessage } from '../../services/console-service.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-console',
  templateUrl: './console-component.component.html',
  styleUrls: ['./console-component.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class ConsoleComponent implements OnInit, OnDestroy {
  messages: ConsoleMessage[] = [];
  showConsole = false;
  private subscriptions = new Subscription();

  constructor(private consoleService: ConsoleService) {}

  ngOnInit() {
    this.subscriptions.add(
      this.consoleService.showConsole$.subscribe(show => {
        this.showConsole = show;
      })
    );

    this.subscriptions.add(
      this.consoleService.messages$.subscribe(messages => {
        this.messages = messages;
        // Auto-scroll vers le bas
        setTimeout(() => this.scrollToBottom(), 100);
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  copyMessages() {
    const allMessages = this.consoleService.copyAllMessages();
    navigator.clipboard.writeText(allMessages).then(() => {
      // Vous pouvez ajouter une notification ici
      console.log('Messages copiés dans le presse-papiers');
    });
  }

  clearConsole() {
    this.consoleService.clearMessages();
  }

  private scrollToBottom() {
    const logArea = document.getElementById('console-log-area');
    if (logArea) {
      logArea.scrollTop = logArea.scrollHeight;
    }
  }
}