import { Injectable } from '@angular/core';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { TablesUpgrades } from '../upgrades/tables/tables';
import { SQLiteService } from './sqlite.service';
import { DbnameVersionService } from './dbname-version.service';
import { environment } from 'src/environments/environment';
import { Observable, Observer } from 'rxjs';
import { Visit } from '../models/visit';


@Injectable({
  providedIn: 'root'
})
export class VisitService {
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;
  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    
}

  /*async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService
      .addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
    // create and/or open the database
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);
    const isData = await this.mDb.query("select * from sqlite_sequence");
    // create database initial data
    if (isData.values!.length === 0) {
      await this.createInitialData();
    }
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, true, "secret",
          this.loadToVersion, false);

    } else {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, false, "no-encryption",
          this.loadToVersion, false);
    }
  }
  private async createInitialData(): Promise<void> {
    // create 
    for (const visit of mockVisits) {
      await this.getVisit(visit);
    }
  }*/
  getAllVisits(): Observable<Visit[]> {
    const selectQuery = 'SELECT * FROM visit;';
    return new Observable((observer: Observer<Visit[]>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const values = this.sqliteService.snakeToCamel(result.values)
          const visits = values as Visit[];
          observer.next(visits);
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }
  async getVisit(jsonVisit: Visit): Promise<Visit> {
    let visit = await this.sqliteService.findOneBy(this.mDb, "visit", { id: jsonVisit.id });
    if (!visit) {
      if (jsonVisit.visitDate && jsonVisit.gadgetId && jsonVisit.userId && jsonVisit.prospectId && jsonVisit.companionId) {
        let visit = new Visit();
        visit.id = jsonVisit.id;
        visit.visitDate = jsonVisit.visitDate;
        visit.prospectId = jsonVisit.prospectId;
        visit.patientNumber = jsonVisit.patientNumber;
        visit.gadgetId = jsonVisit.gadgetId;
        visit.gadgetQuantity = jsonVisit.gadgetQuantity;
        visit.userId = jsonVisit.userId;
        visit.userName = jsonVisit.userName;
        visit.companionId = jsonVisit.companionId;
        visit.lat = jsonVisit.lat;
        visit.lng = jsonVisit.lng;
        visit.status = jsonVisit.status;
        visit.synchronized = jsonVisit.synchronized;
        visit.prospectId = jsonVisit.prospectId;
        if (jsonVisit.generalNote) {
          visit.generalNote = jsonVisit.generalNote;
        }
        visit=this.sqliteService.camelToSnake(visit)
        await this.sqliteService.save(this.mDb, "visit", visit);
        visit = await this.sqliteService.findOneBy(this.mDb, "visit", { id: jsonVisit.id });
        if (visit) {
          return visit;
        } else {
          return Promise.reject(`failed to getVisit for id ${jsonVisit.id}`);
        }
      } else {
        let visit = new Visit();
        visit.id = -1;
        return visit;
      }
    } else {
      return visit;
    }
  }
//filtre
async getVisitByDay(currentDate: Date) {
  const selectQuery = `
SELECT * FROM visit WHERE (visit_date = ?) AND (status<>'DELETED')  ORDER BY visit_date`;
  const res = await this.mDb.query(selectQuery,[currentDate.setHours(0,0,0,0)]);
  const values = this.sqliteService.snakeToCamel(res.values)
  return values || [];
}
}
