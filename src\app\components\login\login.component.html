<ion-content class="ion-padding">
  <div class="login-container">
    
    <!-- Étape 1: Saisie du code laboratoire -->
    <div *ngIf="!isLaboCodeEntered" class="form-section">
      <div class="header-section">
        <ion-icon name="business-outline" size="large" color="primary"></ion-icon>
        <h2>Code de l'entreprise</h2>
        <p>Veuillez saisir le code de votre entreprise</p>
      </div>

      <form #laboForm="ngForm" (ngSubmit)="submitLaboCode()" novalidate>
        <ion-item [class.ion-invalid]="!laboCode && laboForm.submitted">
          <ion-label position="floating">Code de l'entreprise</ion-label>
          <ion-input 
            [(ngModel)]="laboCode" 
            name="laboCode" 
            required
            [disabled]="isLoading">
          </ion-input>
        </ion-item>

        <ion-item *ngIf="errorMessage" class="error-item">
          <ion-icon name="alert-circle" color="danger" slot="start"></ion-icon>
          <ion-label color="danger">{{ errorMessage }}</ion-label>
        </ion-item>

        <ion-button 
          expand="full" 
          type="submit" 
          [disabled]="isLoading"
          class="submit-button">
          <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
          <span *ngIf="!isLoading">Suivant</span>
          <span *ngIf="isLoading">Validation...</span>
        </ion-button>
      </form>
    </div>

    <!-- Étape 2: Authentification -->
    <div *ngIf="isLaboCodeEntered" class="form-section">
      <div class="header-section">
        <ion-icon name="person-circle-outline" size="large" color="primary"></ion-icon>
        <h2>Authentification</h2>
        <p>Connectez-vous à votre compte</p>
        
 
      </div>

      <form #loginForm="ngForm" (ngSubmit)="login()" novalidate>
        <ion-item [class.ion-invalid]="!credentials.username && loginForm.submitted">
          <ion-label position="floating">Nom d'utilisateur</ion-label>
          <ion-input 
            [(ngModel)]="credentials.username" 
            name="username" 
            required
            [disabled]="isLoading">
          </ion-input>
        </ion-item>

        <ion-item [class.ion-invalid]="!credentials.password && loginForm.submitted">
          <ion-label position="floating">Mot de passe</ion-label>
          <ion-input 
            [(ngModel)]="credentials.password" 
            [type]="showPassword ? 'text' : 'password'" 
            name="password" 
            required
            [disabled]="isLoading">
          </ion-input>
          <ion-button 
            fill="clear" 
            slot="end" 
            (click)="toggleShowPassword()"
            [disabled]="isLoading">
            <ion-icon [name]="showPassword ? 'eye-off' : 'eye'"></ion-icon>
          </ion-button>
        </ion-item>

        <ion-item>
          <ion-checkbox 
            [(ngModel)]="showPassword" 
            name="showPassword"
            [disabled]="isLoading">
          </ion-checkbox>
          <ion-label class="ion-margin-start">Afficher mot de passe</ion-label>
        </ion-item>

        <ion-item *ngIf="errorMessage" class="error-item">
          <ion-icon name="alert-circle" color="danger" slot="start"></ion-icon>
          <ion-label color="danger">{{ errorMessage }}</ion-label>
        </ion-item>

        <div class="button-group">
          <ion-button 
            expand="full" 
            type="submit" 
            [disabled]="isLoading"
            class="submit-button">
            <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
            <span *ngIf="!isLoading">Se connecter</span>
            <span *ngIf="isLoading">Connexion...</span>
          </ion-button>

          <ion-button 
            expand="full" 
            fill="outline" 
            (click)="goBackToLaboSelection()"
            [disabled]="isLoading"
            class="back-button">
            <ion-icon name="arrow-back" slot="start"></ion-icon>
            Changer d'entreprise
          </ion-button>
        </div>
      </form>
    </div>
    <div class="logo-section">
      <div class="logo-container" (click)="onLogoClick()">
        <img src="assets/bird-note-logo.png" alt="Bird Note Logo" class="app-logo">
      </div>
      <p class="version-text">version 7.0.0</p>
    </div>
  </div>

</ion-content>
