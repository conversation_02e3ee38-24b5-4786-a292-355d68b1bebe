<app-header [title]="translate('MESSAGE')"></app-header>

<ion-content class="ion-padding">
  <form [formGroup]="messageForm">
    <ion-item>
      <ion-label>{{ LABELS.DATE }}: {{ date | date: 'dd/MM/yyyy' }}</ion-label>
      <ion-button (click)="openDatePicker()">{{ LABELS.DATE }}</ion-button>
    </ion-item>
    <div class="center-radio-group">
    <ion-item>
     
      <ion-radio-group formControlName="type">
        <ion-item>
          <ion-label>{{ LABELS.WEEKLY_REPORT }}</ion-label>
          <ion-radio slot="start" value="WEEKLY_REPORT"></ion-radio>
        </ion-item>
        <ion-item>
          <ion-label>{{ LABELS.MONTHLY_REPORT }}</ion-label>
          <ion-radio slot="start" value="MONTHLY_REPORT"></ion-radio>
        </ion-item>
        <ion-item>
          <ion-label>{{ LABELS.MESSAGE }}</ion-label>
          <ion-radio slot="start" value="MESSAGE"></ion-radio>
        </ion-item>
      </ion-radio-group>

    </ion-item>
  </div>
    <ion-item>
      <ion-label position="stacked">{{ LABELS.MESSAGE }}</ion-label>
      <ion-textarea formControlName="text" rows="6"></ion-textarea>
    </ion-item>

    <ion-button expand="block" (click)="saveMessage()" [disabled]="!messageForm.valid">
      {{ LABELS.SAVE }}
    </ion-button>
  </form>

  <ion-list>
    <ion-item *ngFor="let message of messages; let i = index">
      <ion-label>
        <h2>{{ message.type }}</h2>
        <p>{{ message.text }}</p>
        <p>{{ message.date | date: 'dd/MM/yyyy' }}</p>
      </ion-label>
      <ion-buttons slot="end">
        <ion-button class="white-icon" (click)="updateMessage(message)">
          <ion-icon name="create"></ion-icon>
        </ion-button>
        <ion-button class="white-icon" (click)="deleteMessage(message.id)">
          <ion-icon name="trash"></ion-icon>
        </ion-button>
      </ion-buttons>
    </ion-item>
  </ion-list>
</ion-content>
