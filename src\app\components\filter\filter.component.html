<ion-grid>
  <!-- First Row -->
  <ion-row>
    <ion-col size="6">
      <ion-item>
        <ion-label position="stacked">{{ translate('SECTOR') }}</ion-label>
        <ionic-selectable
          [(ngModel)]="_sectorSelected"
          [items]="sectors"
          itemTextField="name"
          itemValueField="id"
          [canSearch]="true"                           
          (onChange)="syncSelections(
            _sectorSelected,
            _localitySelected,
            _establishmentSelected,
            _specialitySelected,
            _potentialSelected,
            _activitySelected
          );filterProspects(_sectorSelected, _localitySelected, _establishmentSelected, _specialitySelected, _potentialSelected, _activitySelected, keywordsFilter, showOnlyPlanifiedProspects, showOnlyNotVisited, null); filterLocalitiesBySector(_sectorSelected.id)">
        </ionic-selectable>
      </ion-item>
    </ion-col>
    <ion-col size="6">
      <ion-item>
        <ion-label position="stacked">{{ translate('LOCALITY') }}</ion-label>
        <ionic-selectable
          [(ngModel)]="_localitySelected"
          [items]="localities"
          itemTextField="name"
          itemValueField="id"
          [canSearch]="true"
          searchPlaceholder="{{ translate('SEARCH_LOCALITY') }}"
          (onChange)="onLocalityChange($event.value)">
        </ionic-selectable>
      </ion-item>
    </ion-col>
  </ion-row>

  <ion-row>
    <!-- SPECIALITY -->
    <ion-col size="4">
      <ion-item>
        <ion-label position="stacked">{{ translate('SPECIALITY') }}</ion-label>
        <ionic-selectable
          [(ngModel)]="_specialitySelected"
          [items]="specialities"
          itemTextField="name"
          itemValueField="id"
          [canSearch]="true"
          searchPlaceholder="{{ translate('SEARCH_SPECIALITY') }}"
          (onChange)="
            syncSelections(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected
            );
            filterProspects(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected,
              keywordsFilter,
              showOnlyPlanifiedProspects,
              showOnlyNotVisited,
              null
            )">
        </ionic-selectable>
      </ion-item>
    </ion-col>
  
    <!-- POTENTIAL -->
    <ion-col size="4">
      <ion-item>
        <ion-label position="stacked">{{ translate('POTENTIAL') }}</ion-label>
        <ionic-selectable
          [(ngModel)]="_potentialSelected"
          [items]="potentials"
          itemTextField="name"
          itemValueField="id"
          [canSearch]="true"
          searchPlaceholder="{{ translate('SEARCH_POTENTIAL') }}"
          (onChange)="
            syncSelections(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected
            );
            filterProspects(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected,
              keywordsFilter,
              showOnlyPlanifiedProspects,
              showOnlyNotVisited,
              null
            )">
        </ionic-selectable>
      </ion-item>
    </ion-col>
  
    <!-- ACTIVITY -->
    <ion-col size="4">
      <ion-item>
        <ion-label position="stacked">{{ translate('ACTIVITY') }}</ion-label>
        <ionic-selectable
          [(ngModel)]="_activitySelected"
          [items]="activities"
          itemTextField="value"
          itemValueField="id"
          [canSearch]="true"
          searchPlaceholder="{{ translate('SEARCH_ACTIVITY') }}"
          (onChange)="
            syncSelections(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected
            );
            filterEstablishmentByActivity(_activitySelected.id);
            filterProspects(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected,
              keywordsFilter,
              showOnlyPlanifiedProspects,
              showOnlyNotVisited,
              null
            )">
        </ionic-selectable>
      </ion-item>
    </ion-col>
  </ion-row>
  
  <!-- ESTABLISHMENT -->
  <ion-row>
    <ion-col>
      <ion-item>
        <ion-label position="stacked">{{ translate('ESTABLISHMENT') }}</ion-label>
        <ionic-selectable
          [(ngModel)]="_establishmentSelected"
          [items]="establishments"
          itemTextField="name"
          itemValueField="id"
          [canSearch]="true"
          searchPlaceholder="{{ translate('SEARCH_ESTABLISHMENT') }}"
          (onChange)="
            syncSelections(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected
            );
            filterProspects(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected,
              keywordsFilter,
              showOnlyPlanifiedProspects,
              showOnlyNotVisited,
              null
            )">
        </ionic-selectable>
      </ion-item>
    </ion-col>
  </ion-row>
  
  <!-- Filter Input -->
  <ion-row>
    <ion-col>
      <ion-item>
        <ion-input
          placeholder="{{ translate('GLOBAL_FILTER') }}"
          [(ngModel)]="keywordsFilter"
          (ionInput)=" syncSelections(
            _sectorSelected,
            _localitySelected,
            _establishmentSelected,
            _specialitySelected,
            _potentialSelected,
            _activitySelected
          );filterProspects(_sectorSelected, _localitySelected, _establishmentSelected, _specialitySelected, _potentialSelected, _activitySelected, keywordsFilter, showOnlyPlanifiedProspects, showOnlyNotVisited, null)">
        </ion-input>
      </ion-item>
    </ion-col>
  </ion-row>
</ion-grid>
