import { Injectable } from '@angular/core';

import { SQLiteService } from './sqlite.service';
import { Toast } from '@capacitor/toast';
import { NotificationService } from './notification.service';
import { ExpenseService } from './expense.service';
import { MessageService } from './message.service';
import { VisitHistoryService} from './visit-history-service.service';
/*import { UserService } from './user-service.service';*/
import { ActivityService } from './activity.service';
import { FilterService } from './filterService.service';
import { VisitService } from './visit.service';
import { ActionMarketingService } from './action-marketing.service';
import { PlanningService } from './planning.service';

import { DbCreationTablesService } from './db-creation-tables.service';
import { ProspectService } from 'src/app/services/prospect-service.service';
import { ChartService } from 'src/app/services/chart-service.service';
import { UserService } from './user-service.service';
import { LoginService } from './login-service.service';
import { CommonService } from 'src/app/services/common-service.service';
@Injectable()
export class InitializeAppService {
  isAppInit: boolean = false;
  platform!: string;

  constructor(
    private sqliteService: SQLiteService,
    private notificationservice: NotificationService,
    private expenseService:ExpenseService,
    private messageService: MessageService,
    private userService: UserService,
    private filterService:FilterService,
    private planningService:PlanningService, 
    
    private visitService:VisitService,
    private ActionmarketingService:ActionMarketingService,
    private activityService:ActivityService,
    private dbCreationTablesService: DbCreationTablesService,

    private ChartService:ChartService,
    private loginService: LoginService,
    private CommonService: CommonService,
    private ProspectService: ProspectService,
    private VisitHistoryService: VisitHistoryService

    ) {
  }

  async initializeApp() {
    await this.sqliteService.initializePlugin().then(async (ret) => {
      this.platform = this.sqliteService.platform;
      try {
        if( this.sqliteService.platform === 'web') {
          await this.sqliteService.initWebStore();
        }
      
        await this.dbCreationTablesService.initializeDatabase();
        await this.expenseService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.activityService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.notificationservice.initDatabase(this.dbCreationTablesService.getDatabase())
        await this.messageService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.ActionmarketingService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.ChartService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.planningService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.userService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.loginService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.visitService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.filterService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.CommonService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.ProspectService.initDatabase(this.dbCreationTablesService.getDatabase());
        await this.VisitHistoryService.initDB(this.dbCreationTablesService.getDatabase())


        
        this.isAppInit = true;

      } catch (error) {
        console.log(`initializeAppError: ${error}`);
        await Toast.show({
          text: `initializeAppError: ${error}`,
          duration: 'long'
        });
      }
    });
  }

}
