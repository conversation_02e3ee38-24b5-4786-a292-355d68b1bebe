<app-header [title]="translate('CALENDAR')"></app-header>
<!--date Picker-->
<ion-content>
  <form [formGroup]="activityForm">
    <div class="center-datetime">
      <ion-datetime [isDateEnabled]="isMonday" 
      presentation="date"
      formControlName="selectedDate"
      (ionChange)="onDateChange($event)">
</ion-datetime>
    </div>
    <ion-item style="background-color:transparent;text-align: center">
      <ion-label>{{ translate('WEEK_OF') }} : {{ selectedDate | date: 'dd/MM/yyyy' }}</ion-label>
    </ion-item>
    <ion-grid>
      <ion-row>
        <ion-col>
          <!-- Table Header -->
          <ion-card >
            <ion-card-subtitle>
              <ion-grid>
                <ion-row class="table-header">
                  <ion-col>{{translate('ACTIVITY_TYPE')}}</ion-col>
                  <ion-col >{{ translate('MONDAY') }}<br> {{
                    selectedDate | date: 'dd/MMM' }}</ion-col>
                  <ion-col >{{ translate('TUESDAY') }}<br> {{
                    dates[1] | date: 'dd/MMM' }}</ion-col>
                  <ion-col >{{ translate('WEDNESDAY') }}<br> {{
                    dates[2] | date: 'dd/MMM' }}</ion-col>
                  <ion-col>{{ translate('THURSDAY') }}<br> {{
                    dates[3] | date: 'dd/MMM' }}</ion-col>
                  <ion-col>{{ translate('FRIDAY') }}<br> {{ dates[4]
                    | date: 'dd/MMM' }}</ion-col>
                  <ion-col>{{ translate('SATURDAY') }}<br> {{
                    dates[5] | date: 'dd/MMM' }}</ion-col>
                  <ion-col>{{ translate('SUNDAY') }}<br> {{ dates[6]
                    | date: 'dd/MMM' }}</ion-col>
                </ion-row>
              </ion-grid>
            </ion-card-subtitle>
            <ion-grid>
              <ion-row>
                <ion-col>{{ translate('VISIT_PO') }}</ion-col>
                <ion-col *ngFor="let visit of visitList">
                  <ion-item *ngFor="let i of visit" class="custom-background">
                    - {{ getNameFromProspectListById(i.prospectId) }}
                  </ion-item>
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>{{ translate ('PLANNING_') }}</ion-col>
                <ion-col *ngFor="let planning of  planningList" >
                  <ion-item *ngFor="let i of planning" class="custom-background">
                    - {{ getNameFromProspectListById(i.prospectId) }}
                  </ion-item>
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>{{ translate('EXPENSE_REPORTS') }}</ion-col>
                <ion-col *ngFor="let expense of expenseList" >
                  <ion-item *ngFor="let i of expense  " class="custom-background">
                    <ion-col> {{getExpenseTypeNameFromListById(i.expenseTypeId)}}-{{ i.montant }} DT </ion-col>
                  </ion-item>
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>{{ translate ('MARKETING_ACTIONS') }}</ion-col>
                <ion-col *ngFor="let action of marketingActionsList" >
                  <ion-item *ngFor="let i of action  " class="custom-background">
                    <ion-col>-{{i.name}}</ion-col>
                  </ion-item>
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>{{ translate('OTHER_ACTIVITIES') }}</ion-col>
                <ion-col *ngFor="let activity of activityList" >
                  <ion-item *ngFor="let i of activity  " class="custom-background">
                    <ion-col (click)="updateActivity(i)">-{{ getActivityTypeNameFromListById(i.activityTypeId) }} ({{
                      i.hourNumber }} {{ translate('HOURS') }})</ion-col>
                    <ion-icon name="trash" (click)="clickShowConfDeleted( i.id)"></ion-icon>
                  </ion-item>
                </ion-col>
              </ion-row>
              <ion-row>
                <ion-col>{{ translate('ADD_OTHER_ACTIVITIES') }}</ion-col>
                <ion-col *ngFor="let i of [1,2,3,4,5,6,7]">
                  <ion-button (click)="addOtherActivities(i)">
                    <ion-icon name="add"> </ion-icon>
                  </ion-button>
                </ion-col>
              </ion-row>
            </ion-grid>
          </ion-card>
          <ion-button expand="full" (click)="addActivityOverPeriod()">
            {{ translate('ADD_ACTIVITY_OVER_PERIOD')}}
          </ion-button>
        </ion-col>
      </ion-row>

      <div *ngIf="buttonAddActivityOverPeriod  || buttonAddOtherActivities || buttonUpdateActivity">
        <ion-row *ngIf="buttonAddActivityOverPeriod">
          <ion-col>
            <ion-item class="center-datetime">
              <ion-label>{{ translate('START_DATE') }}:</ion-label>
              <ion-input type="date" formControlName="startDate" (ionChange)="onStartDateChange($event)"></ion-input>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item class="center-datetime">
              <ion-label>{{ translate('END_DATE') }}:</ion-label>
              <ion-input type="date" formControlName="endDate" (ionChange)="onEndDateChange($event)"></ion-input>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-col>
          <ion-select formControlName="typeName" [placeholder]="translate('CHOOSE_TYPE')"
            (ionChange)="typeChange($event)">
            <ion-select-option *ngFor="let activityType of activityTypes" [value]="activityType">
              {{ activityType.name }}
            </ion-select-option>
          </ion-select>
          <ion-note color="danger"
            *ngIf="activityForm.get('typeName')?.touched && activityForm.get('typeName')?.hasError('required')">
            {{ translate('TYPE_REQUIRED') }}
          </ion-note>
        </ion-col>
        <ion-row *ngIf="buttonAddOtherActivities || buttonUpdateActivity">
          <ion-col>
            <ion-select formControlName="hourNumber" 
              [placeholder]="translate('Number of hours')" (ionChange)="hourNumberChange($event)">
              <ion-select-option *ngFor="let i of [1, 2, 3, 4, 5, 6, 7, 8]" [value]="i">{{ i }}</ion-select-option>
            </ion-select>
            <ion-note
              *ngIf="activityForm.get('hourNumber')?.touched && activityForm.get('hourNumber')?.hasError('required')"
              color="danger">{{ translate('HOURS_REQUIRED') }}</ion-note>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-textarea formControlName="comment" [placeholder]="translate('COMMENT')" maxlength="255"
              required></ion-textarea>
          </ion-col>
        </ion-row>
        <ion-button expand="full" (click)="save()">
          {{ translate('SAVE') }}
        </ion-button>

        <ion-button *ngIf="" expand="full" [routerLink]="['/expense-list']">
          {{ translate('ADD_EXPENSE_TO_ACTIVITY') }}
        </ion-button>
        <ion-button expand="full" [routerLink]="['/home']">
          {{ translate('RETURN') }}
        </ion-button>
        <div *ngIf="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
      </div>