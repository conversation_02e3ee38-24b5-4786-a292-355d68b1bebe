.console-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: #1e1e1e;
    border-top: 1px solid #333;
    z-index: 9999;
    display: flex;
    flex-direction: column;
  }
  
  .console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #2d2d2d;
    border-bottom: 1px solid #333;
    
    ion-label {
      color: #fff;
      font-weight: bold;
    }
  }
  
  .console-actions {
    display: flex;
    gap: 4px;
    
    ion-button {
      --color: #fff;
    }
  }
  
  .console-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
  }
  
  .console-message {
    margin-bottom: 2px;
    word-wrap: break-word;
    
    .timestamp {
      color: #888;
      margin-right: 8px;
    }
    
    .message-type {
      font-weight: bold;
      margin-right: 8px;
    }
    
    .message-content {
      white-space: pre-wrap;
      color: #ffffff;
    }
  }
  
  // Ajuster le contenu principal quand la console est visible
  :host-context(.console-visible) {
    ion-content {
      --padding-bottom: 200px;
    }
  }
  .console-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: #1e1e1e;
    border-top: 2px solid #ff0000; // Rouge pour debug
    z-index: 9999;
    display: flex;
    flex-direction: column;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.5);
  }
  
  .console-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #2d2d2d;
    border-bottom: 1px solid #333;
    
    ion-label {
      color: #fff;
      font-weight: bold;
    }
  }
  
  .console-actions {
    display: flex;
    gap: 4px;
    
    ion-button {
      --color: #fff;
    }
  }
  
  .console-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    background: #1e1e1e;
  }
  
  .no-messages {
    color: #888;
    font-style: italic;
    text-align: center;
    padding: 20px;
  }
  
  .console-message {
    margin-bottom: 2px;
    word-wrap: break-word;
    
    .timestamp {
      color: #888;
      margin-right: 8px;
    }
    
    .message-type {
      font-weight: bold;
      margin-right: 8px;
    }
    
    .message-content {
      white-space: pre-wrap;
      color: #ffffff;
    }
  }