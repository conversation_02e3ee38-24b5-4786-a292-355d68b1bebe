/*---------------------------------------
  1) MAIN GRID (your products table)
---------------------------------------*/
.responsive-table {
  display: table;
  table-layout: fixed;      // ← honor the widths you set below
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 16px;
}

.responsive-table ion-row {
  display: table-row;
}

/* HEADER ROW */
.responsive-table ion-row.header {
  display: table-header-group;
  background: #f5f5f5;
}

.responsive-table ion-row.header ion-col {
  display: table-cell;
  padding: 8px 12px;
  font-weight: bold;
  color: #333;
  border: 1px solid #ccc;
  text-align: center;
}

/* DATA ROWS */
.responsive-table ion-row:not(.header) ion-col {
  display: table-cell;
  padding: 8px 12px;
  border: 1px solid #ccc;
  vertical-align: middle;
  color: #333;
  font-size: 0.9rem;
  overflow: visible !important;   // allow emoji tooltip to escape
  position: relative;
}

/*---------------------------------------
 FOOTER GRID (bottom form)
---------------------------------------*/
/* show name + delete button on one line */
.footer-col .tagged-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* style the name */
.footer-col .tagged-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* make the × obvious & clickable */
.footer-col .remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  padding: 0 4px;
  color: var(--ion-color-danger, red);
}
.footer-table {
  display: table;
  width: 100%;
  border-collapse: collapse;
  margin-top: 24px;
  border: 1px solid #ccc;
  margin-bottom: 16px;
}

.footer-table ion-row {
  display: flex;
}

.footer-table ion-row:first-child ion-col {
  border-top: none;
}

.footer-table ion-col {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  padding: 8px 12px;
  background: #fff;
  color: #333;
  font-size: 0.9rem;
}

.footer-table ion-col:last-child {
  border-right: 1px solid #ccc;
}

.footer-table ion-col.ion-col:first-child {
  border-left: none;
}

.footer-table .footer-label {
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 4px;
  display: block;
}

.footer-table ion-input,
.footer-table ion-textarea,
.footer-table ion-select {
  background: #fff !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  width: 100% !important;
  box-sizing: border-box;
}

.footer-table ion-list {
  max-height: 150px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 4px;
}

.footer-table ion-row:last-child {
  margin-top: 8px;
}

/*---------------------------------------
  3) WHITEN OUT ALL INPUTS/TEXTAREAS
---------------------------------------*/
.responsive-table ion-input,
.responsive-table ion-textarea,
.footer-table ion-input,
.footer-table ion-textarea {
  --padding-start: 8px;
  --padding-end: 8px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/*=======================================
  4) SMILEY CONTAINER & TOOLTIP
=========================================*/
.smiley-container {
  position: relative;
  overflow: visible;

  .active-smiley {
    width: 40px;
    height: 40px;
    cursor: pointer;
  }

  .smiley-tooltip {
    display: none;
    position: absolute;
    top: 110%;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    z-index: 9999;
    grid-template-columns: repeat(3, auto);
    grid-gap: 6px;
     // ➜ force shrink-wrap and grid‐layout
     width: max-content;
     white-space: nowrap; 
     grid-template-columns: repeat(auto-fill, minmax(40px, auto));
     gap: 8px;
  }

  &:hover .smiley-tooltip {
    display: grid;
  }


  .tooltip-smiley {
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
    &:hover { transform: scale(1.2); }
  }
}

/*---------------------------------------
  5) NARROW SCREENS: allow horizontal scroll
---------------------------------------*/
@media (max-width: 576px) {
  .responsive-table,
  .footer-table {
    display: block;
    overflow-x: auto;
  }
  .responsive-table ion-row,
  .footer-table ion-row {
    display: block;
  }
  .responsive-table ion-row.header,
  .footer-table ion-row.header {
    display: none;
  }
  .responsive-table ion-row:not(.header) ion-col,
  .footer-table ion-row:not(.header) ion-col {
    display: block;
    width: 100%;
    box-sizing: border-box;
  }
}

/*---------------------------------------
  6) FIXED COLUMN WIDTHS (desktop only)
---------------------------------------*/
@media (min-width: 768px) {
  .responsive-table {
    /* 1: Order */
    ion-row.header ion-col:nth-child(1),
    ion-row:not(.header) ion-col:nth-child(1) {
      width: 10%;   min-width: 60px;
    }
    /* 2: Product */
    ion-row.header ion-col:nth-child(2),
    ion-row:not(.header) ion-col:nth-child(2) {
      width: 25%;   min-width: 120px;
    }
    /* 3: Comment */
    ion-row.header ion-col:nth-child(3),
    ion-row:not(.header) ion-col:nth-child(3) {
      width: 35%;   min-width: 200px;
    }
    /* 4: Emergency bell */
    ion-row.header ion-col:nth-child(4),
    ion-row:not(.header) ion-col:nth-child(4) {
      width: 5%;    min-width: 40px;
    }
    /* 5: Smiley */
    ion-row.header ion-col:nth-child(5),
    ion-row:not(.header) ion-col:nth-child(5) {
      width: 5%;    min-width: 40px;
    }
    /* 6: # Prescriptions / Stock */
    ion-row.header ion-col:nth-child(6),
    ion-row:not(.header) ion-col:nth-child(6) {
      width: 10%;   min-width: 80px;
    }
    /* 7: EMG */
    ion-row.header ion-col:nth-child(7),
    ion-row:not(.header) ion-col:nth-child(7) {
      width: 10%;   min-width: 80px;
    }
  }
}

/* 1) make the scroll container transparent (no blue bg) */
ion-scroll.prospects-scroll {
  --background: transparent !important;
}

/* 2) inner flex row */
.prospects-inner {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  overflow-x: auto;
}

/* 3) base card styling */
.prospect-card,
.add-card {
  flex: 0 0 auto;
  width: 100px;
  text-align: center;
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  margin-right: 12px;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease;
}

/* 4) green highlight on selected */
.prospect-card.selected {
  border: 3px solid var(--ion-color-success);
  box-shadow: 0 0 8px rgba(var(--ion-color-success-rgb), 0.3);
  transform: translateY(-4px);
}
/* 5) round images */
.prospect-img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-top: 5px;
  object-fit: cover;
}

/* 6) special add-button image is slightly larger */
.add-img {
  width: 120px;
  height: 120px;
  margin-top: 0;
}

/* 7) name & speciality text */
.prospect-name {
  font-size: 13px;
  margin: 4px 0 0;
  color: var(--ion-color-dark);
}

.prospect-speciality {
  font-size: 13px;
  margin: 2px 0 8px;
  color: var(--ion-color-medium);
}


/* tablet ≈ 3 cards */
@media (min-width: 600px) {
  .prospect-card {
    flex-basis: 30%;
    max-width: 30%;
  }
}

/* desktop ≈ 5 cards */
@media (min-width: 992px) {
  .prospect-card {
    flex-basis: 18%;
    max-width: 18%;
  }
}

/* doctor or plus images */
.prospect-img {
  display: block;
  width: 60px;
  height: 60px;
  margin: 8px auto 4px;
}

/* special styling for the add button icon */
.add-img {
  width: 80px;
  height: 80px;
  margin: 4px auto 8px;
}

/* card titles */
.prospect-name {
  font-size: 0.85rem;
  text-align: center;
}

.prospect-speciality {
  font-size: 0.75rem;
  text-align: center;
  color: var(--ion-color-medium);
}

/* the “add” card gets white background, a little more padding */
.add-card {
  --background: #fff;
}
.comment-suggestion-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--ion-background-color, #fff);
  box-shadow: 0 -2px 8px rgba(0,0,0,0.15);
  padding: 8px 0 60px 0; /* ajoute 16px d’espace en dessous */
  z-index: 1000;
}


.comment-scroll {
  display: flex;
  padding: 0 12px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.comment-pill {
  flex: 0 0 auto;
  background: var(--ion-color-light, #f4f5f8);
  color: var(--ion-color-dark, #222);
  border-radius: 16px;
  padding: 6px 14px;
  margin-right: 8px;
  white-space: nowrap;
  font-size: 0.85rem;
  cursor: pointer;
  user-select: none;
  transition: background 0.2s;
}
.comment-pill:hover {
  background: var(--ion-color-medium-tint, #e0e0e0);
}

.product-link {
  text-decoration: underline;
  color: #000;
  cursor: pointer;
  display: inline-block;
  margin-bottom: 4px;
}

.product-info {
  font-size: 0.85rem;
  color: #555;
  margin-bottom: 8px;
}

.stock-zero {
  color: red;
  font-weight: bold;
}

.stock-normal {
  color: inherit;
}
ion-content .prospects-inner {
  display: flex;
  overflow-x: auto;
  padding: 8px;
}

.hidden-datetime {
  display: none;
}
/* Dans votre fichier CSS/SCSS global (ex: src/theme/variables.scss ou src/global.scss) */

ion-datetime {
  /* Neutralise la mise en évidence du jour "aujourd'hui" */
  --ion-datetime-calendar-day-today-background: var(--ion-datetime-calendar-day-background);
  --ion-datetime-calendar-day-today-text-color: var(--ion-datetime-calendar-day-text-color);

  /* Neutralise l'apparence des jours désactivés */
  --ion-datetime-calendar-day-disabled-opacity: 1; /* Rend les jours désactivés entièrement opaques */
  --ion-datetime-calendar-day-disabled-text-color: var(--ion-datetime-calendar-day-text-color); /* Utilise la même couleur de texte que les jours normaux */

  /* Le jour sélectionné (votre lundi) conservera sa couleur par défaut ou celle que vous définissez */
  /* Exemple si vous voulez une couleur spécifique pour le sélectionné: */
  /* --ion-datetime-calendar-day-selected-background: var(--ion-color-primary); */
  /* --ion-datetime-calendar-day-selected-text-color: var(--ion-color-primary-contrast); */
}