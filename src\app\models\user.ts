export interface User {
    user_id: number;
    username: string;
    password: string;
    first_last_name: string;
    work_type: string;
    working_days: number;
    last_synchronisation: string;
    last_receive_date: number;
    first_sync: boolean;
    time: string;
    auto_sync: boolean;
    lock_after_sync: boolean;
    multi_wholesaler: boolean;
    sync_cycle: number;
    comments_dictionary: string;
    open_report_period: number;
    open_expense_period: number;
  }
  
  export interface Users {
    id: number;
    name: string;
    delegate_id: number;
  }
  