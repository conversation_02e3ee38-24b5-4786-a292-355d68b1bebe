<ng-container *ngFor="let dateEntry of visitsHistory | keyvalue">
  <ng-container *ngFor="let prospectEntry of dateEntry.value | keyvalue">
    <div style="margin-bottom: 1%;">

      <!-- invalid‐date banner -->
      <div *ngIf="!modifyDate" style="background-color:#FF5C4D; margin-top:8px; padding:8px;">
        <h4 class="title">
          La date choisie est non permise : vous ne pouvez pas choisir ni une date inférieure à
          {{ openReportPeriod }} {{ LABELS.DAY }} {{ LABELS.AFTER_CURRENT_DATE }}
        </h4>
      </div>

      <!-- date header -->
      <p style="color:#caab01; font-size:x-large;">
        <strong>
          <a (click)="changeVisitDate(+dateEntry.key, prospectEntry.value.data.visitId)">
            {{ dateEntry.key | date:'dd-MM-yyyy' }}
          </a>
        </strong>
      </p>

      <!-- prospect info + recovery -->
      <p *ngIf="
            prospectEntry.value.data
            && (
               prospectEntry.value.visit.length > 0
               || (prospectEntry.value.pos | keyvalue).length > 0
            )">
        <strong *ngIf="historyIn !== 'REPORT'">
          {{ prospectEntry.key }} :
        </strong>
        <span *ngIf="historyIn !== 'REPORT'" style="color:#caab01">
          <strong>
            {{ prospectEntry.value.data.specialityName }},
            {{ prospectEntry.value.data.localityName }}
          </strong>
        </span>
        <strong *ngIf="historyIn !== 'REPORT' && prospectEntry.value.data.generalNote">|</strong>
        <span *ngIf="prospectEntry.value.data.generalNote" style="color:#caab01">
          RG :
        </span>
        {{ prospectEntry.value.data.generalNote }}
        <strong
          *ngIf="
            prospectEntry.value.data.recoveryStatus
            && prospectEntry.value.data.recoveryStatus !== 'DELETED'
          ">|</strong>
        <a
          *ngIf="
            prospectEntry.value.data.recoveryStatus
            && prospectEntry.value.data.recoveryStatus !== 'DELETED'
          "
          style="text-decoration:underline"
          [routerLink]="['/rapport']"
          [queryParams]="{
            prospectId: prospectEntry.value.data.prospectId,
            visitDate: +dateEntry.key,
            type: 'ORDER',
            purchaseOrderTemplateId: null
          }"
        >
          {{ LABELS.RECOVERY }}
        </a>
      </p>

      <!-- VISITS table -->
      <p *ngIf="prospectEntry.value.visit.length > 0" style="font-size:large;">
        <strong>{{ LABELS.VISIT }}</strong>
      </p>
      <table
        *ngIf="prospectEntry.value.visit.length > 0"
        class="responsive-table"
        style="margin-bottom:1%"
      >
        <thead>
          <tr class="header">
            <th>{{ LABELS.DELEGATE }}</th>
            <th>{{ LABELS.PRODUCT }}</th>
            <th>{{ LABELS.COMMENT }}</th>
            <th title="Nombre échantillons">{{ LABELS.EMG }}</th>
            <th title="Prescription">{{ LABELS.NUMBER_PRESCRIPTION }}</th>
            <th title="Quantité vendue">{{ LABELS.STOCK }}</th>
            <th>{{ LABELS.ACTION }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let h of prospectEntry.value.visit; let i = index">
            <td>{{ h.user_name }}</td>
            <td>{{ h.productName }}</td>
            <td>{{ h.comment || ' ' }}</td>
            <td>{{ h.sample_quantity || ' ' }}</td>
            <td>{{ h.prescription_quantity || ' ' }}</td>
            <td>{{ h.sale_quantity || ' ' }}</td>
            <td *ngIf="h.user_id === userId">
              <button
                *ngIf="
                  !prospectEntry.value.blocked
                  && historyIn !== 'REPORT'
                  && h.purchaseOrderTemplateId == null
                "
                (click)="deleteVisitProduct(i, dateEntry.key, prospectEntry.key, h, null)"
                class="btn btn-small btn-success"
                title="Supprimer la visite"
              >
                {{ LABELS.DELETE }}
              </button>
            </td>
            <td *ngIf="h.user_id !== userId"> </td>
          </tr>
        </tbody>
      </table>

      <!-- edit/delete visit -->
      <div
        class="row"
        style="margin-bottom:1%"
        *ngIf="
          prospectEntry.value.visit[0]?.user_id === userId
          && historyIn !== 'REPORT'
          && !prospectEntry.value.blocked
        "
      >
        <div class="col">
          <button
            class="btn btn-outline-primary"
            
          >
            {{ LABELS.EDIT }}
          </button>
        </div>
        <div class="col">
          <button
            class="btn btn-outline-danger"
            (click)="deleteVisit(prospectEntry.value.visit, dateEntry.key, prospectEntry.key)"
          >
            {{ LABELS.DELETE }}
          </button>
        </div>
      </div>

      <!-- PURCHASE ORDERS -->
      <ng-container *ngFor="let poEntry of prospectEntry.value.pos | keyvalue">
        <div *ngIf="poEntry.value.length" style="margin-bottom:1%">
          <p style="font-size:large;">
            <strong>
              {{ LABELS.ORDER }} :
              <span *ngIf="poEntry.value[0].purchaseOrderTemplateId">FERME</span>
              {{ poEntry.value[0].wholesalerName }}
            </strong>
          </p>

          <table class="responsive-table" style="margin-bottom:1%">
            <thead>
              <tr class="header">
                <th>{{ LABELS.DELEGATE }}</th>
                <th>{{ LABELS.PRODUCT }}</th>
                <th title="Quantité commandée">{{ LABELS.ORDERED_QUANTITY }}</th>
                <th title="Gratuité">{{ LABELS.GRATUITY }}</th>
                <th title="Gratuité Labo">{{ LABELS.GRATUITY_LABO }}</th>
                <th>{{ LABELS.ACTION }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of poEntry.value; let j = index">
                <td>{{ item.user_name }}</td>
                <td>{{ item.productName }}</td>
                <td>{{ item.order_quantity || ' ' }}</td>
                <td>{{ item.freeOrder        || ' ' }}</td>
                <td>{{ item.lab_gratuity    || ' ' }}</td>
                <td *ngIf="item.user_id === userId">
                  <button
                    *ngIf="historyIn !== 'REPORT' && !prospectEntry.value.blocked"
                    (click)="deleteVisitProduct(j, dateEntry.key, prospectEntry.key, item, poEntry.key)"
                    class="btn btn-small btn-success"
                    title="Supprimer la commande"
                  >
                    {{ LABELS.DELETE }}
                  </button>
                </td>
                <td *ngIf="item.user_id !== userId"> </td>
              </tr>
            </tbody>
          </table>

          <div
            class="row"
            style="margin-bottom:1%"
            *ngIf="
              poEntry.value[0].user_id === userId
              && historyIn !== 'REPORT'
              && !prospectEntry.value.blocked
            "
          >
            <div class="col">
              <button
                class="btn btn-outline-primary"
              >
                {{ LABELS.EDIT }}
              </button>
            </div>
            <div class="col">
              <button
                class="btn btn-outline-danger"
                (click)="deletePo(
                  poEntry.key,
                  poEntry.value[0].visit_id,
                  poEntry.value[0],
                  dateEntry.key,
                  prospectEntry.key
                )"
              >
                {{ LABELS.DELETE }}
              </button>
            </div>
          </div>
        </div>
      </ng-container>

    </div>
  </ng-container>
</ng-container>
