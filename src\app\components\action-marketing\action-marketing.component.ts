import { Compo<PERSON>,  <PERSON><PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON> } from "@angular/core"
import  { ActionMarketingService } from "../../services/action-marketing.service"
import { IonicModule } from "@ionic/angular"
import { FormsModule, ReactiveFormsModule } from "@angular/forms"
import { CommonModule } from "@angular/common"
import  { TranslationService } from "src/app/services/traduction-service.service"
import  { LoginService } from "src/app/services/login-service.service"
import  { ThemeService } from "src/app/services/theme.service"
import {  FormBuilder,  FormGroup, Validators } from "@angular/forms"
import { HeaderComponent } from "../../header/header.component"
import  { Subscription } from "rxjs"

@Component({
  selector: "app-marketing-actions",
  templateUrl: "./action-marketing.component.html",
  styleUrls: ["./action-marketing.component.scss"],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, ReactiveFormsModule, HeaderComponent],
})
export class MarketingActionsComponent implements OnI<PERSON>t, OnDestroy {
  marketing_action: any[] = []
  displayForm = false
  marketingForm!: FormGroup
  errorMessage = ""
  products: any[] = []
  prospects: any[] = []
  translations: any = {}
  nextActionRules: any[] = []

  private translationSubscription?: Subscription
  // ✅ Subscription pour écouter les changements de langue
  private languageSubscription?: Subscription

  currentLanguage = "an.json"

  constructor(
    private actionMarketingService: ActionMarketingService,
    private translationService: TranslationService,
    private themeService: ThemeService,
    private LoginService: LoginService,
    private formBuilder: FormBuilder,
  ) {}

  async ngOnInit() {
    console.log("🚀 Initialisation MarketingActionsComponent")

    // ✅ S'abonner aux changements de traductions
    this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
      console.log("🔄 MarketingActions - Traductions mises à jour:", translations)
      this.translations = translations
    })

    // ✅ S'abonner aux changements de langue
    this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
      console.log("🌐 MarketingActions - Langue changée vers:", language)
      this.currentLanguage = language
    })

    // Charger les données
    await this.loadData()
    this.initializeForm()

    console.log("✅ MarketingActions - Initialisé avec langue:", this.translationService.getCurrentLanguage())
  }

  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    // ✅ Nettoyer la subscription de langue
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
  }

  // ✅ Changement de langue simplifié - utilise le service centralisé
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 MarketingActions - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ MarketingActions - Langue changée avec succès")
    } catch (error) {
      console.error("❌ MarketingActions - Erreur changement langue:", error)
    }
  }

  private initializeForm(): void {
    this.marketingForm = this.formBuilder.group({
      marketingActionId: [null],
      marketingActionName: ["", Validators.required],
      budget: ["", [Validators.required, Validators.pattern("^[0-9]*$")]],
      productIds: [[]],
      prospectIds: [[]],
      selectedDate: ["", Validators.required],
      description: [""],
    })
  }

  async loadData() {
    await this.getAllMarketingActions()
    await this.loadNextActionRules()
    await this.getAllProducts()
    await this.getAllProspects()
  }

  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ MarketingActions - Traduction manquante: ${key}`)
    }
    return translation || key
  }

  async getAllMarketingActions() {
    try {
      this.marketing_action = await this.actionMarketingService.getAllMarketingActions()
      console.log("Marketing actions loaded:", this.marketing_action)
    } catch (err) {
      console.error("Error fetching marketing actions", err)
    }
  }

  async getAllProducts() {
    try {
      this.products = await this.actionMarketingService.getAllProducts()
    } catch (err) {
      console.error("Unable to retrieve products", err)
      this.products = []
    }
  }

  async getAllProspects() {
    try {
      this.prospects = await this.actionMarketingService.getAllProspects()
    } catch (err) {
      console.error("Unable to retrieve prospects", err)
      this.prospects = []
    }
  }

  displayAddForm() {
    this.marketingForm.reset()
    this.displayForm = true
  }

  async save() {
    if (this.marketingForm.valid) {
      const formData = this.marketingForm.value
      const date = new Date(formData.selectedDate).getTime()

      try {
        if (formData.marketingActionId) {
          await this.actionMarketingService.updateMarketingAction(
            formData.marketingActionId,
            formData.marketingActionName,
            Number.parseInt(formData.budget),
            formData.productIds.join(","),
            formData.prospectIds.join(","),
            formData.description,
            date,
          )
        } else {
          await this.actionMarketingService.addMarketingAction(
            formData.marketingActionName,
            Number.parseInt(formData.budget),
            formData.productIds.join(","),
            formData.prospectIds.join(","),
            formData.description,
            date,
          )
        }

        this.displayForm = false
        await this.getAllMarketingActions()
        this.errorMessage = ""
      } catch (error) {
        console.error("Erreur sauvegarde:", error)
        this.errorMessage = "Erreur lors de la sauvegarde"
      }
    } else {
      this.errorMessage = "Veuillez remplir tous les champs requis."
      console.log("Form is invalid")
    }
  }

  cancel() {
    this.displayForm = false
    this.errorMessage = ""
  }

  editAction(action: any) {
    const date = new Date(action.marketingAction_date)
    const formattedDate = date.toISOString().split("T")[0]

    this.marketingForm.patchValue({
      marketingActionId: action.id,
      selectedDate: formattedDate,
      marketingActionName: action.name,
      budget: action.budget.toString(),
      productIds: action.product_id.split(","),
      prospectIds: action.prospect_id.split(","),
      description: action.description,
    })

    if (
      !action.name ||
      !action.budget ||
      !action.product_id ||
      !action.prospect_id ||
      !action.marketingAction_date ||
      !action.description
    ) {
      this.errorMessage = "Veuillez remplir tous les champs requis avant de continuer."
    } else {
      this.errorMessage = ""
    }

    this.displayForm = true
  }

  async deleteAction(id: number) {
    try {
      await this.actionMarketingService.deleteMarketingAction(id)
      await this.getAllMarketingActions()
    } catch (error) {
      console.error("Erreur suppression:", error)
    }
  }

  logout() {
    this.LoginService.logout()
  }

  onThemeChange(): void {
    this.themeService.switchTheme()
  }

  async loadNextActionRules() {
    try {
      this.nextActionRules = await this.actionMarketingService.getNextActionRules()
    } catch (error) {
      console.error("Erreur chargement règles:", error)
      this.nextActionRules = []
    }
  }
}
