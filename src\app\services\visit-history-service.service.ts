import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { DateService } from './date.service';
import { CommonService } from "./common-service.service";
import { LoadingController } from '@ionic/angular';
import { LoggerService } from 'src/app/services/logger.service';
import { CapacitorSQLite, SQLiteDBConnection } from '@capacitor-community/sqlite';
import { HttpClient } from '@angular/common/http';
import { enLanguage } from 'src/app/models/enLanguage';
@Injectable({
  providedIn: 'root'
})
export class VisitHistoryService {
  [x: string]: any;
  visitsHistory: any = {};
  noVisits: boolean = true;
  lockAfterSync: boolean = false;
  lockAfterPeriod: boolean = false;
  currentUser: any = {}; // Assuming this holds the current user data
  selectedWholesaler: any = {};
  rootScope: any; // Assuming this contains general settings/messages
  MESSAGES = enLanguage.MESSAGES;
  private db!: SQLiteDBConnection;

  constructor(
    private http: HttpClient,
    private sqliteService: SQLiteService,
    private dateService: DateService,
    private loadingController: LoadingController,
    private commonService: CommonService,
    private logger: LoggerService,
   
    
  ) {}

  // Initialize the database connection
  async initDB(db: SQLiteDBConnection): Promise<void> 
  {
    this.db = db;
  }


  async getRecapTableData(
    selectedWholesaler: any,
    showOrders: boolean,
    showVisits: boolean,
    startDate: Date,
    endDate: Date,
    userId: number
  ): Promise<any> {
    const revenueBySpecialities: any[] = [];
    const start_date_timestamp = startDate.setHours(0, 0, 0, 0);
    const end_date_timestamp = endDate.setHours(23, 59, 59, 0);

    // Query to get revenue by specialties
    if (showOrders) {
      let query = `
        SELECT SUM(p.price * vp.order_quantity) AS sellingPrice,
               SUM(p.buying_price * vp.order_quantity) AS buyingPrice,
               sp.name AS specialityName
        FROM visit_product vp
        INNER JOIN product p ON vp.product_id = p.id
        INNER JOIN visit v ON v.id = vp.visit_id
        INNER JOIN prospect pr ON v.prospect_id = pr.id
        INNER JOIN speciality sp ON pr.speciality_id = sp.id
      `;

      if (selectedWholesaler && selectedWholesaler.id) {
        query += ` INNER JOIN purchase_order po ON po.visit_id = v.id `;
      }

      query += `
        WHERE v.user_id = ? AND v.status <> 'DELETED'
          AND v.visit_date >= ? AND v.visit_date <= ?
          AND (sp.action % 2 = 1 OR sp.action = 4 OR sp.action = 6 OR sp.action = 12 OR sp.action = 14)
      `;

      const params: any[] = [userId, start_date_timestamp, end_date_timestamp];

      if (selectedWholesaler && selectedWholesaler.id) {
        query += ` AND po.wholesaler_id = ? `;
        params.push(selectedWholesaler.id);
      }

      query += ` GROUP BY sp.name`;

      try {
        const result = await this.db.query(query, params);
        if (result.values) {
          result.values.forEach((row: any) => {
            revenueBySpecialities.push(row);
          });
        }
      } catch (err) {
        console.error('Error in revenueBySpecialities query:', err);
        throw err;
      }
    }

    // Queries for visit and order counts
    let countVisitQuery = `
      SELECT COUNT(DISTINCT v.id) AS countVisit
      FROM visit v
      INNER JOIN visit_product vp ON vp.visit_id = v.id
    `;

    const paramsCountVisit: any[] = [start_date_timestamp, end_date_timestamp, userId];

    if (selectedWholesaler && selectedWholesaler.id) {
      countVisitQuery += ` INNER JOIN purchase_order po ON po.visit_id = v.id `;
    }

    countVisitQuery += `
      WHERE v.status <> 'DELETED' AND v.visit_date >= ? AND v.visit_date <= ? AND v.user_id = ?
    `;

    if (showOrders && !showVisits) {
      countVisitQuery += ` AND (vp.purchase_order_id IS NOT NULL AND vp.purchase_order_id <> 0) `;
    }

    if (!showOrders && showVisits) {
      countVisitQuery += ` AND (vp.purchase_order_id IS NULL OR vp.purchase_order_id = 0) `;
    }

    if (selectedWholesaler && selectedWholesaler.id) {
      countVisitQuery += ` AND po.wholesaler_id = ? `;
      paramsCountVisit.push(selectedWholesaler.id);
    }

    let countVisitResult: any = 0;
    try {
      const result = await this.db.query(countVisitQuery, paramsCountVisit);
      if (result.values && result.values.length > 0) {
        countVisitResult = result.values[0].countVisit;
      }
    } catch (err) {
      console.error('Error in countVisit query:', err);
      throw err;
    }

    // Queries for orders, samples, and prescriptions
    let countQuery = `
      SELECT SUM(vp.order_quantity) AS orders, 
             SUM(vp.sample_quantity) AS samples, 
             SUM(vp.prescription_quantity) AS prescription
      FROM visit v
      INNER JOIN visit_product vp ON vp.visit_id = v.id
    `;

    const paramsCount: any[] = [start_date_timestamp, end_date_timestamp, userId];

    if (selectedWholesaler && selectedWholesaler.id) {
      countQuery += ` INNER JOIN purchase_order po ON po.visit_id = v.id `;
    }

    countQuery += `
      WHERE v.status <> 'DELETED' AND vp.status <> 'DELETED' 
        AND v.visit_date >= ? AND v.visit_date <= ? 
        AND v.user_id = ?
    `;

    if (showOrders && !showVisits) {
      countQuery += ` AND (vp.purchase_order_id IS NOT NULL AND vp.purchase_order_id <> 0) `;
    }

    if (!showOrders && showVisits) {
      countQuery += ` AND (vp.purchase_order_id IS NULL OR vp.purchase_order_id = 0) `;
    }

    if (selectedWholesaler && selectedWholesaler.id) {
      countQuery += ` AND po.wholesaler_id = ? `;
      paramsCount.push(selectedWholesaler.id);
    }

    let ordersResult: any = { orders: 0, samples: 0 };
    try {
      const result = await this.db.query(countQuery, paramsCount);
      if (result.values && result.values.length > 0) {
        ordersResult = result.values[0];
      }
    } catch (err) {
      console.error('Error in countQuery:', err);
      throw err;
    }

    return {
      revenueBySpecialities,
      countVisit: countVisitResult,
      orders: ordersResult.orders || 0,
      samples: ordersResult.samples || 0
    };
  }

  
async getAllProspects(keywordsFilter: string, selectedWholesaler: any, showVisits: boolean, showOrders: boolean, startDate: Date, endDate: Date, userId: number): Promise<any[]> {
  const prospects: any[] = [];

  prospects.push({
    id: undefined,
    name: 'All'  // Replace with your label
  });

  const start_date_timestamp = startDate.setHours(0, 0, 0, 0);
  const end_date_timestamp = endDate.setHours(23, 59, 59, 0);

  let query = `
    SELECT DISTINCT(p.id), p.firstname, p.lastname, sp.name as speciality
    FROM prospect p
    INNER JOIN visit v ON p.id = v.prospect_id
    INNER JOIN visit_product vp ON vp.visit_id = v.id
    INNER JOIN speciality sp ON sp.id = p.speciality_id
  `;

  if (selectedWholesaler && selectedWholesaler.id) {
    query += ` INNER JOIN purchase_order po ON po.visit_id = v.id `;
  }

  query += `
    WHERE (v.visit_date >= ? AND v.visit_date <= ?) AND (v.user_id = ?) AND v.status <> 'DELETED'
  `;

  const params: any[] = [start_date_timestamp, end_date_timestamp, userId];

  if (selectedWholesaler && selectedWholesaler.id) {
    query += ` AND (po.wholesaler_id = ?) `;
    params.push(selectedWholesaler.id);
  }

  if (keywordsFilter) {
    query += ` AND (p.firstname || ' ' || p.lastname LIKE ?) `;
    params.push(`%${keywordsFilter}%`);
  }

  if (showOrders && !showVisits) {
    query += ` AND (vp.purchase_order_id IS NOT NULL AND vp.purchase_order_id <> 0) `;
  }

  if (!showOrders && showVisits) {
    query += ` AND (vp.purchase_order_id IS NULL OR vp.purchase_order_id = 0) `;
  }

  query += ` GROUP BY p.id ORDER BY v.id, v.visit_date, p.firstname, p.lastname`;

  try {
    const result = await this.db.query(query, params);
    result.values?.forEach((row: any) => {
      prospects.push({
        id: row.id,
        name: `${row.firstname} ${row.lastname}`
      });
    });
  } catch (err) {
    console.error('Error fetching prospects:', err);
    // Optionally, handle error display
  } finally {
    
  }

  return prospects;
}

async testDatabaseConnection(): Promise<void> {
  try {
      // This is a simple query to check if the database connection works.
      const result = await this.db.query('SELECT 1');
      console.log('Database connection successful:', result);
  } catch (error) {
      console.error('Database connection failed:', error);
      throw error;
  }
}


  // Get all wholesalers from the database
  async getWholesalers(): Promise<any[]> {
    try {
      const result = await this.db.query('SELECT * FROM wholesaler'); // Selecting all columns
      return result.values?.map((row: any) => ({
        id: row.id, 
        // If the table doesn't have 'firstname' and 'lastname', use the actual column that exists (like 'name' or 'full_name').
        name: row.firstname && row.lastname ? `${row.firstname} ${row.lastname}` : row.name || row.full_name || 'Unknown', // Fallback logic
      })) || [];
    } catch (error) {
      console.error('Error fetching wholesalers:', error);
      throw error;
    }
  }
  updateVisitDate(visitId: number, newDate: Date): Promise<any> {
    const url = `${this['apiUrl']}/${visitId}/date`;
    return this.http.put(url, { newDate: newDate.toISOString() }).toPromise();
  }

  // Get the visits history for a prospect and a date range
  async getVisitsHistory(prospectSelectedId: number, startDate: any, endDate: any): Promise<any> {
    this.logger.info(`function : getVisitsHistory with parameter prospectSelectedId: ${prospectSelectedId}`);

    const loading = await this.loadingController.create({
      message: 'Loading..',
    });
    await loading.present();
    this.visitsHistory = {};

    let start_date_timestamp: number | null = null;
    let end_date_timestamp: number | null = null;

    if (startDate != null) {
      start_date_timestamp = new Date(startDate).setHours(0, 0, 0, 0);
    }

    if (endDate != null) {
      end_date_timestamp = new Date(endDate).setHours(23, 59, 59, 0);
    }

    let selectVisitQuery = `
      SELECT p.name as productName, v.id as visitId, v.general_note, 
             pr.firstname || ' ' || pr.lastname as prospectName, s.name as specialityName, 
             l.name as localityName, v.prospect_id, v.user_id, v.synchronized, 
             v.user_Name, v.visit_date, vp.prescription_quantity, vp.comment, vp.visit_id, 
             vp.sample_quantity, vp.order_quantity, vp.sale_quantity, vp.freeOrder, 
             vp.lab_gratuity, vp.id as visitProductId
      FROM visit v 
      INNER JOIN visit_product vp ON vp.visit_id = v.id 
      LEFT JOIN product p ON vp.product_id = p.id 
      JOIN Prospect pr ON v.prospect_id = pr.id 
      JOIN speciality s ON pr.speciality_id = s.id 
      LEFT JOIN locality l ON pr.locality_id = l.id
    `;

    // Build the WHERE clause
    let whereClause = '';
    if (start_date_timestamp != null) {
      console.log("start_date_timestame", start_date_timestamp)
      whereClause += ` v.visit_date >= ${start_date_timestamp}`;
    }
    console.log("whereClause", whereClause)
    if (end_date_timestamp != null) {
      if (whereClause !== '') {
        whereClause += ' AND ';
      }
      whereClause += ` v.visit_date <= ${end_date_timestamp}`;
    }

    if (prospectSelectedId !== undefined) {
      if (whereClause !== '') {
        whereClause += ' AND ';
      }
      whereClause += ` v.prospect_id = ${prospectSelectedId}`;
    }

    if (whereClause !== '') {
      whereClause += ' AND ';
    }
    whereClause += ` vp.status <> 'DELETED' and v.status <> 'DELETED' `;

    selectVisitQuery += ` WHERE ${whereClause} ORDER BY visit_date, v.prospect_id, rank `;

    try {
      console.log(selectVisitQuery);
      const visitResultSet = await this.db.query(selectVisitQuery, []);
      console.log("visitResultSet", visitResultSet)
      if (visitResultSet.values && visitResultSet.values.length > 0) {
        this.noVisits = false;
      }

      // Loop through results and populate visitsHistory
      visitResultSet.values?.forEach((item: any) => {
        const dateKey = item.visit_date;
        const prospectName = item.prospectName;

        if (!this.visitsHistory[dateKey]) {
          this.visitsHistory[dateKey] = {};
        }

        if (!this.visitsHistory[dateKey][prospectName]) {
          this.visitsHistory[dateKey][prospectName] = {
            visit: [],
            pos: {},
            data: {},
            blocked: false
          };
        }

        const date = new Date(dateKey);
        this.lockAfterSync = false;

        // Lock visit if synchronized or locked after the period
        if (this.currentUser.lock_after_sync === 'true' && item.synchronized) {
          this.lockAfterSync = true;
        }

        // Check for locked periods (assuming this function returns a boolean)
        const isPeriodLocked = this.commonService.checkOpenPeriod(date, 'REPORT');
        if (this.lockAfterPeriod || this.lockAfterSync || isPeriodLocked) {
          this.visitsHistory[dateKey][prospectName].blocked = true;
        }

        // Populate visits data
        this.visitsHistory[dateKey][prospectName].visit.push(item);

        const prospect = {
          prospectName: item.prospectName,
          localityName: item.localityName,
          specialityName: item.specialityName,
          generalNote: item.general_note,
          visitId: item.visitId
        };

        this.visitsHistory[dateKey][prospectName].data = prospect;
      });
      console.log("visitHistory", this.visitsHistory)
      return this.visitsHistory;

    } catch (error: any) {
      this.logger.info(error.message);
      throw error;
    } finally {
      await loading.dismiss();
    }
  }

  // Fetch PO history by prospect
  async getPoHistory(prospectSelectedId: number, startDate: any, endDate: any): Promise<any> {
    this.logger.info(`function : getPoHistory with parameter prospectSelectedId: ${prospectSelectedId}`);
    this.visitsHistory = {};
    const loading = await this.loadingController.create({
      message: this.MESSAGES.LOADING,
    });
    await loading.present();

    let start_date_timestamp: number | null = null;
    let end_date_timestamp: number | null = null;

    if (startDate != null) {
      start_date_timestamp = new Date(startDate).setHours(0, 0, 0, 0);
    }

    if (endDate != null) {
      end_date_timestamp = new Date(endDate).setHours(23, 59, 59, 0);
    }

    let selectPoQuery = `
      SELECT po.id as purchaseOrderId, p.name as productName, v.id as visitId, v.general_note, 
             pr.firstname || ' ' || pr.lastname as prospectName, s.name as specialityName, 
             l.name as localityName, v.prospect_id, v.user_id, v.synchronized, 
             v.user_Name, v.visit_date, vp.comment, vp.visit_id, vp.prescription_quantity, 
             vp.sample_quantity, vp.order_quantity, vp.sale_quantity, vp.freeOrder, 
             vp.lab_gratuity, vp.id as visitProductId, w.name as wholesalerName, 
             r.status as recoveryStatus, po.purchase_order_template_id as purchaseOrderTemplateId,po.status as poStatus
      FROM purchase_order po 
      INNER JOIN wholesaler w ON po.wholesaler_id = w.id 
      INNER JOIN visit v ON v.id = po.visit_id 
      INNER JOIN visit_product vp ON vp.purchase_order_id = po.id 
      LEFT JOIN product p ON vp.product_id = p.id 
      JOIN Prospect pr ON v.prospect_id = pr.id 
      JOIN speciality s ON pr.speciality_id = s.id 
      LEFT JOIN locality l ON pr.locality_id = l.id 
      LEFT OUTER JOIN recovery r ON r.purchase_order_id = po.id
    `;

    // Build the WHERE clause
    let whereClause = '';
    if (start_date_timestamp != null) {
      whereClause += ` v.visit_date >= ${start_date_timestamp}`;
    }
    if (end_date_timestamp != null) {
      if (whereClause !== '') {
        whereClause += ' AND ';
      }
      whereClause += ` v.visit_date <= ${end_date_timestamp}`;
    }

    if (prospectSelectedId !== undefined) {
      if (whereClause !== '') {
        whereClause += ' AND ';
      }
      whereClause += ` v.prospect_id = ${prospectSelectedId}`;
    }

    if (this.selectedWholesaler?.id != null) {
      whereClause += ` AND po.wholesaler_id = ${this.selectedWholesaler.id}`;
    }

    if (whereClause !== '') {
      whereClause += ' AND ';
    }
    whereClause += ` vp.status <> 'DELETED' and v.status <> 'DELETED' `;

    selectPoQuery += ` WHERE ${whereClause} GROUP BY visitProductId ORDER BY visit_date, v.prospect_id, rank `;

    try {
      console.log("selectPOquery",selectPoQuery);
      const poResultSet = await this.db.query(selectPoQuery, []);
      console.log("POresult",poResultSet);
      if (poResultSet.values && poResultSet.values.length > 0) {
        this.noVisits = false;
      }

      poResultSet.values?.forEach((item: any) => {
        const dateKey = item.visit_date;
        const prospectName = item.prospectName;
        const purchaseOrderId = item.purchaseOrderId;

        if (!this.visitsHistory[dateKey]) {
          this.visitsHistory[dateKey] = {};
        }

        if (!this.visitsHistory[dateKey][prospectName]) {
          this.visitsHistory[dateKey][prospectName] = {
            visit: [],
            pos: {},
            data: {},
            blocked: false
          };
        }

        const date = new Date(dateKey);
        this.lockAfterSync = false;
        if (this.currentUser.lock_after_sync === 'true' && item.synchronized) {
          this.lockAfterSync = true;
        }

        const isPeriodLocked = this.commonService.checkOpenPeriod(date, 'REPORT');
        if (this.lockAfterPeriod || this.lockAfterSync || isPeriodLocked) {
          this.visitsHistory[dateKey][prospectName].blocked = true;
        }

        if (!this.visitsHistory[dateKey][prospectName].pos[purchaseOrderId]) {
          this.visitsHistory[dateKey][prospectName].pos[purchaseOrderId] = [];
        }

        this.visitsHistory[dateKey][prospectName].pos[purchaseOrderId].push(item);
        const prospect = {
          prospectId: item.prospect_id,
          prospectName: item.prospectName,
          localityName: item.localityName,
          specialityName: item.specialityName,
          generalNote: item.general_note,
          recoveryStatus: item.recoveryStatus,
          purchaseOrderTemplateId: item.purchase_order_template_id
        };
        this.visitsHistory[dateKey][prospectName].data = prospect;
      });
      return(this.visitsHistory)
    } catch (error: any) {
      this.logger.info(error.message);
      throw error;
    } 
    finally {
      await loading.dismiss();
    }
  }

  // Fetch PO history by prospect (simple retrieval based on prospect and wholesaler)
  async getPoHistoryByProspect(prospectId: number, wholesalerId: number): Promise<any> {
    try {
      const query = `
        SELECT * FROM purchase_order WHERE prospect_id = ? AND wholesaler_id = ?
      `;
      const result = await this.db.query(query, [prospectId, wholesalerId]);
      return result.values || [];
    } catch (error) {
      console.error('Error fetching PO history:', error);
      throw error;
    }
  }

  // Fetch visit history by prospect
  async getVisitsHistoryByProspect(prospectId: number, wholesalerId: number): Promise<any> {
    try {
      const query = `
        SELECT * FROM visit WHERE prospect_id = ? AND wholesaler_id = ?
      `;
      const result = await this.db.query(query, [prospectId, wholesalerId]);
      return result.values || [];
    } catch (error) {
      console.error('Error fetching visit history by prospect:', error);
      throw error;
    }
  }
}
