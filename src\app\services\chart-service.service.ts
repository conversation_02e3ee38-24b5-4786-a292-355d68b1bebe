import { Injectable } from '@angular/core';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { Chart, ChartConfiguration } from 'chart.js';
import { LoggerService } from 'src/app/services/logger.service';
import { SQLiteService } from './sqlite.service';

@Injectable({
  providedIn: 'root'
})
export class ChartService {
  sqlite: any;
  private mDb!: SQLiteDBConnection;
  constructor(private sqliteService: SQLiteService, private Logger: LoggerService) { }

  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
}
  async drawVisitedVsAllProspectChart(chartDivId: string, startDateTimestamp: number, endDateTimestamp: number, userId: number, showOrders: boolean, showVisits: boolean) {
    let visitedProspectCount = 0;
    let allProspectCount = 0;
    const canvas = document.getElementById(chartDivId) as HTMLCanvasElement;
    if (canvas) {
      // Destroy the existing chart before creating a new one
      Chart.getChart(chartDivId)?.destroy();
    }

    try {

      

      const allProspectsResult = await this.mDb.query(
        `SELECT COUNT(p.id) as prospectCount 
         FROM prospect p 
         WHERE p.status <> 'DELETED'`
      );

      if (allProspectsResult && allProspectsResult.values && allProspectsResult.values.length > 0) {
        allProspectCount = allProspectsResult.values[0].prospectCount;
      }

      let countVisitQuery = `
        SELECT COUNT(DISTINCT v.prospect_id) as visitedProspectCount 
        FROM visit v 
        INNER JOIN visit_product vp ON vp.visit_id = v.id 
        WHERE v.status <> 'DELETED' 
          AND v.visit_date >= ? 
          AND v.visit_date <= ? 
          AND v.user_id = ?`;

      if (showOrders && !showVisits) {
        countVisitQuery += " AND (vp.purchase_order_id IS NOT NULL AND vp.purchase_order_id <> 0)";
      }
      if (!showOrders && showVisits) {
        countVisitQuery += " AND (vp.purchase_order_id IS NULL OR vp.purchase_order_id = 0)";
      }

      const visitResult = await this.mDb.query(countVisitQuery, [startDateTimestamp, endDateTimestamp, userId]);
      if (visitResult && visitResult.values && visitResult.values.length > 0) {
        visitedProspectCount = visitResult.values[0].visitedProspectCount;
      }

      const chartDiv = document.getElementById(chartDivId) as HTMLCanvasElement | null;
      if (chartDiv) {
        const config: ChartConfiguration = {
          type: 'doughnut',
          data: {
            labels: ['Visited Prospects', 'Not Visited Prospects'],
            datasets: [{
              data: [visitedProspectCount, allProspectCount - visitedProspectCount],
              backgroundColor: ['#4b9841', '#c85200'],
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                display: true,
                labels: {
                  color: "#000000",
                }
              },
              tooltip: {
                callbacks: {
                  label: (tooltipItem: any): string => {
                    const data = tooltipItem.chart.data;
                    const label = data.labels[tooltipItem.dataIndex];
                    const value = data.datasets[0].data[tooltipItem.dataIndex];
                    let resultLabel = `${label}: ${value}`;
                    const total = data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
                    if (total !== 0) {
                      const percentage = (value * 100) / total;
                      resultLabel += ` (${percentage.toFixed(2)} %)`;
                    }
                    return resultLabel;
                  }
                }
              }
            }
          }
        };

        new Chart(chartDiv, config);
        
      }

    } catch (error) {
      this.Logger.info(`Error: ${(error as any).message}`);
    }
  } catch(error: any) {
    this.Logger.info(`Error: ${(error as any).message}`);
  }

  async drawPlannedVsAllProspectChart(chartDivId: string, startDateTimestamp: number, endDateTimestamp: number) {
    console.log("start date",startDateTimestamp);
    console.log("end date", endDateTimestamp);
    let countVisitsPlanned = 0;
    let allProspectCount = 0;

    try {
      

   

      const allProspectsResult = await this.mDb.query(
        "SELECT COUNT(p.id) as prospectCount FROM prospect p WHERE p.status <> 'DELETED'"
      );
      console.log('All Prospects Result:', allProspectsResult);

      if (allProspectsResult && allProspectsResult.values && allProspectsResult.values.length > 0) {
        allProspectCount = allProspectsResult.values[0].prospectCount;
      }

      const countVisitsPlannedQuery = `
        SELECT COUNT(pl.prospect_id) as countVisit 
        FROM planning pl 
        WHERE pl.status <> 'DELETED' 
          AND pl.planning_date >= ? 
          AND pl.planning_date <= ?`;
          
      const visitResult = await this.mDb.query(countVisitsPlannedQuery, [startDateTimestamp, endDateTimestamp]);
      console.log('countVisitsPlannedQuery Result:', countVisitsPlannedQuery);
      console.log('Visit Result:', visitResult)
      if (visitResult && visitResult.values && visitResult.values.length > 0) {
        countVisitsPlanned = visitResult.values[0].countVisit;

        const chartDiv = document.getElementById(chartDivId) as HTMLCanvasElement | null;
        if (chartDiv) {
          const config: ChartConfiguration = {
            type: 'doughnut',
            data: {
              labels: ['Planned Prospects', 'Not Planned Prospects'],
              datasets: [{
                data: [countVisitsPlanned, allProspectCount - countVisitsPlanned],
                backgroundColor: ['#4b9841', '#c85200'],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              plugins: {
                legend: {
                  display: true,
                  labels: {
                    color: "#000000",
                  }
                }
              }
            }
          };

          new Chart(chartDiv, config);
        }
      }

      
    } catch (error) {
      this.Logger.info(`Error: ${(error as any).message}`);
    }
  }

  async drawPlanningChart(chartDivId: string, startDateTimestamp: number, endDateTimestamp: number, groupBy: string) {
    console.log("start date",startDateTimestamp);
    console.log("end date", endDateTimestamp);
    const prospectCountByPotential: number[] = [];
    const allPotentialsLabels: string[] = [];
    const potentialPlannedVisitsValues: number[] = [];
    const backgroundColors: string[] = [];

   

    try {
     

      let groupQuery = '';
      switch (groupBy) {
        case 'SPECIALITY':
          groupQuery = ', sp.name as label';
          break;
        case 'POTENTIAL':
          groupQuery = ', pt.name as label';
          break;
        case 'SECTOR':
          groupQuery = ', s.name as label';
          break;
        case 'ACTIVITY':
          groupQuery = ', p.activity as label';
          break;
      }

      const countProspectQuery = `
        SELECT COUNT(p.id) as prospectsCount ${groupQuery} 
        FROM prospect p 
        INNER JOIN speciality sp ON p.speciality_id = sp.id
        INNER JOIN potential pt ON p.potential = pt.id 
        INNER JOIN sector s ON p.sector_id = s.id 
        WHERE p.status <> 'DELETED' 
        GROUP BY label`;

      const prospectsResult = await this.mDb.query(countProspectQuery);
      console.log("queruy ",countProspectQuery);
      console.log('Prospects Result:', prospectsResult);
      if (prospectsResult && prospectsResult.values) {
        for (let j = 0; j < prospectsResult.values.length; j++) {
          allPotentialsLabels.push(prospectsResult.values[j].label);
          prospectCountByPotential.push(prospectsResult.values[j].prospectsCount);
        }

        const countPlannedVisitsByPotentialQuery = `
          SELECT COUNT(p.id) as prospectsCount ${groupQuery} 
          FROM planning pl 
          INNER JOIN prospect p ON pl.prospect_id = p.id
          INNER JOIN speciality sp ON p.speciality_id = sp.id
          INNER JOIN potential pt ON p.potential = pt.id 
          INNER JOIN sector s ON p.sector_id = s.id 
          WHERE pl.status <> 'DELETED' 
            AND pl.planning_date >= ? 
            AND pl.planning_date <= ? 
          GROUP BY label`;
       
        const plannedVisitsResult = await this.mDb.query(countPlannedVisitsByPotentialQuery, [startDateTimestamp, endDateTimestamp]);
        console.log(" query ",countPlannedVisitsByPotentialQuery);
        console.log('Planned Visits Result:', plannedVisitsResult);
        if (plannedVisitsResult && plannedVisitsResult.values) {
          for (let j = 0; j < plannedVisitsResult.values.length; j++) {
            const potential = plannedVisitsResult.values[j].label;
            const index = allPotentialsLabels.indexOf(potential);
            potentialPlannedVisitsValues[index] = plannedVisitsResult.values[j].prospectsCount;
            backgroundColors.push(this.dynamicColors());
            console.log(this.dynamicColors());

          }
        }

        if (allPotentialsLabels.length > 0 && prospectCountByPotential.length > 0 && potentialPlannedVisitsValues.length > 0) {
          const chartDiv = document.getElementById(chartDivId) as HTMLCanvasElement | null;
          console.log("chart ",chartDiv);
          if (chartDiv) {
            const config: ChartConfiguration = {
              type: 'bar',
              data: {
                labels: allPotentialsLabels,
                datasets: [
                  {
                    label: 'Number of Prospects in Portfolio',
                    data: prospectCountByPotential,
                    type: 'scatter', // Consider using 'bar' or 'line' if you face issues with 'scatter' for bars
                    backgroundColor: 'white',
                    borderColor: 'red',
                    borderWidth: 5,
                    // Make sure 'type' is correct, 'scatter' might not be appropriate for a bar chart
                  },
                  {
                    label: 'Number of Planned Prospects',
                    data: potentialPlannedVisitsValues,
                    type: 'bar',
                    backgroundColor: backgroundColors,
                  }
                ]
              },
              options: {
                responsive: true,
                plugins: {
                  legend: {
                    display: true,
                    labels: {
                      color: "#000000", // Color of the legend labels
                    }
                  },
                  tooltip: {
                    callbacks: {
                      label: function(context) {
                        let label = context.dataset.label || '';
                        if (label) {
                          label += ': ';
                        }
                        if (context.parsed.y !== null) {
                          label += new Intl.NumberFormat().format(context.parsed.y);
                        }
                        return label;
                      }
                    }
                  }
                },
                scales: {
                  x: {
                    stacked: true, // Stack bars if needed
                    title: {
                      display: true,
                      text: groupBy // Label for X-axis
                    }
                  },
                  y: {
                    stacked: true, // Stack bars if needed
                    title: {
                      display: true,
                      text: 'Count' // Label for Y-axis
                    }
                  }
                }
              }
            };

            new Chart(chartDiv, config);
          }
        }
      }
    } catch (error: any) {
      this.Logger.info(`Error: ${error.message}`);
    } finally {
    
    }
  }
  private dynamicColors(): string {
    const r = Math.floor(Math.random() * 255);
    const g = Math.floor(Math.random() * 255);
    const b = Math.floor(Math.random() * 255);
    return `rgb(${r},${g},${b})`;
  }
  
  async drawHistoryChart(
    chartDivId: string,
    start_date_timestamp: number,
    end_date_timestamp: number,
    userId: number,
    selectedKpi: any,
    groupBy: string,
    objectif: boolean,
    selectedType?: any,
    showOrders?: boolean,
    showVisits?: boolean
  ) {
  
    
    let selectQuery = '';
    let groupByColumn = '';
    let join = '';
    let selectedTypeQuery = '';
    const canvas = document.getElementById(chartDivId) as HTMLCanvasElement;
    if (canvas) {
      // Destroy the existing chart before creating a new one
      Chart.getChart(chartDivId)?.destroy();
    }
    // Determine the select query based on the selected KPI
    switch (selectedKpi.value) {
      case 'VISIT':
        selectQuery = 'SELECT COUNT(DISTINCT v.prospect_id) as countVisit, ';
        break;
      case 'REVENUE':
        selectQuery = 'SELECT SUM(prd.buying_price * vp.order_quantity) as countVisit, ';
        break;
      case 'ORDER':
        selectQuery = 'SELECT SUM(vp.order_quantity) as countVisit, ';
        break;
      case 'SAMPLE_QUANTITY':
        selectQuery = 'SELECT SUM(vp.sample_quantity) as countVisit, ';
        break;
      default:
        console.error('Unknown KPI type:', selectedKpi.value);
        return;
    }
  
    // Construct the GROUP BY clause and JOINs based on groupBy
    switch (groupBy) {
      case 'SPECIALITY':
        groupByColumn = 'sp.name';
        join = 'INNER JOIN speciality sp ON gi.speciality_id = sp.id';
        break;
      case 'POTENTIAL':
        groupByColumn = 'pt.name';
        join = 'INNER JOIN potential pt ON (gi.potential_id = pt.id)';
        break;
      case 'PRODUCT':
        groupByColumn = 'prd.name';
        join = 'INNER JOIN product prd ON (prd.id = gi.product_id)';
        break;
      case 'SECTOR':
        groupByColumn = 's.name';
        join = ' INNER JOIN sector s ON (gi.sector_id = s.id)';
        break;
      case 'PROSPECTTYPE':
        groupByColumn = 'pros.name';
        join = 'INNER JOIN prospect_type pros ON (gi.prospect_type_id = pros.id)';
        break;
      case 'ACTIVITY':
        groupByColumn = 'activity';
        join = '';
        break;
      default:
        console.error('Unknown groupBy value:', groupBy);
      
        return;
    }
  
    // Add filters based on selectedType
    if (selectedType?.type?.id !== undefined) {
      selectedTypeQuery = `AND p.type_id = ${selectedType.type.id}`;
    }
  
    // Construct the final SELECT query
    selectQuery += `${groupByColumn} as label 
    FROM visit v
    INNER JOIN visit_product vp ON v.id = vp.visit_id
    INNER JOIN product prd ON prd.id = vp.product_id
    INNER JOIN prospect p ON v.prospect_id = p.id
    INNER JOIN speciality sp ON p.speciality_id = sp.id
    INNER JOIN potential pt ON (p.potential = pt.id)
    INNER JOIN sector s ON (p.sector_id = s.id)  
    INNER JOIN prospect_type pros ON (pros.id = p.type_id)
    WHERE v.status <> 'DELETED'
    AND v.visit_date >= ?
    AND v.visit_date <= ?
    AND (v.user_id =?)`;
  
    if (selectedTypeQuery) {
      selectQuery += ` ${selectedTypeQuery}`;
    }
  
    if (showOrders && !showVisits) {
      selectQuery += ' AND (vp.purchase_order_id IS NOT NULL AND vp.purchase_order_id <> 0)';
    }
    if (!showOrders && showVisits) {
      selectQuery += ' AND (vp.purchase_order_id IS NULL OR vp.purchase_order_id = 0)';
    }
  
    selectQuery += ` GROUP BY ${groupByColumn}`;
  
    // Debugging: Log the constructed query
    console.log('Final SQL Query:', selectQuery);
  
    try {
      const result = await this.mDb.query(selectQuery,[start_date_timestamp, end_date_timestamp,userId]);
  
      const values: any = {};
      const backgroundColors: string[] = [];
  
      for (const row of result.values!) {
        const label = row.label;
        if (!values[label]) {
          values[label] = {};
        }
        values[label]['countVisit'] = row.countVisit;
        backgroundColors.push('#4994ff');
      }
  
      if (selectedKpi.value !== 'SAMPLE_QUANTITY' && objectif) {
        const goalQuery = `SELECT SUM(value) as goalValue, ${groupByColumn} as label 
          FROM goal_item gi
          ${join}
          INNER JOIN goal g ON g.id = gi.goal_id
          WHERE g.type = ?
          AND g.first_date <= ?
          AND g.last_date >= ?
          GROUP BY ${groupByColumn}`;
  
        const goalResult = await this.mDb.query(goalQuery, [selectedKpi.value, start_date_timestamp, end_date_timestamp]);
  
        for (const row of goalResult.values!) {
          const label = row.label;
          if (!values[label]) {
            values[label] = {};
          }
          values[label]['goalTarget'] = row.goalValue;
        }
      }
  console.log("selected kpi ", selectedKpi.label)
      this.updateChart(values, selectedKpi.label, backgroundColors, chartDivId);
  
    } catch (err: any) {
      console.error(`Error executing queries: ${err.message}`);

    }
  }
  
  
  
  
  updateChart(values: any, label: string, backgroundColors: string[], chartDivId: string) {
    const visitCountValues: number[] = [];
    const goalTargetValues: number[] = [];
  
    for (const key in values) {
      visitCountValues.push(values[key]['countVisit'] ?? 0);
      goalTargetValues.push(values[key]['goalTarget'] ?? 0);
    }
  
    const chartDiv = document.getElementById(chartDivId) as HTMLCanvasElement | null;
    if (chartDiv) {
      const config: ChartConfiguration = {
        type: 'bar',
        data: {
          labels: Object.keys(values),
          datasets: [
            {
              data: visitCountValues,
              label: label,
              backgroundColor: backgroundColors,
              borderWidth: 1,
            },
            {
              data: goalTargetValues,
              label: 'Goal',
              type: 'scatter',
              backgroundColor: 'white',
              borderColor: 'red',
              borderWidth: 5,
            },
          ],
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              display: true,
              labels: {
                color: "#000000",
              }
            }
          }
        }
      };
  
      new Chart(chartDiv, config);
    }
  }
}