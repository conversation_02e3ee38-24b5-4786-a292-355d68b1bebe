import { CommonModule } from "@angular/common"
import { Component, type OnInit } from "@angular/core"
import { FormsModule } from "@angular/forms"
import { Router } from "@angular/router"
import { IonicModule,  LoadingController,  ToastController } from "@ionic/angular"
import { LoginService } from "src/app/services/login-service.service"
import { SyncService } from "src/app/services/sync.service"

@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
  standalone: true,
  imports: [IonicModule, FormsModule, CommonModule],
})
export class LoginComponent implements OnInit {
  laboCode = ""
  credentials = { username: "", password: "" }
  isLaboCodeEntered = false
  isLoading = false
  errorMessage: string | null = null
  showPassword = false
  currentLaboInfo: { laboName: string; serverUrl: string } | null = null
  logoClickCount = 0
  logoClickTimer: any
  constructor(
    private loginService: LoginService,
    private router: Router,
    private syncService: SyncService,
    private loadingController: LoadingController,
    private toastController: ToastController,
  ) {}

  async ngOnInit() {
    await this.checkLaboConfiguration()
    await this.getStoredCredentials()
    const footer = document.querySelector(".global-footer") as HTMLElement
    if (footer) {
      footer.style.display = "none"
    }
  }
  ngOnDestroy() {
   
    const footer = document.querySelector(".global-footer") as HTMLElement
    if (footer) {
      footer.style.display = "block"
    }
  }

  private async getStoredCredentials() {
    try {
      const storedCredentials = await this.loginService.getStoredCredentials()
      if (storedCredentials) {
        this.credentials.username = storedCredentials.username
        this.credentials.password = storedCredentials.password
      }
    } catch (error) {
      console.error("Error loading stored credentials:", error)
    }
  }

  private async checkLaboConfiguration() {
    try {
      const isConfigured = await this.loginService.isLaboConfigured()
      if (isConfigured) {
        this.currentLaboInfo = await this.loginService.getCurrentLaboConfig()
        if (this.currentLaboInfo) {
          this.laboCode = this.currentLaboInfo.laboName
          this.isLaboCodeEntered = true
        }
      }
    } catch (error) {
      console.error("Error checking labo configuration:", error)
    }
  }

  async submitLaboCode() {
    if (!this.laboCode.trim()) {
      this.errorMessage = "Veuillez saisir un code de laboratoire"
      return
    }

    this.isLoading = true
    this.errorMessage = null

    const loading = await this.loadingController.create({
      message: "Validation du code laboratoire...",
      spinner: "crescent",
    })
    await loading.present()

    try {
      const success = await this.loginService.updateLaboInfo(this.laboCode)
      if (success) {
        this.currentLaboInfo = await this.loginService.getCurrentLaboConfig()
        this.isLaboCodeEntered = true
        await this.showToast("Configuration laboratoire mise à jour", "success")
      } else {
        this.errorMessage = "Code de laboratoire invalide"
      }
    } catch (error) {
      this.errorMessage = this.handleError(error)
    } finally {
      this.isLoading = false
      await loading.dismiss()
    }
  }

  async login() {
    if (!this.credentials.username.trim() || !this.credentials.password.trim()) {
      this.errorMessage = "Veuillez remplir tous les champs"
      return
    }

    this.isLoading = true
    this.errorMessage = null

    // Loading pour l'authentification
    const authLoading = await this.loadingController.create({
      message: "Connexion en cours...",
      spinner: "crescent",
    })
    await authLoading.present()

    try {
      // 1. Authentification
      const userDetails = await this.loginService.authenticate(this.credentials)
      await this.loginService.handleLoginSuccess(userDetails, this.credentials)
      
      await authLoading.dismiss()
      await this.showToast("Connexion réussie", "success")

      // 2. Redirection vers home
      await this.router.navigate(["/home"])

      // 3. Démarrer la synchronisation automatiquement
      await this.startDataReception()

    } catch (error) {
      await authLoading.dismiss()
      this.errorMessage = this.handleError(error)
    } finally {
      this.isLoading = false
    }
  }

  private async startDataReception() {
  

    try {
      // Appeler la méthode receive du SyncService
      await this.syncService.receive()
      
      
    } catch (error) {
      console.error("Erreur lors de la synchronisation:", error)
      await this.showToast("Erreur lors de la synchronisation des données", "danger")
    } finally {
     
    }
  }

  async goBackToLaboSelection() {
    try {
      await this.loginService.resetLaboConfiguration()
      this.isLaboCodeEntered = false
      this.laboCode = ""
      this.currentLaboInfo = null
      this.errorMessage = null
      await this.showToast("Configuration réinitialisée", "success")
    } catch (error) {
      console.error("Error resetting configuration:", error)
    }
  }

  toggleShowPassword() {
    this.showPassword = !this.showPassword
  }

  private handleError(error: unknown): string {
    if (error instanceof Error) {
      return error.message
    } else if (typeof error === "string") {
      return error
    } else {
      return "Une erreur inconnue est survenue"
    }
  }

  private async showToast(message: string, color: "success" | "danger" = "success") {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: "top",
    })
    await toast.present()
  }
  onLogoClick() {
    this.logoClickCount++

    // Reset le compteur après 2 secondes si pas assez de clics
    if (this.logoClickTimer) {
      clearTimeout(this.logoClickTimer)
    }

    this.logoClickTimer = setTimeout(() => {
      this.logoClickCount = 0
    }, 2000)

    // Redirection après 5 clics
    if (this.logoClickCount >= 5) {
      this.logoClickCount = 0
      if (this.logoClickTimer) {
        clearTimeout(this.logoClickTimer)
      }
      this.router.navigate(["/trouble-shooting"])
    }
  }
}
