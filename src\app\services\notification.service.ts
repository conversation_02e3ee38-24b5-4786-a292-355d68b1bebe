import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { Notification } from '../models/notifications';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { DbnameVersionService } from './dbname-version.service';
import { environment } from 'src/environments/environment';
import { mockNotification } from '../mock-data/notifications';
import { Observable, Observer } from 'rxjs';
import { TablesUpgrades } from '../upgrades/tables/tables';
import { DbCreationTablesService } from './db-creation-tables.service';
@Injectable()
export class NotificationService {
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;


  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
    private dbCreationTablesService: DbCreationTablesService,
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }
  
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    await this.createInitialData();
}
  async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService
      .addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
    // create and/or open the database
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);
    const isData = await this.mDb.query("select * from sqlite_sequence");
    // create database initial data
    if (isData.values!.length === 0) {
      await this.createInitialData();
    }
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, true, "secret",
          this.loadToVersion, false);

    } else {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, false, "no-encryption",
          this.loadToVersion, false);
    }
  }

  
  private async createInitialData(): Promise<void> {
   
  }
  
  getAllNotifications(): Observable<Notification[]> {
    const selectQuery = 'SELECT * FROM  notifications ORDER BY id DESC LIMIT 10;';
    
    return new Observable((observer: Observer<Notification[]>) => {
      console.log("database",this.mDb);
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const values = this.sqliteService.snakeToCamel(result.values)
          const notifications = values as Notification[];
          observer.next(notifications);
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }

  async getNotificationsByDate(day: number, month: number, year: number) {
    const selectQuery = `
          SELECT * FROM notifications
          WHERE notification_day = ? AND notification_month = ? AND notification_year = ?;
        `;
    const res = await this.mDb.query(selectQuery, [day, month, year]);
    const values = this.sqliteService.snakeToCamel(res.values)
    return values || [];
  }


  async getNotification(jsonNotification: Notification): Promise <Notification> {
    let notification = await this.sqliteService.findOneBy(this.mDb, "notifications", {id: jsonNotification.id});
    
    if (!notification) {

      if (jsonNotification.notificationText && jsonNotification.notificationDay && jsonNotification.notificationMonth  && jsonNotification.notificationYear) {
        // create a new author
        let notif = new Notification();
        notif.id = jsonNotification.id;
        notif.notificationText = jsonNotification.notificationText;
        notif.notificationMonth = jsonNotification.notificationMonth;
        notif.notificationYear = jsonNotification.notificationYear ;
        notif.notificationDay = jsonNotification.notificationDay;
        notif.status = jsonNotification.status;

        if (jsonNotification.notificationHour) {
          notif.notificationHour = jsonNotification.notificationHour;
        }
        if (jsonNotification.notificationMinutes) {
          notif.notificationMinutes = jsonNotification.notificationMinutes;
        }
        notif=this.sqliteService.camelToSnake(notif)
        await this.sqliteService.save(this.mDb, "notifications", notif);
        // cette etape pour verifier que l'element est bien sauvgarde dans la table 
        notif = await this.sqliteService.findOneBy(this.mDb, "notifications", { id: jsonNotification.id });
        if (notif) {
          return notif;
        } else {
          return Promise.reject(`failed to getNotification for id ${jsonNotification.id}`);
        }
      } else {
        // notification not in the database
        let notif = new Notification();
        notif.id = -1;
        return notif;
      }
    } else {
      return notification;
    } 
  }
 
 
}
    
