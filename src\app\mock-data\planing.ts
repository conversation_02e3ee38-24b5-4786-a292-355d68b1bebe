import { Planning, Prospect } from "../models/planing";

export const mockPlanning:Planning[]=[
    {
        id: 1,
        planningDate:new Date(2024,7,15).getTime(),
        prospectId: 2,
        synchronized: 0,
        status: "planned",
      },
      {
        id: 2,
        planningDate: new Date(2024,7,15).getTime(),
        prospectId: 1,
        synchronized: 1,
        status: "completed",
      },
      {
        id: 3,
        planningDate: new Date(2024,7,16).getTime(), 
        prospectId: 3,
        synchronized: 1,
        status: "in_progress",
      },
      {
        id: 4,
        planningDate: new Date(2024,7,16).getTime(),
        prospectId: 4,
        synchronized: 0,
        status: "planned",
      },
      {
        id: 5,
        planningDate: new Date(2024,7,18).getTime(), 
        prospectId: 1,
        synchronized: 0,
        status: "planned",
      }
    
    ]
    export const mockProspects: Prospect[] = [
        {
          id: 1,
          firstname: '<PERSON>',
          lastname: '<PERSON><PERSON>',
          activity: 'Medical Sales',
          potential: 75,
          address: '123 Elm Street',
          gsm: '+12345678901',
          phone: '+19876543210',
          email: '<EMAIL>',
          note: 'Interested in new products',
          secretary: '<PERSON>',
          grade: 'A',
          specialityId: 2,
          sectorId: 3,
          localityId: 10,
          lat: 40.712776,
          lng: -74.005974,
          mapAddress: 'New York, NY, USA',
          status: 'Active',
          synchronized: true,
          validation: 1,
          typeId: 1,
          establishmentId: 5
        },
        {
          id: 2,
          firstname: 'Alice',
          lastname: 'Johnson',
          activity: 'Pharmaceutical Distribution',
          potential: 85,
          address: '456 Oak Avenue',
          gsm: '+23456789012',
          phone: '+28765432109',
          email: '<EMAIL>',
          note: 'Requested more information on pricing',
          secretary: 'Bob Brown',
          grade: 'B',
          specialityId: 4,
          sectorId: 2,
          localityId: 15,
          lat: 34.052235,
          lng: -118.243683,
          mapAddress: 'Los Angeles, CA, USA',
          status: 'Active',
          synchronized: false,
          validation: 2,
          typeId: 2,
          establishmentId: 3
        },
        {
          id: 3,
          firstname: 'Michael',
          lastname: 'Williams',
          activity: 'Healthcare Consulting',
          potential: 90,
          address: '789 Pine Lane',
          gsm: '+34567890123',
          phone: '+37654321098',
          email: '<EMAIL>',
          note: 'Potential client for Q4',
          secretary: 'Sarah Green',
          grade: 'A',
          specialityId: 1,
          sectorId: 4,
          localityId: 8,
          lat: 41.878113,
          lng: -87.629799,
          mapAddress: 'Chicago, IL, USA',
          status: 'Inactive',
          synchronized: true,
          validation: 3,
          typeId: 1,
          establishmentId: 4
        },
        {
          id: 4,
          firstname: 'Emily',
          lastname: 'Davis',
          activity: 'Equipment Supply',
          potential: 65,
          address: '321 Birch Road',
          gsm: '+45678901234',
          phone: '+46543210987',
          email: '<EMAIL>',
          note: 'Interested in bulk purchase discounts',
          secretary: 'David White',
          grade: 'C',
          specialityId: 3,
          sectorId: 1,
          localityId: 20,
          lat: 29.760427,
          lng: -95.369804,
          mapAddress: 'Houston, TX, USA',
          status: 'Active',
          synchronized: false,
          validation: 1,
          typeId: 3,
          establishmentId: 2
        },
        {
          id: 5,
          firstname: 'Chris',
          lastname: 'Martinez',
          activity: 'Laboratory Services',
          potential: 80,
          address: '654 Cedar Street',
          gsm: '+56789012345',
          phone: '+54321098765',
          email: '<EMAIL>',
          note: 'Looking for collaboration opportunities',
          secretary: 'Laura Blue',
          grade: 'B',
          specialityId: 5,
          sectorId: 5,
          localityId: 12,
          lat: 33.448376,
          lng: -112.074036,
          mapAddress: 'Phoenix, AZ, USA',
          status: 'Inactive',
          synchronized: true,
          validation: 2,
          typeId: 2,
          establishmentId: 1
        }
      ];
      