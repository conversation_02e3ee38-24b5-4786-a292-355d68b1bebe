import { Component, ElementRef, Input, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ModalController } from '@ionic/angular';
import { Router } from '@angular/router';

// adjust this import to wherever your Prospect interface lives
import { Prospect } from 'src/app/models/planing';
import { enLanguage } from 'src/app/models/enLanguage';

@Component({
  selector: 'app-prospect-popup',
  standalone: true,
  imports: [CommonModule, IonicModule],
  templateUrl: './prospect-popup.component.html',
  styleUrls: ['./prospect-popup.component.scss'],
})
export class ProspectPopupComponent {
  /** the record you passed in when opening the modal */
  @Input() prospect!: Prospect;

  @ViewChild('mapContainer', { static: false })
  mapContainer!: ElementRef<HTMLDivElement>;
  LABELS = enLanguage.LABELS;

  /** map zoom level */
  zoom = 14;

  /** reuse your SVG pin */
  markerOptions: google.maps.MarkerOptions = {
    icon: {
      path: 'M0,0 C-2,-20 -10,-22 -10,-30 A10,10 0 1,1 10,-30 C10,-22 2,-20 0,0 z',
      fillColor: 'red',
      fillOpacity: 0.8,
      strokeWeight: 0
    }
  };

  constructor(
    private modalCtrl: ModalController,
    private router: Router
  ) {}

  ngAfterViewInit() {
    if (
      this.prospect.lat != null &&
      this.prospect.lng != null &&
      (window as any).google?.maps
    ) {
      setTimeout(() => this.renderMap(), 0);
    }
  }

  private renderMap() {
    const map = new google.maps.Map(this.mapContainer.nativeElement, {
      center: { lat: this.prospect.lat!, lng: this.prospect.lng! },
      zoom: this.zoom,
    });
    new google.maps.Marker({
      position: { lat: this.prospect.lat!, lng: this.prospect.lng! },
      map,
    });
  }

  dismiss() {
    this.modalCtrl.dismiss();
  }

  onEditProspect(id?: number) {
    // 1) close the popup
    this.modalCtrl.dismiss();
    // 2) navigate to detail page
    if (id != null) {
      // edit an existing prospect
      this.router.navigate(['/prospect-detail', id]);
    } else {
      // create a brand-new prospect
      this.router.navigate(['/prospect-detail']);
    }
  }
}
