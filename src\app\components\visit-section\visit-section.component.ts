import { Alert<PERSON>ontroller } from '@ionic/angular';
import { enLanguage } from 'src/app/models/enLanguage';
import { VisitHistoryService } from 'src/app/services/visit-history-service.service';
import { Component,Input, CUSTOM_ELEMENTS_SCHEMA, AfterViewInit , Inject, NO_ERRORS_SCHEMA, OnInit,ViewChild, ElementRef  } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router'; 
import { LoggerService } from 'src/app/services/logger.service'; 
import { SQLiteService } from 'src/app/services/sqlite.service';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { reportService } from 'src/app/services/report-service.service'; 
import { DateService } from 'src/app/services/date.service';
import { LoadingController, ModalController } from '@ionic/angular'; 
import { LocationService } from 'src/app/services/location-service.service';
import { HttpClient } from '@angular/common/http';
import { PopupService } from 'src/app/services/popup-service.service';
import { environment } from 'src/environments/environment';
import { CommonService } from 'src/app/services/common-service.service';
import { FilterService } from 'src/app/services/filterService.service';
import { COLORS, CODES, NAME_FILE } from 'src/app/constants';
import { ChangeDetectorRef, NgZone } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
//import { DatePicker, DatePickerOptions } from '@capacitor-community/date-picker';
import { VisitHistoryComponent } from 'src/app/components/visit-history/visit-history.component';

import { SafeResourceUrl } from '@angular/platform-browser';
import { ProspectService } from 'src/app/services/prospect-service.service';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { CameraService } from 'src/app/services/camera.service';
import { SyncService } from 'src/app/services/sync.service';
import { QUERIES } from 'src/app/models/constants';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { UserService } from 'src/app/services/user-service.service';
import { Prospect } from 'src/app/models/planing';
import { LoginService } from 'src/app/services/login-service.service';
import { Geolocation } from '@capacitor/geolocation';
import { CommonModule } from '@angular/common';
import { FormBuilder,Validators, FormGroup, FormsModule , ReactiveFormsModule} from '@angular/forms';
import { ThemeService } from 'src/app/services/theme.service';
import { DbCreationTablesService } from 'src/app/services/db-creation-tables.service';
import { FilterComponent } from '../filter/filter.component';
import { ProductPopupComponent } from '../product-popup/product-popup.component';
import { GoogleMapsLoaderService } from 'src/app/services/google-map-loader.service';


@Component({
  selector: 'app-visit-section',
  templateUrl: './visit-section.component.html',
  styleUrls: ['./visit-section.component.scss'],
  standalone: true,
  imports : [RouterModule ,IonicModule ,VisitHistoryComponent, CommonModule , FormsModule,ReactiveFormsModule, FilterComponent,VisitSectionComponent, ProductPopupComponent], 
  schemas :[CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class VisitSectionComponent  implements OnInit {
  @Input() selectedProspectId: number | null = null;
  LABELS = enLanguage.LABELS;
   // *** STRONGLY TYPE THIS INPUT ***
   @Input() visitsHistory!: Record<string,
   Record<string, {
     data: {
       prospectName: string;
       localityName: string | null;
       specialityName: string;
       generalNote: string;
       visitId: number;
       recoveryStatus?: string;
       prospectId: number;
     };
     visit: Array<{
       user_id: string;
       user_name: string;
       productName: string;
       comment: string;
       sample_quantity: number;
       prescription_quantity: number;
       sale_quantity: number;
       purchaseOrderTemplateId?: number | null;
     }>;
     pos: Record<string,
       Array<{
         user_id: string;
         user_name: string;
         productName: string;
         order_quantity: number;
         freeOrder: number;
         lab_gratuity: number;
         visit_id: number;
         prospect_id: number;
         wholesalerName: string;
         purchaseOrderTemplateId?: number | null;
       }>
     >;
     blocked: boolean;
   }>
 >;

  /** show/hide the “invalid date” banner */
  @Input() modifyDate: boolean = true;

  /** lower-bound period label, e.g. “3” (days after current date) */
  @Input() openReportPeriod!: string;
  /** mode flag (e.g. 'REPORT') to disable edits */
  @Input() historyIn!: string;

  /** current user’s ID for per-row permissions */
  @Input() userId!: string;
  constructor(
    private alertCtrl: AlertController,
    private visitHistoryService: VisitHistoryService,
  ) { }

  ngOnInit() {
    console.log("modifyDate",this.modifyDate)
  }
  // Add the changeVisitDate method
  deleteVisitProduct(
    idx: number,
    visitDate: string,
    prospectName: string,
    history: any,
    opId: string | number | null
  ): void {
    throw new Error('Method not implemented.');
}
deleteVisit(
  visit: any,
  visitDate: string,
  prospectName: string
): void {
  throw new Error('Method not implemented.');
}
deletePo(
  poId: string,
  visitId: string | number,
  purchaseOrder: any,
  visitDate: string,
  prospectName: string
): void {
  throw new Error('Method not implemented.');
}
async changeVisitDate(visitDate: any, visitId: number) {
  const alert = await this.alertCtrl.create({
    header: 'Change Visit Date',
    inputs: [
      {
        name: 'newDate',
        type: 'date',
        // Cast visitDate to a string to resolve the error
        value: new Date(visitDate as string).toISOString().substring(0, 10),
      },
    ],
    buttons: [
      {
        text: 'Cancel',
        role: 'cancel',
      },
      {
        text: 'OK',
        handler: async (data) => {
          const newDate = new Date(data.newDate);
          try {
            await this.visitHistoryService.updateVisitDate(visitId, newDate);
          } catch (error) {
            console.error('Error updating visit date:', error);
          }
        },
      },
    ],
  });
  await alert.present();
}

}
