import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { takeUntil } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { LoginService } from 'src/app/services/login-service.service';
import { ThemeService } from 'src/app/services/theme.service';
import { NextActionRule } from 'src/app/models/next-action-rule';
import { NextActionRuleService } from 'src/app/services/next-action-rule.service';
import { HeaderComponent } from '../../header/header.component';

@Component({
  selector: 'app-next-action-rule',
  templateUrl: './next-action-rule.component.html',
  styleUrls: ['./next-action-rule.component.scss'],
  standalone: true, 
  imports: [IonicModule, FormsModule, CommonModule,HeaderComponent]
})
export class NextActionRuleComponent implements OnInit {
  isDarkTheme = false;
  nextActionRules: NextActionRule[] = [];
  destroy$ = new Subject<void>();
  private translationSubscription?: Subscription
  private languageSubscription?: Subscription
  currentLanguage = "an.json"
  translations: any = {};
  
  constructor(
    private themeService: ThemeService,
    private loginService: LoginService,
    private nextActionRuleService: NextActionRuleService,
    private translationService: TranslationService
  ) {}
  
  ngOnInit() {
    this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
      console.log("🔄 ActivityCalender - Traductions mises à jour:", translations)
      this.translations = translations
    })
  
    // ✅ S'abonner aux changements de langue
    this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
      console.log("🌐 ActivityCalender - Langue changée vers:", language)
      this.currentLanguage = language
    })
    this.getAllNextActionRules();

    
  }
  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
    this.destroy$.next()
    this.destroy$.complete()
  }

  getAllNextActionRules() {
    this.nextActionRuleService.getAllNextActionRules()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (nextActionRules: NextActionRule[]) => this.nextActionRules = nextActionRules,
        error: (error: any) => console.error('Erreur lors de la récupération des règles d\'action suivantes:', error)
      });
  }
  
 
  
  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ next-action - Traduction manquante: ${key}`)
    }
    return translation || key
  }
  
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 next-action - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ next-action - Langue changée avec succès")
    } catch (error) {
      console.error("❌ next-action - Erreur changement langue:", error)
    }
  }
  
  private loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;
      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  }
  
  logout() {
    this.loginService.logout();
  }
  
  onThemeChange(): void {
    this.themeService.switchTheme();
  }
}