import { Component, CUSTOM_ELEMENTS_SCHEMA, AfterViewInit , Inject, NO_ERRORS_SCHEMA, OnInit,ViewChild, ElementRef  } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router'; 
import { LoggerService } from 'src/app/services/logger.service'; 
import { SQLiteService } from 'src/app/services/sqlite.service';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { IonicSafeString } from '@ionic/angular';
import { reportService } from 'src/app/services/report-service.service'; 
import { DateService } from 'src/app/services/date.service';
import { LoadingController, ModalController } from '@ionic/angular'; 
import { LocationService } from 'src/app/services/location-service.service';
import { HttpClient } from '@angular/common/http';
import { PopupService } from 'src/app/services/popup-service.service';
import { environment } from 'src/environments/environment';
import { CommonService } from 'src/app/services/common-service.service';
import { FilterService } from 'src/app/services/filterService.service';
import { enLanguage } from 'src/app/models/enLanguage';
import { COLORS, CODES, NAME_FILE } from 'src/app/constants';
import { ChangeDetectorRef, NgZone } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
//import { DatePicker, DatePickerOptions } from '@capacitor-community/date-picker';
import { VisitHistoryService } from 'src/app/services/visit-history-service.service';
import { VisitHistoryComponent } from 'src/app/components/visit-history/visit-history.component';

import { AlertController } from '@ionic/angular';
import { SafeResourceUrl } from '@angular/platform-browser';
import { ProspectService } from 'src/app/services/prospect-service.service';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { CameraService } from 'src/app/services/camera.service';
import { SyncService } from 'src/app/services/sync.service';
import { QUERIES } from 'src/app/models/constants';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { UserService } from 'src/app/services/user-service.service';
import { Prospect } from 'src/app/models/planing';
import { LoginService } from 'src/app/services/login-service.service';
import { Geolocation } from '@capacitor/geolocation';
import { CommonModule } from '@angular/common';
import { FormBuilder,Validators, FormGroup, FormsModule , ReactiveFormsModule} from '@angular/forms';
import { ThemeService } from 'src/app/services/theme.service';
import { DbCreationTablesService } from 'src/app/services/db-creation-tables.service';
import { FilterComponent } from '../filter/filter.component';
import { VisitSectionComponent } from '../visit-section/visit-section.component';
import { ProductPopupComponent } from '../product-popup/product-popup.component';
import { GoogleMapsLoaderService } from 'src/app/services/google-map-loader.service';
import { ImageModalComponent } from '../image-modal/image-modal.component';

import { HeaderComponent } from '../../header/header.component';










interface Product {
  product_id: number;
  name: string;
}

interface SelectedType {
  id: number | null;
  name: string;
}
type PurchaseOrder = {
  attachment?: {
    name: string;
    base64Image: string;
  };
  isPurchaseOrderUpdated: boolean;
};





@Component({
  selector: 'app-report',
  templateUrl: './report.component.html',
  styleUrls: ['./report.component.scss'],
  standalone : true ,
  
  imports : [HeaderComponent,RouterModule,IonicModule ,VisitHistoryComponent, CommonModule , FormsModule,ReactiveFormsModule, FilterComponent,VisitSectionComponent, ProductPopupComponent,ImageModalComponent], 
  schemas :[CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ReportComponent  implements OnInit {
  @ViewChild('productComment', { static: false }) productCommentRef!: ElementRef;
  @ViewChild('mapContainer', { static: false }) mapContainer!: ElementRef;
  @ViewChild('mapContainerBottom', { static: false }) mapContainerBottom!: ElementRef;
  @ViewChild('weekPicker', { static: false }) weekPicker!: any;

  
  reportForm!: FormGroup;
  _sectorSelected: any;
  _localitySelected: any;
  _establishmentSelected: any;
  _specialitySelected: any;
  _potentialSelected: any;
  _activitySelected: any;
localities : any =[];
   specialities :any  = [];
   potentials:any = [];
  activities:any = [];
  sectors:any = [];
  notSavedVisitData: boolean = false;
  notSavedData: boolean = false; 
  notSavedPoData: boolean = false;
  notSavedPo: boolean = false; 
  notSavedLocationData: boolean = false;
  notSavedLocation: boolean = false; 
  payments: Array<{ id: string; value: string }> = []; 
  selectedTab: string = '';
  visitDate: Date | undefined;
  weekMondayDay: Date = this.dateService.getMondayOfWeek(); 
  purchaseOrderTemplateItem: any[] = [];
  isPurchaseOrderTemplate: boolean = false;
  showLoading: boolean = false;
  googleMapIsAvailable: boolean = true;
  showDetailedProspectView: boolean = false;
  products: any[] = [];
  selectedProducts: any[] = [];
  orders: any[] = [];
  currentUser: any;
  commentsDictionary: string[] = [];
  openReportPeriod: any;
  visitData: { 
    generalNote: string ; 
    patientNumber: number; 
    selectedGadget: any | null; 
    gadgetQuantity: number; 
    selectedCompanion: any | null; 
    selectedContactType: any | null; // Nouvelle ligne
} = { 
    generalNote: '', 
    patientNumber: 0, 
    selectedGadget: null, 
    gadgetQuantity: 0, 
    selectedCompanion: null ,
    selectedContactType: null
};
  ranges: any[] = [];
  gadgets: any[] = [];
  wholesalers: any[] = [];
  users: any[] = [];
  delegates: any[] = [];
  showHistory = false;
  visitsHistory: Record<string, any> = {};
  selectedProspect! : Prospect;
  showPoHistory = false;
  showMap = false; 
  showFilters = false;
  establishments: any[] = [];
  prospects: any[] = [];
  orderSelected: any;
  isListOfVisitedProspects = false;
  LABELS = enLanguage.LABELS;
  filtredUsers: any[] = [];
  selectedUser: any = null;
  userSearchText: string = '';
  visit: any = null; 
  purchaseOrders: any[] = [];
  savedDocumentTimeTracking: any[] = [];
  commentsDictionaryJson: any = null
productCommentSuggestions: string[] = []
generalCommentSuggestions: string[] = []
showProductCommentSuggestion = false
showGeneralCommentSuggestion = false
private isGeneralComment = false
  previousTracking: { documentId: number | null; startTimeTracking: number | null; endTimeTracking: number | null } = {
    documentId: null,
    startTimeTracking: null,
    endTimeTracking: null
  };
  currentTracking: { documentId: number | null; startTimeTracking: number | null; endTimeTracking: number | null } = {
    documentId: null,
    startTimeTracking: null,
    endTimeTracking: null
  };
  currentTab: number | undefined;
  keywordsFilter!: string ;
  showOnlyPlanifiedProspects: boolean = false;
  showOnlyNotVisited: boolean = false;
  public previousTraking: any = { documentId: null, startTimeTraking: null, endTimeTraking: null };
  public currentTraking: any = { documentId: null, startTimeTraking: null, endTimeTraking: null };
  public tagedUsers: any[] = [];
  public mondayDate: Date | undefined;
  public tuesdayDate: Date | undefined;
  public wednesdayDate: Date | undefined;
  public thursdayDate: Date | undefined;
  public fridayDate: Date | undefined;
  public saturdayDate: Date | undefined;
  note = { note: '', link: '' };
  logger: any;
  historyIn = 'REPORT';
  prospectSpecialities: any;
  canPres: boolean = false;
  canOrder: boolean = false;
  needPo: boolean = false;
  isReseller: boolean = false;
  lockAfterSync = false;
  lockAfterPeriod = false;
  loadingPlan = false;
  userId: any;
  visit_id: number | undefined;
  poIndex: number = 0;
  visitProducts: any[] = []; 
  contactTypes: any[] = [];
  visitId: number = 1; 
  noVisits: boolean = true;
  prospectSelectedId: number | undefined;
  startDate: Date | null = null;
  endDate: Date | undefined;
  public showBuyingPrice: boolean = false;
  purchaseOrder: any = {
    selectedWholeSalerName: '',
    filtredWholeSalers: []
  };
  currentDocument: SafeResourceUrl | null = null; 
  documents: any[] = []; 
  sanitizer: any;
  searchItems: any[] = []
  currentProduct: string | undefined;
  visitProductToInsert: any[] = [];
  translations: any = {};
  selectedProductId: number | undefined;
  reportService: any;
  visitedProspectsIds : number[] = [];
  prospectsWidth : number = 0;
  private mDb!: SQLiteDBConnection;
  markersPositions: any[] = [];
  zoom = 17;
  currentLocation = {
    lat: 0,
    lng: 0,
    name :'',
    times: null,
    icon: {
      path: 'M 0,0 C -2,-20 -10,-22 -10,-30 A 10,10 0 1,1 10,-30 C 10,-22 2,-20 0,0 z',
      scale: 2,
      fillColor: 'yellow',
      fillOpacity: 0.8,
      strokeWeight: 1,
    },
  };
  private topMap!: google.maps.Map;
  private bottomMap!: google.maps.Map;
  private topBounds!: google.maps.LatLngBounds;
  private bottomBounds!: google.maps.LatLngBounds;
  bounds!: any; // google.maps.LatLngBounds
  readonly ALL_OPTION = { id: null as number|null, name: enLanguage.LABELS.ALL };
  public commentSuggestions: string[] = [];
  public showCommentSuggestion = false;
  private curCommentProductId: number | null = null;
  public modifyDate = true;

 



  constructor(private router: Router,
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
    private googleMapsLoader: GoogleMapsLoaderService,
    private route: ActivatedRoute,
    private sqliteService: SQLiteService,
    private dateService: DateService,
    private formBuilder: FormBuilder,
    private fileLogger: LoggerService,
    private loadingController: LoadingController = new LoadingController,
    private http: HttpClient,
    private locationService: LocationService,
    private popupService: PopupService,
    private commonService: CommonService,
    private loggerService: LoggerService,
    private filterService: FilterService,
    private loadingCtrl: LoadingController,
    @Inject(COLORS) private colors: { RED: string; GREEN: string; ORANGE: string; WHITE: string; BLACK: string},
    private visitHistoryService: VisitHistoryService,
    private alertController: AlertController,
    private prospectService: ProspectService,
    private cameraService: CameraService,
    private syncService: SyncService,
    private translationService: TranslationService,
    @Inject(CODES) private CODES: any,
    @Inject(NAME_FILE) private NAME_FILE: any,
    private userService: UserService,
    private LoginService : LoginService,
    private themeService: ThemeService,
    private dbCreationTablesService: DbCreationTablesService,
    private modalCtrl: ModalController) {

    }



    

    resetFilters() {
      const allLabel = this.translate('LABELS.ALL');
    
      this._sectorSelected = { id: null, name: allLabel };
      this._localitySelected = { id: null, name: allLabel };
      this._establishmentSelected = { id: null, name: allLabel };
      this._specialitySelected = { id: null, name: allLabel };
      this._potentialSelected = { id: null, name: allLabel };
      this._activitySelected = { id: null, name: allLabel };
    
      this.filterProspects(
        this._sectorSelected,
        this._localitySelected,
        this._establishmentSelected,
        this._specialitySelected,
        this._potentialSelected,
        this._activitySelected,
        '', 
        false, 
        false, 
        '' 
      );
    }
 
    private initializeForm(): void {
      this.reportForm = this.formBuilder.group({
        weekMondayDay: [this.weekMondayDay, Validators.required],
        selectedProspect: [this.selectedProspect, Validators.required],
        _sectorSelected: ['', Validators.required],
        _localitySelected: ['', Validators.required],
        _specialitySelected: ['', Validators.required],
        _potentialSelected: ['', Validators.required],
        _activitySelected: ['', Validators.required],
        _establishmentSelected: ['', Validators.required],
      });
    }
async initDatabase(mDb: SQLiteDBConnection){
  this.mDb = mDb;
  
}


async ngAfterViewInit() {
  // once your template has rendered the <div #mapContainer>
  this.checkGoogleMapAvailability();
}


async ngOnInit() {
  this.mDb =  this.dbCreationTablesService.getDatabase();
  this.loadTranslations('an.json');
  // Set storage filename and log start of controller
  this.loggerService.setStorageFilename(this.NAME_FILE.FILE_LOG);
  this.loggerService.info('Debut of controller Rapport');
  
  // Set visitDate to start of the day if it exists, otherwise create a new Date object
  if (this.visitDate) {
    this.visitDate.setHours(0, 0, 0, 0);
  } else {
    this.visitDate = new Date();
    console.log("visit date is initialized to:",this.visitDate)
    this.visitDate.setHours(0, 0, 0, 0);
  }
  
  this.selectedProspect = {
    id: 0 ,
        firstname : '' ,
        lastname :'',
        activity :'' ,
        potential :0 , 
        address :'' ,
        gsm :'' ,
        phone :'' ,
        email :'' ,
        note :'' ,
        secretary :'' ,
        grade :'' ,
        specialityId :0 , 
        sectorId :0 , 
        localityId :0 , 
        lat :0 , 
        lng :0 , 
        mapAddress :'' ,
        status :'' ,
        synchronized :false,
        validation :0 , 
        typeId :0 , 
        establishmentId :0 ,
        backgroundColor : '',
  }
  this.showDetailedProspectView = false;
  // Set weekMondayDay to start of the day if it exists, otherwise create a new Date object
  this.weekMondayDay = this.dateService.getMondayOfWeek();
  if (this.weekMondayDay) {
    this.weekMondayDay.setHours(0, 0, 0, 0);
  } else {
    this.weekMondayDay = new Date();
    this.weekMondayDay.setHours(0, 0, 0, 0);
  }
  this.initializeForm();
  // Fetch current user
  this.currentUser =  await this.commonService.getCurrentUser();
  this.commentsDictionary = this.commonService.commentsDictionary;
  const rawLocalities = await this.filterService.getAllLocalities(true);
  const rawSectors        = await this.filterService.getAllSectors(true);
  const rawEstablishments = await this.filterService.getAllEstablishments(true);
  const rawSpecialities   = await this.filterService.getAllSpecialities(true);
  const rawPotentials     = await this.filterService.getAllPotential(true);
  const rawActivities     = await this.commonService.getAllActivities();
  const allActivity = { id: null, value: this.translate('All') };
  
  rawEstablishments.sort((a, b) => a.name.localeCompare(b.name));
  rawSpecialities.sort((a, b)   => a.name.localeCompare(b.name));
  rawLocalities.sort((a, b) => a.name.localeCompare(b.name));
  // Fetch various filters
  // now prepend the “All” item:S
  this.sectors        = [ this.ALL_OPTION, ...rawSectors ];
  this.establishments = [ this.ALL_OPTION, ...rawEstablishments ];
  this.specialities   = [ this.ALL_OPTION, ...rawSpecialities ];
  this.potentials     = [ this.ALL_OPTION, ...rawPotentials ];
  this.localities = [ this.ALL_OPTION, ...rawLocalities ];
  this.activities = [ allActivity, ...rawActivities ];
  const selectedActivity = localStorage.getItem('selected_activity');
  
  // If no selected activity, fetch all establishments
  if (!selectedActivity) {
    await this.filterService.getAllEstablishments(true);
  }

  // Fetch other data
  this.getAllOrders();
  this.gadgets = await this.getAllGadgets();

  this.getAllRanges();
  this.getAllWholesalers();

  this.getAllUsers();
  this.getAllContactTypes();

  // Retrieve selected options from local storage
  let _sectorSelected = { id: null as number | null, name: enLanguage.LABELS.ALL };
  let _localitySelected = { id: null as number | null, name: enLanguage.LABELS.ALL };
  let _establishmentSelected = { id: null as number | null, name: enLanguage.LABELS.ALL };
  let _potentialSelected = { id: null as number | null, name: enLanguage.LABELS.ALL };
  let _specialitySelected = { id: null as number | null, name: enLanguage.LABELS.ALL };
  let _activitySelected = { id: null as number | string | null, name: enLanguage.LABELS.ALL };

  // Retrieve sector
  const selectedSector = localStorage.getItem('selected_sector');
  
  
  if (selectedSector && selectedSector !== '') {
    _sectorSelected = { id: parseInt(selectedSector), name: _sectorSelected.name };
  }

  // Retrieve locality
  const selectedLocality = localStorage.getItem('selected_locality' );
  if (selectedLocality && selectedLocality !== '') {
    _localitySelected = { id: parseInt(selectedLocality), name: _localitySelected.name };
  }

  // Retrieve establishment
  const selectedEstablishment = localStorage.getItem('selected_establishment' );
  if (selectedEstablishment && selectedEstablishment !== '') {
    _establishmentSelected = { id: parseInt(selectedEstablishment), name: _establishmentSelected.name };
  }

  // Retrieve potential
  const selectedPotential = localStorage.getItem('selected_potential');
  if (selectedPotential && selectedPotential !== '') {
    _potentialSelected = { id: parseInt(selectedPotential), name: _potentialSelected.name };
  }

  // Retrieve speciality
  const selectedSpeciality = localStorage.getItem('selected_speciality');
  if (selectedSpeciality && selectedSpeciality !== '') {
    _specialitySelected = { id: parseInt(selectedSpeciality), name: _specialitySelected.name };
  }

  // Retrieve activity
  if (selectedActivity && selectedActivity !== '') {
    _activitySelected = { id: selectedActivity, name: _activitySelected.name };
  }

  if (_sectorSelected?.id) {
    this._sectorSelected = this.sectors.find((s: { id: number | null; }) => s.id == _sectorSelected.id);
    await this.filterLocalitiesBySector(this._sectorSelected.id)
  }
  if (_localitySelected?.id) {
    this._localitySelected = this.localities.find((l: { id: number | null; }) => l.id == _localitySelected.id);
  }
  if (_establishmentSelected?.id) {
    this._establishmentSelected = this.establishments.find((e: { id: number | null; }) => e.id == _establishmentSelected.id);
  }

  if (_potentialSelected?.id) {
    this._potentialSelected = this.potentials.find((p: { id: number | null; }) => p.id == _potentialSelected.id);
  }

  if (_specialitySelected?.id) {
    this._specialitySelected = this.specialities.find((sp: { id: number | null; }) => sp.id == _specialitySelected.id);
  }

  if (_activitySelected?.id) {
    this._activitySelected = this.activities.find((a: { id: number | string | null; }) => a.id == _activitySelected.id);
    await this.filterEstablishmentByActivity(String(this._activitySelected.id));
  }
   // Filter prospects based on selected options
  this.filterProspects(
    _sectorSelected, _localitySelected, _establishmentSelected, _specialitySelected,
    _potentialSelected, _activitySelected, '', false, false, '');

  // Initialize various parameters
  this.initByStateParams();
  this.initPayment();

  // Set user ID
  this.currentUser = await this.commonService.getCurrentUser();

  await this.userService.setUserId(this.currentUser.user_id).toPromise();
  this.userId = this.userService.getUserId();   
  // Get days and check Google map availability
  this.getDays();
  //this.checkGoogleMapAvailability();
}
translate(key: string): string {
  return this.translations[key] || key;
}

async syncSelections(
    sector: any,
    locality: any,
    establishment: any,
    speciality: any,
    potential: any,
    activity: any
  ) {
    this._sectorSelected        = sector;
    this._localitySelected      = locality;
    this._establishmentSelected = establishment;
    this._specialitySelected    = speciality;
    this._potentialSelected     = potential;
    this._activitySelected      = activity;
}

changeLanguage(lang: string) {
  this.loadTranslations(lang);
}

private loadTranslations(lang: string) {
  this.translationService.loadTranslations(lang).subscribe(
    (translations) => {
      this.translations = translations;
      
    },
    (error) => {
      console.error(`Error loading translations for ${lang}`, error);
    }
  );
}
logout() {
  this.LoginService.logout();
}
onThemeChange(): void {
  this.themeService.switchTheme();
}
  redirectToAddProspect() {
    this.router.navigate(['/prospect-detail']);
  }
  setNotSavedVisitData() {
    this.notSavedVisitData = true;
    this.notSavedData = true; 
  }
  setNotSavedPoData() {
    this.notSavedPoData = true;
    this.notSavedPo = true; 
  }
  setNotSavedLocationData() {
    this.notSavedLocationData = true;
    this.notSavedLocation = true; 
  }
  initPayment() {
    // Initialize the payments array
    this.payments = [];

    // Add payment methods to the payments array
    this.payments.push({
      id: 'check',
      value: 'Payé par chèque'
    });
    this.payments.push({
      id: 'cash',
      value: 'Payé par espèce'
    });
  }
  onClickTab(tab: string) {
    this.selectedTab = tab;
  }
  async initByStateParams() {
    const selectedProspectId = this.route.snapshot.paramMap.get('prospectId');
    const selectedVisitDate = this.route.snapshot.paramMap.get('visitDate');
    const selectedType = this.route.snapshot.paramMap.get('type');
  
    if (selectedProspectId !== null && selectedVisitDate !== null) {
      this.selectedTab = selectedType || '';
  
      this.visitDate = new Date(Number(selectedVisitDate));
      console.log("line 587",this.visitDate)
      this.weekMondayDay = this.dateService.getMondayOfWeekSelected(this.visitDate);
  
      try {
        const query = 'SELECT * FROM prospects WHERE id = ?';
        const params = [selectedProspectId];
        const rs = await this.mDb.query(query, params);
  
        // Check if rs has a 'values' property and handle the data accordingly
        if (rs.values && rs.values.length > 0) {
          const item = rs.values[0]; // Access the first item in the values array
          const prospect = {
            id: item.id,
            lng: item.lng,
            lat: item.lat,
            name: item.firstname + ' ' + item.lastname,
            potential_id: item.potential,
            prospect_type_id: item.prospectTypeId,
            speciality_id: item.speciality_id,
            speciality: item.speciality,
            action: item.action
          };
          this.selectProspect(prospect);
        }
      } catch (error) {
        if (error instanceof Error) {
          this.fileLogger.error('Error in FIND_PROSPECT_BY_ID: ' + error.message);
        } else {
          this.fileLogger.error('Unknown error occurred in FIND_PROSPECT_BY_ID');
        }
      }
    }
  }
  async getPurchaseOrderTemplatesItem(purchaseOrderTemplateId: string) {
    this.showLoading = true;
    const loading = await this.loadingController.create({
      message: 'Loading...'
    });
    await loading.present();
  
    const FIND_POT_ITEM = `
      SELECT p.*, pr.name as productName, pr.price, pr.buying_price 
      FROM purchase_order_template_item p 
      INNER JOIN product pr ON pr.id = p.product_id 
      WHERE p.purchase_order_template_id = ?
    `;
    try {
      const purchaseOrderTemplateIdNumber = parseInt(purchaseOrderTemplateId, 10);
      if (isNaN(purchaseOrderTemplateIdNumber)) {
        throw new Error('Invalid purchaseOrderTemplateId: Not a valid number');
      }
  
      const rs = await this.mDb.query(FIND_POT_ITEM, [purchaseOrderTemplateIdNumber]);
      this.purchaseOrderTemplateItem = [];
  
      if (rs.values && rs.values.length > 0) {
        for (let i = 0; i < rs.values.length; i++) {
          const element = {
            id: rs.values[i].product_id,
            name: rs.values[i].productName,
            order_quantity: rs.values[i].quantity,
            freeOrder: rs.values[i].freeOrder,
            labGratuity: rs.values[i].labGratuity,
            price: rs.values[i].price,
            buying_price: rs.values[i].buying_price,
          };
          this.purchaseOrderTemplateItem.push(element);
        }
  
        this.isPurchaseOrderTemplate = true;
        this.addPO(this.purchaseOrderTemplateItem, purchaseOrderTemplateIdNumber, true);
      } else {
        this.loggerService.error('No data found for the given Purchase Order Template ID.');
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.loggerService.error('Error in getPurchaseOrderTemplatesItem: ' + error.message);
      } else {
        this.loggerService.error('Unknown error occurred in getPurchaseOrderTemplatesItem');
      }
    } finally {
      this.showLoading = false;
      await loading.dismiss();
    }
  }
  
  async checkGoogleMapAvailability() {
    if (!(window as any).google || !(window as any).google.maps) {
      this.googleMapIsAvailable = false;
      this.popupService.showAlert('Info', 'Google Maps script not loaded.', '');
      return;
    }
    if (!navigator.onLine) {
      this.googleMapIsAvailable = false;
      this.popupService.showAlert('Info', 'No internet connection.', '');
    } else {
      this.googleMapIsAvailable = true;
      
    }
  }
  

          /***************** Gel All Data  *****************/

  getProducts(query: string): { items: Product[], selectedItems: Product[] } {
    this.selectedProducts = [];
    const returnValue = { items: [] as Product[], selectedItems: [] as Product[] };

    this.products.forEach((item) => {
      let exist = false;
      for (let i = 0; i < this.selectedProducts.length; i++) {
        if (item.product_id === this.selectedProducts[i].product_id) {
          exist = true;
        }
      }
      if (!exist && item.name.toUpperCase().indexOf(query.toUpperCase()) > -1) {
        returnValue.items.push(item);
      }
    });

    return returnValue;
  }
  async getAllOrders() {
    this.orders = await this.commonService.getAllOrders();
  }
  async getAllGadgets(type?: string): Promise<{ id: number; name: string }[]> {
    // if no type was passed in, we'll fetch *every* gadget
    const hasTypeFilter = typeof type === 'string' && type.length > 0;
    const sql = hasTypeFilter
      ? 'SELECT * FROM gadget WHERE type = ?'
      : 'SELECT * FROM gadget';
    const params = hasTypeFilter ? [type] : [];
  
    const res = await this.mDb.query(sql, params);
    console.log('ALL THE GADGETS', res);
  
    const gadgets: { id: number; name: string }[] = [];
    if (res.values?.length) {
      for (let i = 0; i < res.values.length; i++) {
        gadgets.push({
          id: res.values[i].id,
          name: res.values[i].name
        });
      }
    }
    console.log('ALL THE GADGETS', gadgets);
    return gadgets;
  }

 
  getCurrentUser() {
    const query = "SELECT * FROM user u"
    this.mDb.query(query).then((result: any) => {
      if (result.values.length > 0) {
        this.currentUser = result.values[0]
        if (this.currentUser) {
          // Handle both old and new comment dictionary formats
          if (this.currentUser.comments_dictionary !== "") {
            try {
              // Try to parse as JSON first (new format)
              this.commentsDictionaryJson = JSON.parse(this.currentUser.comments_dictionary)
              console.log("Using new JSON comment structure:", this.commentsDictionaryJson)
            } catch (e) {
              // Fallback to old format (split by \n)
              this.commentsDictionary = this.currentUser.comments_dictionary.split("\n")
              console.log("Using old comment structure")
            }
          }
          this.openReportPeriod = this.currentUser.open_report_period
        }
      }
    })
  }
  getAllRanges() {
    this.visitData = { generalNote: '', patientNumber: 0, selectedGadget: null, gadgetQuantity: 0, selectedCompanion: null,selectedContactType: null };
    this.ranges = [];
    const checkedRanges = window.localStorage.getItem('checked_ranges') || '';

    const query = 'SELECT * from range order by name'; // Define the query directly inside the function

    this.mDb.query(query, []).then((result: any) => {
      for (let i = 0; i < result.values.length; i++) {
        const range_id = result.values[i].id;
        let checked = false;
        
        if (checkedRanges !== undefined && checkedRanges.indexOf(range_id.toString()) !== -1) {
          checked = true;
        }

        const element = {
          range_id: range_id,
          name: result.values[i].name,
          checked: checked
        };

        this.ranges.push(element);
      }

      if (this.gadgets.length > 0) {
        this.visitData.selectedGadget = this.gadgets[0];
      }
    });
  }
  async getAllWholesalers() {
    this.loggerService.info('function : getAllWholesalers'); // Replacing $fileLogger.info with loggerService

    this.wholesalers = [];
    const query = 'SELECT * FROM wholesaler'; // Define the query directly inside the function

    const result = await this.mDb.query(query, []);
    this.wholesalers = [];
    if (result.values && result.values.length > 0) {
      for (let i = 0; i < result.values.length; i++) {
        const wholesaler = {
          id: result.values[i].id,
          name: result.values[i].name,
          email: result.values[i].email,
          phone: result.values[i].phone,
          address: result.values[i].address,
          description: result.values[i].description
        };

        this.wholesalers.push(wholesaler);
      }
    }
      this.loggerService.info('view getAllWholesalers list');
   
  }
  getAllUsers() {
    this.loggerService.info('function : getAllUsers'); 

    this.users = [];
    this.delegates = [];
    const query = 'SELECT * FROM users'; 

    this.mDb.query(query, []).then((result: any) => {
      for (let i = 0; i < result.values.length; i++) {
        const user = {
          id: result.values[i].id,
          name: result.values[i].name
        };

        if (result.values[i].delegate_id != undefined && result.values[i].delegate_id != null) {
          this.delegates.push(user);
        }

        this.users.push(user);
      }

      this.loggerService.info('view getAllUsers list'); 
    }).catch((error) => {
      this.loggerService.error('Error fetching users data', error); 
    });
  }
  async getAllContactTypes() {
    this.loggerService.info('function : getAllContactTypes');
    this.contactTypes = [];
    const query = 'SELECT * FROM contact_type ORDER BY name';
    
    try {
      const result = await this.mDb.query(query, []);
      if (result.values && result.values.length > 0) {
        for (let i = 0; i < result.values.length; i++) {
          const contactType = {
            id: result.values[i].id,
            name: result.values[i].name,
            action: result.values[i].action,
            icon: result.values[i].icon
          };
          this.contactTypes.push(contactType);
        }
      }
      this.loggerService.info('view getAllContactTypes list');
    } catch (error) {
      this.loggerService.error('Error fetching contact types data', error);
    }
  }
          /***************** Filters  *****************/

  setShowHistory(): void {          
    if (this.selectedProspect && this.selectedProspect.id) {
      this.getVisitAndPOHistory(Number(this.selectedProspect.id));
    }
  }
  setShowPoHistory(): void {  
    if (this.selectedProspect && this.selectedProspect.id) {
      this.getVisitAndPOHistory(Number(this.selectedProspect.id));
    }
  }
  setShowMap(): void {
    if (this.showMap && this.googleMapIsAvailable) {
      // let the DOM update first…
      setTimeout(() => this.renderTopMap(), 0);
    }
  }
  
  renderTopMap() {
    // initialize or re‐init the top map
    this.topMap = new google.maps.Map(this.mapContainer.nativeElement, {
      center: { lat: this.currentLocation.lat, lng: this.currentLocation.lng },
      zoom:   this.zoom,
    });
    this.topBounds = new google.maps.LatLngBounds();
    this.drawMarkers(this.topMap, this.topBounds);
  }

  renderBottomMap() {
    this.loggerService.info('Rendering bottom map...');
  
    const centerLat = this.currentLocation?.lat;
    const centerLng = this.currentLocation?.lng;
  
    if (centerLat === undefined || centerLng === undefined) {
      this.loggerService.warn('Cannot render bottom map: currentLocation is undefined.');
      return;
    }
  
    this.loggerService.info(`Initializing map with center: lat=${centerLat}, lng=${centerLng} and zoom=${this.zoom}`);
  
    try {
      // Initialize or re-init the bottom map
      this.bottomMap = new google.maps.Map(this.mapContainerBottom.nativeElement, {
        center: { lat: centerLat, lng: centerLng },
        zoom: this.zoom,
      });
  
      this.loggerService.info('Bottom map initialized successfully');
  
      // Initialize bounds
      this.bottomBounds = new google.maps.LatLngBounds();
      this.loggerService.info('Bottom map bounds initialized');
  
      // Draw markers
      this.drawMarkers(this.bottomMap, this.bottomBounds);
      this.loggerService.info('Markers drawn on bottom map');
  
    } catch (error) {
      this.loggerService.error('Error rendering bottom map: ' + (error instanceof Error ? error.message : error));
    }
  }
  

  private drawMarkers(map: google.maps.Map, bounds: google.maps.LatLngBounds) {
    // clear old markers
    (map as any).markers?.forEach((m: google.maps.Marker) => m.setMap(null));
    (map as any).markers = [];

    // add new ones
    this.markersPositions.forEach(pos => {
      const m = new google.maps.Marker({
        position: { lat: pos.lat, lng: pos.lng },
        map,
        icon: pos.icon,
        title: pos.name
      });
      m.addListener('click', () => this.showDetails(m, pos));
      (map as any).markers.push(m);
      bounds.extend(m.getPosition()!);
    });

    // fit or fallback
    if (this.markersPositions.length) {
      map.fitBounds(bounds);
      if ((map.getZoom() || 0) < 7) map.setZoom(7);
    } else {
      map.setCenter({ lat: 0, lng: 0 });
      map.setZoom(5);
    }
  }

  private showDetails(marker: google.maps.Marker, pos: any) {
    new google.maps.InfoWindow({
      content: `<div><strong>${pos.name}</strong></div>`
    }).open(marker.getMap()!, marker);
  }

  
  showHideFilter(): void {
    
  }
  async filterLocalitiesBySector(sectorId: number) {
     this.localities=await this.filterService.filterLocalitiesBySector(sectorId);
     this._localitySelected = this.localities[0];
  }
  
    async filterEstablishmentByActivity(activityId: string) {
     this.establishments=await this.filterService.filterEstablishmentsByActivity(activityId);
    }
    async filterProspects(
      sectorSelected: any,
      localitySelected: any,
      establishmentSelected: any,
      specialitySelected: any,
      potentialSelected: any,
      activitySelected: any,
      keywordsFilter: string,
      showOnlyPlanifiedProspects: boolean,
      showOnlyNotVisited: boolean,
      orderSelected: any
    ) {
    
      // 2) Normalize any undefined/null back to your “All” option
      if (!sectorSelected?.id) {
        sectorSelected = { id: null, name: enLanguage.LABELS.ALL };
        localitySelected = { id: null, name: enLanguage.LABELS.ALL };
      }
      if (!activitySelected?.id) {
        activitySelected = { id: null, name: enLanguage.LABELS.ALL };
        establishmentSelected = { id: null, name: enLanguage.LABELS.ALL };
      }
      if (!localitySelected?.id) {
        localitySelected = { id: null, name: enLanguage.LABELS.ALL };
      }
      if (!establishmentSelected) {
        establishmentSelected = { id: null, name: enLanguage.LABELS.ALL };
      }
      if (!specialitySelected) {
        specialitySelected = { id: null, name: enLanguage.LABELS.ALL };
      }
      if (!potentialSelected) {
        potentialSelected = { id: null, name: enLanguage.LABELS.ALL };
      }
      if (orderSelected === undefined) {
        orderSelected = null;
      }
    
      // 3) Shorthand flags
      const hasText = !!(keywordsFilter?.trim());
      const hasFilt = [
        sectorSelected.id,
        localitySelected.id,
        establishmentSelected.id,
        specialitySelected.id,
        potentialSelected.id,
        activitySelected.id
      ].some(id => id != null);
      const planning = showOnlyPlanifiedProspects;
      const notVisi  = showOnlyNotVisited;
    
      let sql    = '';
      let params: any[] = [];
      // 4b) ANY dropdown ID or text → dynamic filter
       if (hasFilt || hasText) {
        const baseQuery = await this.commonService.getFilterQuery(
          sectorSelected,
          localitySelected,
          establishmentSelected,
          specialitySelected,
          potentialSelected,
          activitySelected,
          null,
          null,
          keywordsFilter,
          this.weekMondayDay,
          orderSelected,
          planning,
          notVisi ? 'WEEK' : '',
          ''
        );
        params = this.commonService.getFilterQueryParams(
          sectorSelected,
          localitySelected,
          establishmentSelected,
          specialitySelected,
          potentialSelected,
          activitySelected,
          keywordsFilter,
          null,
          null
        );

        const weekEnd = new Date(this.weekMondayDay);
        weekEnd.setDate(weekEnd.getDate() + 6);

        sql = `
          SELECT q.*, COUNT(vp.id) AS visitProductCount,
                 v.lat AS visitLat,
                 v.lng AS visitLng
            FROM (${baseQuery}) q
       LEFT JOIN visit v ON v.prospect_id = q.id
                        AND v.status <> 'DELETED'
                        AND v.visit_date >= ${this.weekMondayDay.getTime()}
                        AND v.visit_date <= ${weekEnd.getTime()}
       LEFT JOIN visit_product vp ON vp.visit_id = v.id
        GROUP BY q.id
        `;
        this.isListOfVisitedProspects = false;
    
      // 4c) Only toggles on → visits of the week
      } else {
        const weekEnd = new Date(this.weekMondayDay);
        weekEnd.setDate(weekEnd.getDate() + 6);
    
        sql = `
          SELECT p.*, COUNT(vp.id) AS visitProductCount,
                 v.lat AS visitLat,
                 v.lng AS visitLng,
                 pt.id AS prospectTypeId,
                 pot.id AS potential,
                 sp.id AS speciality_id,
                 sp.name AS speciality,
                 sp.action AS action
            FROM visit v
       LEFT JOIN visit_product vp ON vp.visit_id = v.id
       INNER JOIN prospect p       ON p.id = v.prospect_id
       INNER JOIN speciality sp     ON sp.id = p.speciality_id
       INNER JOIN prospect_type pt  ON pt.id = p.type_id
       INNER JOIN potential pot     ON pot.id = p.potential
           WHERE v.status <> 'DELETED'
             AND v.visit_date >= ${this.weekMondayDay.getTime()}
             AND v.visit_date <= ${weekEnd.getTime()}
           GROUP BY v.prospect_id
        `;
        params = [];
        this.isListOfVisitedProspects = true;
      }
    
      // 5) Run query & map results
      console.log('📋 Executing SQL:', sql, params);
      try {
        const rs = await this.mDb.query(sql, params);
        this.prospects = [];
        if (rs.values?.length) {
          for (const row of rs.values) {
            const prospect = {
                id: row.id,
              lng: row.lng,
              lat: row.lat,
              visitLat: row.visitLat,
              visitLng: row.visitLng,
              visitProductCount: row.visitProductCount,
              name: `${row.firstname} ${row.lastname}`,
              potential_id: row.potential,
              prospect_type_id: row.prospectTypeId,
              speciality_id: row.speciality_id,
              speciality: row.speciality,
              action: row.action,
              activity: row.activity,
              sector_id: row.sector_id,
              backgroundColor: this.colors.BLACK

            };
            // preserve your original ordering logic
            if (this.prospects.some(p => p.id === row.id)) {
              this.prospects.unshift(prospect);
            } else {
              this.prospects.push(prospect);
            }
          }
        }
        this.prospectsWidth = this.prospects.length * 140;
        await this.highlightVisitedProspect();

        // build markers array for map display
        this.markersPositions = this.prospects
          .filter(p => p.lat && p.lng && p.lat !== 0 && p.lng !== 0)
          .map(p => ({
            lat: p.lat,
            lng: p.lng,
            name: p.name,
            icon: {
              path: 'M 0,0 C -2,-20 -10,-22 -10,-30 A 10,10 0 1,1 10,-30 C 10,-22 2,-20 0,0 z',
              fillColor: 'red',
              fillOpacity: 0.8,
              strokeWeight: 0,
            },
          }));

        if (this.showMap && this.googleMapIsAvailable) {
          setTimeout(() => this.renderTopMap(), 0);
        }
      } catch (err) {
        console.error('❌ filterProspects error:', err);
      }
      this.showDetailedProspectView = false;
      // 7) Persist “All” or selected IDs
      window.localStorage['selected_sector']      = sectorSelected.id   ?? '';
      window.localStorage['selected_locality']    = localitySelected.id ?? '';
      window.localStorage['selected_establishment'] = establishmentSelected.id ?? '';
      window.localStorage['selected_speciality']  = specialitySelected.id  ?? '';
      window.localStorage['selected_potential']   = potentialSelected.id   ?? '';
      window.localStorage['selected_activity']    = activitySelected.id    ?? '';
    }

  async highlightVisitedProspect() {
    console.log("Entered trying to color prospeccct names.");
    for (let j = 0; j < this.prospects.length; j++) {
     console.log("we are running through prospects",this.prospects[j]);
      if (this.prospects[j].visitProductCount !== undefined &&
          this.prospects[j].visitProductCount !== null &&
          this.prospects[j].visitProductCount !== 0) {
        console.log("TRUE GREEN");
        this.prospects[j].backgroundColor = this.colors.GREEN;
        const prospect = this.prospects[j];
        this.prospects.splice(j, 1);
        this.prospects.unshift(prospect);
      }
    }
  
    for (let j = 0; j < this.prospects.length; j++) {
      if (this.prospects[j].visitProductCount === 0 &&
          this.prospects[j].visitLat !== 0 && this.prospects[j].visitLng !== 0 &&
          this.prospects[j].visitLat !== null && this.prospects[j].visitLng !== null) {
        
        this.prospects[j].backgroundColor = this.colors.ORANGE;
        const prospect = this.prospects[j];
        this.prospects.splice(j, 1);
        this.prospects.unshift(prospect);
      }
    }
  
   // this.renderMap();
  }
          /***** Filtered wholesaler *****/


          filterWholesalers(purchaseOrder: any): void {
            const wholeSaleSearchText = purchaseOrder.selectedWholeSalerName;
          
            // Filter the wholesalers list based on the search text
            purchaseOrder.filtredWholeSalers = this.wholesalers.filter((item: any) =>
              item.name.toLowerCase().includes(wholeSaleSearchText.toLowerCase())
            );
          
            // If the search text is empty, clear the selected wholesaler
            if (wholeSaleSearchText.trim() === '') {
              this.selectWholesaler(null, purchaseOrder);
            }
          }
          
  
          clearWholesalersFilter(purchaseOrder: any): void {
            // Clear the filtered wholesalers list
            purchaseOrder.filtredWholeSalers = [];
          
            // If no wholesaler is selected, reset the wholesaler details
            if (!purchaseOrder.selectedWholesaler) {
              purchaseOrder.selectedWholeSalerName = '';
              purchaseOrder.isPurchaseOrderUpdated = false;
              this.selectWholesaler(null, purchaseOrder);
            }
          }
  
          /***** Filtered user *****/

  filterUsers(text: string): void {
    const userSearchText = text;

    this.filtredUsers = this.users.filter((item: any) => {
      return item.name.toLowerCase().includes(userSearchText.toLowerCase());
    });

    if (userSearchText === '') {
      this.selectUser(null);
    }
  }
  clearUsersFilter(): void {
    setTimeout(() => this.filtredUsers = [], 200);

    if (this.selectedUser == null) {
      this.userSearchText = '';
      this.selectUser(null);
    }
  }
  async filterProductsByRanges(): Promise<void> {
    const ranges_ids: number[] = [];
    if (this.ranges.length > 0) {
      for (let i = 0; i < this.ranges.length; i++) {
        if (this.ranges[i].checked) {
          ranges_ids.push(this.ranges[i].range_id);
        }
      }
  
      window.localStorage['checked_ranges'] = JSON.stringify(ranges_ids);
    }
  
    if (this.visit !== null && this.visit !== undefined) {
      const currentVisitProduct = this.visit.visitProducts;
      console.log(currentVisitProduct);
      if (ranges_ids.length === 0) {
        for (let i = 0; i < currentVisitProduct.length; i++) {
          currentVisitProduct[i].show = true;
        }
      } else {
        for (let i = 0; i < currentVisitProduct.length; i++) {
          currentVisitProduct[i].show = false;
          for (let j = 0; j < currentVisitProduct[i].productRangeId.length; j++) {
            if (ranges_ids.indexOf(currentVisitProduct[i].productRangeId[j]) !== -1) {
              currentVisitProduct[i].show = true;
              break;
            }
          }
        }
      }
    }
  
    for (let v = 0; v < this.purchaseOrders.length; v++) {
      const currentVisitProduct = this.purchaseOrders[v].visitProducts;
      if (ranges_ids.length === 0) {
        for (let i = 0; i < currentVisitProduct.length; i++) {
          currentVisitProduct[i].show = true;
        }
      } else {
        for (let i = 0; i < currentVisitProduct.length; i++) {
          currentVisitProduct[i].show = false;
          for (let j = 0; j < currentVisitProduct[i].productRangeId.length; j++) {
            if (ranges_ids.indexOf(currentVisitProduct[i].productRangeId[j]) !== -1) {
              currentVisitProduct[i].show = true;
              break;
            }
          }
        }
      }
    }
  }
  setShowRecovery(purchaseOrder: any): void {

    if (purchaseOrder.showRecovery === true) {
      this.getRecovery(purchaseOrder);
    }
  }
          /***************** Report Init  *****************/

          isMonday = (dateString: string) => {
            const date = new Date(dateString);
            const utcDay = date.getUTCDay();
            return utcDay === 1;
          }
          showDatePicker=false;
          openDatePicker() {
            this.showDatePicker = !this.showDatePicker;
          }
          onWeekChange(event: any) {
            this.weekMondayDay = new Date(event.detail.value);
            this.visitsHistory = {};
            this.getDays();
            this.savedDocumentTimeTracking = [];
            this.previousTracking = { documentId: null, startTimeTracking: null, endTimeTracking: null };
            this.currentTracking = { documentId: null, startTimeTracking: null, endTimeTracking: null };
        
            if (this.weekMondayDay.getTime() < this.dateService.getMondayOfWeek().setHours(0, 0, 0, 0)) {
              this.currentTab = 0;
              this.visitDate = this.weekMondayDay;
            } else {
              this.visitDate = new Date();
              this.visitDate.setHours(0, 0, 0, 0);
              this.initCurrentTab(this.visitDate.toISOString(), this.weekMondayDay.toISOString());
            }
        
            if (this.selectedProspect != null) {
              this.selectProspect(this.selectedProspect);
            }
        
            let _sectorSelected: SelectedType = { id: null, name: 'ALL' };
            let _localitySelected: SelectedType = { id: null, name: 'ALL' };
            let _establishmentSelected: SelectedType = { id: null, name: 'ALL' };
            let _potentialSelected: SelectedType = { id: null, name: 'ALL' };
            let _specialitySelected: SelectedType = { id: null, name: 'ALL' };
            let _activitySelected: SelectedType = { id: null, name: 'ALL' };
        
            if (window.localStorage['selected_sector'] !== undefined && window.localStorage['selected_sector'] !== '') {
              _sectorSelected = { id: parseInt(window.localStorage['selected_sector']), name: _sectorSelected.name };
            }
        
            if (window.localStorage['selected_locality'] !== undefined && window.localStorage['selected_locality'] !== '') {
              _localitySelected = { id: parseInt(window.localStorage['selected_locality']), name: _localitySelected.name };
            }
        
            if (window.localStorage['selected_establishment'] !== undefined && window.localStorage['selected_establishment'] !== '') {
              _establishmentSelected = { id: parseInt(window.localStorage['selected_establishment']), name: _establishmentSelected.name };
            }
        
            if (window.localStorage['selected_potential'] !== undefined && window.localStorage['selected_potential'] !== '') {
              _potentialSelected = { id: parseInt(window.localStorage['selected_potential']), name: _potentialSelected.name };
            }
        
            if (window.localStorage['selected_speciality'] !== undefined && window.localStorage['selected_speciality'] !== '') {
              _specialitySelected = { id: parseInt(window.localStorage['selected_speciality']), name: _specialitySelected.name };
            }
        
            if (window.localStorage['selected_activity'] !== undefined && window.localStorage['selected_activity'] !== '') {
              _activitySelected = { id: window.localStorage['selected_activity'], name: _activitySelected.name };
            }
        
            this.filterProspects(
              _sectorSelected,
              _localitySelected,
              _establishmentSelected,
              _specialitySelected,
              _potentialSelected,
              _activitySelected,
              this.keywordsFilter || '',
              this.showOnlyPlanifiedProspects ?? false,
              this.showOnlyNotVisited || false,
              this.orderSelected
            );
          }
         
  checkOpenReportPeriod() {
    const selectedTabDate = this.dateService.getDateOfDay(
      this.currentTab ?? 0, 
      this.weekMondayDay ?? new Date()
    );
    this.lockAfterPeriod = this.commonService.checkOpenPeriod(selectedTabDate, 'REPORT'); 
    this.fileLogger.info('Checked open report period for date: ' + selectedTabDate);
  }

  initCurrentTab(visitDate: string, weekMondayDay: string): void {
    console.log("visitDAte when pressing",this.visitDate);
    const visitDateObj = new Date();
    visitDateObj.setHours(0, 0, 0, 0);
    const weekMondayDayObj = new Date(weekMondayDay);
    this.currentTab = this.dateService.numberOfDaysBetween2Dates(visitDateObj, weekMondayDayObj);
    console.log("visitDateObj",visitDateObj);
    console.log("weekMOndayDAyObj",weekMondayDayObj);

    console.log("current tab after pressing",this.currentTab)
    if (this.currentTab == 6 && this.currentUser.working_days == 6) {
      this.currentTab = 5;
    } else if ((this.currentTab == 5 || this.currentTab == 6) && this.currentUser.working_days == 5) {
      this.currentTab = 4;
    }
    
    this.selectDay(this.currentTab);
  }
  selectDay(currentTab: number): void {
    this.currentTab = currentTab;

    const weekMondayDay = this.weekMondayDay || new Date();

    this.visitDate = this.dateService.getDateOfDay(this.currentTab, weekMondayDay);
    this.getVisitData();

    // Check if selectedProspect and its id are defined before calling getVisitAndPOHistory
    if (this.selectedProspect && this.selectedProspect.id) {
      this.getVisitAndPOHistory(Number(this.selectedProspect.id));
    } else {
        console.error("Selected prospect is null or ID is missing.");
    }

    this.savedDocumentTimeTracking = [];
    this.previousTraking = { documentId: null, startTimeTraking: null, endTimeTraking: null };
    this.currentTraking = { documentId: null, startTimeTraking: null, endTimeTraking: null };

    this.tagedUsers = [];
  }
  getDays(): void {
    if (!this.weekMondayDay) {
      console.error("weekMondayDay is undefined. Please ensure it is set before calling getDays.");
      return;
    }
    
    this.mondayDate = this.dateService.getDateOfDay(0, this.weekMondayDay);
    this.tuesdayDate = this.dateService.getDateOfDay(1, this.weekMondayDay);
    this.wednesdayDate = this.dateService.getDateOfDay(2, this.weekMondayDay);
    this.thursdayDate = this.dateService.getDateOfDay(3, this.weekMondayDay);
    this.fridayDate = this.dateService.getDateOfDay(4, this.weekMondayDay);
    this.saturdayDate = this.dateService.getDateOfDay(5, this.weekMondayDay);
  }
  async getNotes(selectedProspect: { speciality_id: number, sector_id: number, activity: string }) {
    try {
      this.note = { note: '', link: '' };
  
      // Fetch all notes from the database
      const getNoteParams = "SELECT * FROM note";
      const notes = await this.mDb.query(getNoteParams, []);
  
      let specialityIds = '';
      let sectorIds = '';
      let activities = '';
  
      // Check if notes.values is defined and not empty
      if (notes.values && notes.values.length > 0) {
        for (let i = 0; i < notes.values.length; i++) {
          specialityIds += notes.values[i].speciality_ids + ',';
          sectorIds += notes.values[i].sector_ids + ',';
          activities += notes.values[i].activities + ',';
        }
  
        // Removing the trailing comma from the strings
        specialityIds = specialityIds.slice(0, -1);
        sectorIds = sectorIds.slice(0, -1);
        activities = activities.slice(0, -1);
  
        const getNoteQuery = `SELECT note, link FROM note WHERE ${selectedProspect.speciality_id} IN (${specialityIds}) AND 
                              ${selectedProspect.sector_id} IN (${sectorIds}) AND '${selectedProspect.activity}' IN ('${activities}')`;
        
        const noteResult = await this.mDb.query(getNoteQuery, []);
  
        if (noteResult.values && noteResult.values.length > 0) {
          this.note.note = noteResult.values[0].note;
          this.note.link = noteResult.values[0].link;
        }
      } else {
        console.warn("No notes found in the database.");
      }
      
    } catch (err) {
      // Type assertion to ensure err is an Error object
      this.logger.error("Error in executing getNotes function: " + (err as Error).message);
    } 
  }
  async selectProspect(selectedProspect: any) {
    try {
      console.log("user is",this.currentUser.work_type)
      this.selectedProspect = selectedProspect;
      this.showDetailedProspectView = true;
      this.showHistory=false;
      this.showPoHistory=false;
      this.initCurrentTab(
        this.visitDate ? this.visitDate.toISOString() : '', 
        this.weekMondayDay ? this.weekMondayDay.toISOString() : ''
      );
      await this.getNotes(selectedProspect);
      this.historyIn = 'REPORT';
      this.purchaseOrders = [];
      this.savedDocumentTimeTracking = [];
      this.previousTraking = { documentId: null, startTimeTraking: null, endTimeTraking: null };
      this.currentTraking = { documentId: null, startTimeTraking: null, endTimeTraking: null };
      // ① Force the VISIT tab
      this.onClickTab('VISIT');
      this.visitsHistory = {};
      
      this.prospectSpecialities = selectedProspect.speciality;

      let actions = selectedProspect.action.toString(2).split('');
      while (actions.length < 4) {
        actions.unshift("0");
      }
      this.canPres = actions[0] === "1";
      this.canOrder = actions[1] === "1";
      this.needPo = actions[2] === "1";
      this.isReseller = actions[3] === "1";



      const purchaseOrderTemplateId = this.route.snapshot.paramMap.get('purchaseOrderTemplateId');
      if (purchaseOrderTemplateId && purchaseOrderTemplateId !== 'null') {
          this.selectedTab = 'ORDER';
          this.getPurchaseOrderTemplatesItem(purchaseOrderTemplateId);
      }

      if (!this.prospects.includes(selectedProspect)) {
        this.isListOfVisitedProspects = false;
        this.prospects = [selectedProspect];
      }

      this.getVisitAndPOHistory(Number(this.selectedProspect?.id ?? ''));
      this.tagedUsers = [];
      
    } catch (error) {
      this.showDetailedProspectView = false;
      this.logger.error("Error in selectProspect function: " + (error as Error).message);
    }

  }

  async getVisitData() {
    try {
      this.visitData = { generalNote: '', patientNumber: 0, selectedGadget: null, gadgetQuantity: 0, selectedCompanion: null,selectedContactType: null };
      this.purchaseOrders = [];
      this.isPurchaseOrderTemplate = false;
      this.lockAfterSync = false;
  
      this.checkOpenReportPeriod();
  
      if (this.weekMondayDay != null && this.selectedProspect != null) {
        const queryProductInChargePlan = `SELECT p.* FROM charge_plan c JOIN product p ON p.id = c.product_id LEFT JOIN speciality s ON s.id = c.speciality_id WHERE s.name = ? ORDER BY c.rank`;
  
        const productsResult = await this.mDb.query(queryProductInChargePlan, [this.selectedProspect.speciality]);
        if (!productsResult.values || productsResult.values.length === 0) {
          this.loadingPlan = false;
          this.showLoading = false;
          this.popupService.showAlert(this.translate('INFO'), this.translate('LOADING_PLAN_IS_EMPTY'), '');
          this.fileLogger.error('Loading plan is empty');
          return;
        } else {
          this.loadingPlan = true;
          this.showLoading = true;
  
          const rangesResult = await this.mDb.query('SELECT * FROM product_range', []);
          let productRanges: any = {};
          if (rangesResult.values) {
            rangesResult.values.forEach((range: any) => {
              if (!productRanges[range.product_id]) {
                productRanges[range.product_id] = [];
              }
              productRanges[range.product_id].push(range.range_id);
            });
          }
  
          if (productsResult.values.length > 0) {
            this.products = [];
            productsResult.values.forEach((product: any) => {
              product.range_id = productRanges[product.id];
              this.products.push(product);
            });
  
            this.visitDate = this.dateService.getDateOfDay(this.currentTab ?? 0, this.weekMondayDay);
            
            const selectVisitQuery = "SELECT v.* FROM visit v WHERE v.status <> 'DELETED' AND v.prospect_id = ? AND v.visit_date = ? AND v.user_id = ?";
            const date = this.visitDate.getTime();
            console.log('📝 getVisitData(): SELECT v… params =', {
              prospectId: this.selectedProspect?.id,
              visitDateMs: date,
              userId: this.userId
            });
            
            const resultSet = await this.mDb.query(selectVisitQuery, [this.selectedProspect.id, date, this.userId]);
  
            this.visit_id = 0;
            if (resultSet.values && resultSet.values.length === 1) {
              const visitRow = resultSet.values[0];
              this.visit_id = visitRow.id;
              this.visitData.generalNote = visitRow.general_note;
              this.visitData.patientNumber = visitRow.patient_number;
              this.visitData.gadgetQuantity = visitRow.gadget_quantity;
  
              this.gadgets.forEach((gadget) => {
                if (gadget.id === visitRow.gadget_id) {
                  this.visitData.selectedGadget = gadget;
                }
              });
  
              this.delegates.forEach((delegate) => {
                if (delegate.id === visitRow.companion_id) {
                  this.visitData.selectedCompanion = delegate;
                }
              });
              this.contactTypes.forEach((contactType) => {
                if (contactType.id === visitRow.contact_type_id) {
                  this.visitData.selectedContactType = contactType;
                }
              });
  
              this.checkIfReportIsLocked(visitRow.synchronized === 1);
              this.visit = {
                type: 'visit',
                currentRanking: 0,
                attachment: { name: '', base64Image: '' },
                orderPlacementByPhoneChecked: false,
                selectedWholesaler: null,
                purchaseOrderId: 0,
                placementMethod: 'ON_SITE',
                isPurchaseOrderUpdated: false,
                visitProducts: this.getAllproductsWithVisits([], this.products),
                orderTotalprice: 0,
                selectedWholeSalerName: '',
                generatePo: true,
                generateDo: false,
              };
              await this.getVisitProducts(this.visit);
              await this.getMessageTag();
            } else {
              this.visit = {
                type: 'visit',
                currentRanking: 0,
                attachment: { name: '', base64Image: '' },
                orderPlacementByPhoneChecked: false,
                selectedWholesaler: null,
                purchaseOrderId: 0,
                placementMethod: 'ON_SITE',
                isPurchaseOrderUpdated: false,
                visitProducts: this.getAllproductsWithVisits([], this.products),
                orderTotalprice: 0,
                selectedWholeSalerName: '',
                generatePo: true,
                generateDo: false,
              };
              await this.filterProductsByRanges();
            }
  
            if (this.selectedTab === 'POSITION') {
              this.onPositionClick();
            }
  
            if (!this.canOrder && this.selectedTab === 'ORDER') {
              this.selectedTab = 'VISIT';
            }
  
            await this.getPurchaseOrder();
            this.showLoading = false;
          }
        }
      }
    } catch (err) {
      this.loadingController.dismiss();
      this.fileLogger.error('Error in getVisitData: ' + (err instanceof Error ? err.message : String(err)));
    }
  }

  onPurchaseOrderTabClick() {
    if (this.purchaseOrders.length === 0) {
      const defaultId = 0;
      this.addPO(undefined, defaultId, false);
    }
  }
  
  addPO(products: any, purchaseOrderTemplateId: number, isPurchaseOrderTemplate: boolean) {
    let visitProducts: any;
  
    if (products !== undefined && products !== null) {
      visitProducts = this.getAllproductsWithVisits([], products);
    } else {
      visitProducts = this.getAllproductsWithVisits([], this.products);
    }
  
    const recovery = {
      id: 0,
      date: new Date(),
      amount: null,
      payment: null,
      attachment_id: null,
      description: null,
      isRecoveryUpdated: false,
      attachment: { base64Image: '', attachmentName: '' }
    };
  
    this.poIndex = this.poIndex + 1;
    const index = this.poIndex;
  
    this.purchaseOrders.push({
      type: 'po',
      index: index,
      currentRanking: 0,
      attachment: { name: '', base64Image: '' },
      orderPlacementByPhoneChecked: false,
      selectedWholesaler: null,
      placementMethod: 'ON_SITE',
      purchaseOrderId: 0,
      isPurchaseOrderUpdated: false,
      visitProducts: visitProducts,
      orderTotalprice: 0,
      selectedWholeSalerName: '',
      recoveryList: [],
      recovery: recovery,
      purchaseOrderTemplateId: purchaseOrderTemplateId,
      isPurchaseOrderTemplate: isPurchaseOrderTemplate,
      generatePo: true,
      generateDo: false
    });
  
    this.calculateTotalPrice();
    this.filterProductsByRanges();
  }
  async getVisitProducts(purchaseOrder: any) {
    const visitProducts: any[] = [];
    const selectQuery = "SELECT p.id as productId, p.price, p.name, p.ordre, vp.* FROM product p LEFT JOIN visit_product vp ON p.id = vp.product_id LEFT JOIN visit v ON vp.visit_id = v.id " +
      " WHERE vp.visit_id = ? AND (purchase_order_id IS NULL OR purchase_order_id = 0) AND vp.status <> 'DELETED'";
  
    try {
      const resultSet = await this.mDb.query(selectQuery, [this.visit_id]);
      
      const rows = resultSet.values || [];
  
      for (const row of rows) {
        visitProducts.push(row);
      }
      this.visit.visitProducts.sort((a: { ordre: number; }, b: { ordre: number; }) => a.ordre - b.ordre);
      purchaseOrder.visitProducts = this.visitProducts = this.getAllproductsWithVisits(visitProducts, this.products);
      this.filterProductsByRanges();
      this.calculateTotalPrice();
      
    } catch (err) {
      const error = err as Error;
      this.fileLogger.error(error.message);
    }
  }
  async getPOProducts(purchaseOrder: any) {
    const visitProducts: any[] = [];
    const selectQuery = "SELECT p.id as productId, p.price, p.name, p.ordre, vp.* FROM product p LEFT JOIN visit_product vp ON p.id = vp.product_id WHERE vp.purchase_order_id = ?";

    try {
      const resultSet = await this.mDb.query(selectQuery, [purchaseOrder.purchaseOrderId]);
      const rows = resultSet.values || [];
      for (const row of rows) {
        visitProducts.push(row);
      }

      purchaseOrder.visitProducts = this.getAllproductsWithVisits(visitProducts, this.products);
      this.filterProductsByRanges();
      this.calculateTotalPrice();

    } catch (err) {
      if (err instanceof Error) {
        this.fileLogger.error(err.message);
      } else {
        this.fileLogger.error('An unknown error occurred');
      }
    }
  }
  async getMessageTag() {
    const selectMassageTag = `
      SELECT u.*
      FROM message_tag m
      INNER JOIN users u ON m.user_id = u.id
      WHERE m.visit_id = ?`;
  
    try {
      const resultSet = await this.mDb.query(selectMassageTag, [this.visit_id]);
      this.tagedUsers = [];
      
      if (resultSet.values && Array.isArray(resultSet.values)) {
        for (const user of resultSet.values) {
          this.tagedUsers.push(user);
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error('Error fetching message tags: ' + error.message);
      } else {
        this.logger.error('An unknown error occurred while fetching message tags');
      }
    }
  }
  checkIfReportIsLocked(isSynchronized: boolean): void {
    if (this.currentUser.lock_after_sync === "true") {
        if (isSynchronized) {
            this.lockAfterSync = true;
        } else {
            this.lockAfterSync = false;
        }
    }
  } 
  getAllproductsWithVisits(visitProducts: any[], products: any[]): any[] {
    const result: any[] = [];

    if (visitProducts.length === 0) {
        this.resetOrders(visitProducts);
        for (let i = 0; i < products.length; i++) {
            const element = {
                id: 0,
                ordre: products[i].ordre,
                product_id: products[i].id,
                productName: products[i].name,
                productPrice: products[i].price,
                sellingPrice: products[i].price,
                buyingPrice: products[i].buying_price,
                productRangeId: products[i].range_id,
                order_quantity: products[i].order_quantity,
                freeOrder: products[i].freeOrder,
                labGratuity: products[i].labGratuity,
                show: true,
                comment: "",
                smily: 4
            };
            result.push(element);
        }
    } 
    // productsInReport is NOT EMPTY
    else {
        for (let i = 0; i < products.length; i++) {
            let element: any = {};
            let index = -1;
            let ranges_ids: any;

            for (let j = 0; j < visitProducts.length; j++) {
                if (products[i].id === visitProducts[j].product_id) {
                    index = j;
                    ranges_ids = products[i].range_id;
                    break;
                }
            }

            if (index !== -1) {
                element = {
                    id: visitProducts[index].id,
                    ordre: visitProducts[index].ordre,
                    product_id: visitProducts[index].product_id,
                    productPrice: visitProducts[index].price,
                    sellingPrice: products[i].price,
                    buyingPrice: products[i].buying_price,
                    productName: visitProducts[index].name,
                    productRangeId: ranges_ids,
                    show: true,
                    comment: visitProducts[index].comment,
                    sample_quantity: visitProducts[index].sample_quantity,
                    order_quantity: visitProducts[index].order_quantity,
                    rank: visitProducts[index].rank,
                    smily: visitProducts[index].smily,
                    sale_quantity: visitProducts[index].sale_quantity,
                    urgent: !!visitProducts[index].urgent,
                    prescription_quantity: visitProducts[index].prescription_quantity,
                    freeOrder: visitProducts[index].freeOrder,
                    labGratuity: visitProducts[index].lab_gratuity,
                };
            } else {
                element = {
                    id: 0,
                    product_id: products[i].id,
                    productName: products[i].name,
                    productPrice: products[i].price,
                    sellingPrice: products[i].price,
                    buyingPrice: products[i].buying_price,
                    productRangeId: products[i].range_id,
                    show: true,
                    comment: "",
                    smily: 4
                };
            }
            // PUSH HERE
            result.push(element);
        }
    }
    return result;
  }
  async getVisitAndPOHistory(prospectId: number): Promise<void> {
    this.historyIn = 'REPORT';

    let visitsData  = {};
    let poData      = {};
    this.noVisits = true;
    this.prospectSelectedId = prospectId;
    this.startDate = null;
    this.endDate = this.visitDate ? new Date(this.visitDate) : new Date();
    console.log("start Date", this.startDate)
    console.log("End Date", this.endDate)

    if (this.showHistory === true) {
      visitsData =await this.visitHistoryService.getVisitsHistory(this.prospectSelectedId, this.startDate, this.endDate);

    }

    if (this.showPoHistory === true) {
      poData =await this.visitHistoryService.getPoHistory(this.prospectSelectedId, this.startDate, this.endDate);
    }
    this.visitsHistory = {};

    // copy visits
    Object.entries(visitsData).forEach(([dateKey, prospects]: any) => {
      this.visitsHistory[dateKey] = { ...prospects };
    });
    
    // merge PO
    Object.entries(poData).forEach(([dateKey, prospects]: any) => {
      if (!this.visitsHistory[dateKey]) {
        this.visitsHistory[dateKey] = {};
      }
      Object.entries(prospects).forEach(([name, block]: any) => {
        const existing = this.visitsHistory[dateKey][name] || { visit: [], pos: {}, data: {}, blocked: false };
        this.visitsHistory[dateKey][name] = {
          visit: [...existing.visit, ...(block.visit || [])],
          pos: { ...existing.pos, ...block.pos },
          data: block.data,       // or decide how to merge data
          blocked: block.blocked  // or combine flags as you like
        };
      });
    });
  }
  async getPurchaseOrder() {
    const querypurchaseOrder = `
      SELECT 
        p.synchronized, p.id, p.attachmentBase64, p.attachmentName, 
        placement_method, p.wholesaler_id, p.purchase_order_template_id, 
        generate_po, generate_do 
      FROM purchase_order p  
      WHERE visit_id = ? AND status <> 'DELETED'
    `;
    
    try {
      const result = await this.mDb.query(querypurchaseOrder, [this.visit_id]);
      if (result.values && result.values.length > 0) {
        for (let i = 0; i < result.values.length; i++) {
          let recovery = {
            id: 0,
            date: new Date(),
            amount: null,
            payment: null,
            attachment_id: null,
            description: null,
            isRecoveryUpdated: false,
            attachment: { base64Image: '', attachmentName: '' }
          };
  
          this.poIndex = this.poIndex + 1;
          const index = this.poIndex;
  
          let purchaseOrder = {
            type: 'po',
            index: index,
            currentRanking: 0,
            attachment: { name: '', base64Image: '' },
            orderPlacementByPhoneChecked: false,
            selectedWholesaler: null,
            purchaseOrderId: 0,
            placementMethod: 'ON_SITE',
            isPurchaseOrderUpdated: false,
            visitProducts: this.getAllproductsWithVisits([], this.products),
            orderTotalprice: 0,
            selectedWholeSalerName: '',
            recoveryList: [],
            recovery: recovery,
            isPurchaseOrderTemplate: false,
            generatePo: true,
            generateDo: false
          };
  
          this.purchaseOrders.push(purchaseOrder);
          
          purchaseOrder.selectedWholesaler = null;
          purchaseOrder.purchaseOrderId = result.values[i].id;
          (purchaseOrder as any).wholesalerId = result.values[i].wholesaler_id;
  
          if (result.values[i].purchase_order_template_id !== undefined && result.values[i].purchase_order_template_id !== null) {
            purchaseOrder.isPurchaseOrderTemplate = true;
            this.isPurchaseOrderTemplate = true;
          }
  
          for (let j = 0; j < this.wholesalers.length; j++) {
            if ((purchaseOrder as any).wholesalerId === this.wholesalers[j].id) {
              purchaseOrder.selectedWholesaler = this.wholesalers[j];
            }
          }
  
          purchaseOrder.selectedWholeSalerName = purchaseOrder.selectedWholesaler ? (purchaseOrder.selectedWholesaler as any).name.trim() : '';
          let synchronized = result.values[i].synchronized;
          purchaseOrder.attachment.base64Image = result.values[i].attachmentBase64;
          purchaseOrder.orderPlacementByPhoneChecked = result.values[i].placement_method === 'PHONE';
          purchaseOrder.generatePo =
            result.values[i].generate_po === 1 || result.values[i].generate_po === 'true';
          purchaseOrder.generateDo =
            result.values[i].generate_do === 1 || result.values[i].generate_do === 'true';
          purchaseOrder.attachment = {
            name: result.values[i].attachmentName,
            base64Image: result.values[i].attachmentBase64
          };
  
          await this.getPOProducts(purchaseOrder);
        }
      }
  
      if (this.selectedTab === 'ORDER') {
        this.onPurchaseOrderTabClick();
      }
    } catch (err) {
      console.error("Error in select purchase order: " + (err as Error).message);
      this.loadingCtrl.dismiss(); 
      this.logger.error("Error in select purchase order: " + (err as Error).message);
    }
  }

        /***************** Fill visit data  *****************/


  toogleProductPrice(showBuyingPrice: boolean): void {
  
    for (let purchaseOrder of this.purchaseOrders) {
      for (let product of purchaseOrder.visitProducts) {
        product.productPrice = showBuyingPrice ? product.buyingPrice : product.sellingPrice;
      }
    }
    this.calculateTotalPrice();
  }
  async setFreeQuantity(visitProduct: any) {
    if (!this.selectedProspect || !this.selectedProspect.id) {
      console.error('No valid prospect selected');
      this.fileLogger.warn('setFreeQuantity: No valid prospect selected');
      return;
    }
  
    const querypurchaseOrder = `
      SELECT freeQuantity, labGratuity, max(orderQuantity) as orderQuantity
      FROM free_quantity_rule_item
      WHERE product_id = ? AND potential_id = ? AND prospect_type_id = ? AND orderQuantity <= ?
    `;
    console.log(this.selectedProspect.potential)
    const params = [
      visitProduct.product_id,
      this.selectedProspect.potential_id,
      this.selectedProspect.prospect_type_id,
      visitProduct.order_quantity
    ];
  
    this.fileLogger.info(`setFreeQuantity: Executing query with params: ${JSON.stringify(params)}`);
  
    try {
      const result = await this.mDb.query(querypurchaseOrder, params);
      this.fileLogger.info(`setFreeQuantity: Query result - ${JSON.stringify(result)}`);
  
      if (result.values && result.values.length > 0) {
        const row = result.values[0];
  
        if (visitProduct.order_quantity >= row.orderQuantity) {
          visitProduct.freeOrder = row.freeQuantity;
          visitProduct.labGratuity = row.labGratuity;
  
          this.fileLogger.info(`setFreeQuantity: Applied freeOrder=${row.freeQuantity}, labGratuity=${row.labGratuity} for product_id=${visitProduct.product_id}`);
        } else {
          this.fileLogger.info(`setFreeQuantity: order_quantity (${visitProduct.order_quantity}) < rule.orderQuantity (${row.orderQuantity}) — no freeOrder applied.`);
        }
      } else {
        this.fileLogger.info(`setFreeQuantity: No matching rule found for product_id=${visitProduct.product_id}`);
      }
    } catch (err) {
      if (err instanceof Error) {
        this.fileLogger.error(`setFreeQuantity: Error in select purchase order: ${err.message}`);
      } else {
        this.fileLogger.error('setFreeQuantity: Unknown error occurred in select purchase order');
      }
    } finally {
      this.fileLogger.info(`setFreeQuantity: Finished processing product_id=${visitProduct.product_id}`);
    }
  }
  
  fillRank(productId: number, purchaseOrder: any) {
    const ranks: number[] = [];
    let index = 0;
    const visitProducts = purchaseOrder.visitProducts;

    this.fileLogger.info(`function : fillRank with parameter productId: ${productId}`);

    for (let j = 0; j < visitProducts.length; j++) {
      if (visitProducts[j].rank !== undefined) {
        ranks.push(visitProducts[j].rank);
      }
      if (visitProducts[j].product_id === productId) {
        index = j;
      }
    }

    if (ranks.length === 0 && visitProducts[index].rank === undefined) {
      purchaseOrder.currentRanking++;
      visitProducts[index].rank = purchaseOrder.currentRanking;
    } 
    else if (ranks.length > 0 && visitProducts[index].rank === undefined) {
      purchaseOrder.currentRanking = Math.max(...ranks) + 1;
      visitProducts[index].rank = purchaseOrder.currentRanking;
    }
  }
  async showResetOrderDialog(visit: any) {
    this.loggerService.info("function : showResetOrderDialog for réinitialiser ordres");

    const alert = await this.alertController.create({
      header: this.translate('CONFIRMATION'),
      message: this.translate('ARE_YOU_SURE_YOU_WANT_TO_RESET_ORDERS'),
      buttons: [
        {
          text: this.translate('CANCEL'),
          role: 'cancel',
          handler: () => {
            this.loggerService.info('cancel reset orders');
          }
        },
        {
          text: this.translate('OK'),
          handler: () => {
            visit.currentRanking = 0;
            this.resetOrders(visit.visitProducts);
          }
        }
      ]
    });

    await alert.present();
  }
  resetOrders(visitProducts: Array<{ rank?: number }>): void {
    visitProducts.forEach(product => {
      product.rank = undefined;
    });
  }
  setSmily(idProduct: number, value: number, visitProducts: Array<{ product_id: number, smily?: number }>): void {
    console.info(`Function: setSmily with parameters: idProduct = ${idProduct}, value = ${value}`);
    
    visitProducts.forEach(product => {
      if (product.product_id === idProduct) {
        product.smily = value;
      }
    });
  }
  updateVisits(visitToUpdate: { product_id: number }, purchaseOrder: { visitProducts: Array<{ product_id: number }> }): void {
    purchaseOrder.visitProducts = purchaseOrder.visitProducts.map(product => 
      product.product_id === visitToUpdate.product_id ? visitToUpdate : product
    );
  }
  updatePatientNumber(patientNumber: number): void {
    this.visitData.patientNumber = patientNumber;
  }
  updateGadgetId(selectedGadget: string): void {
    this.visitData.selectedGadget = selectedGadget;
  }
  updateCompanionId(selectedCompanion: any): void {
    this.visitData.selectedCompanion = selectedCompanion;
  }
  updategadgetQuantity(gadgetQuantity: number): void {
    this.visitData.gadgetQuantity = gadgetQuantity;
  }
  updateGeneralNote(generalNote: string): void {
    this.visitData.generalNote = generalNote;
  }
  checkIsNumber(evt: KeyboardEvent): void {
    const allowedKeys = ['Backspace', 'Delete'];
    if (!isFinite(evt.key as any) && !allowedKeys.includes(evt.key)) {
      evt.preventDefault();
    }
  }
  onClickWolesalers(purchaseOrder: any): void {
    const wholeSaleSearchText = purchaseOrder.selectedWholeSalerName;
  
    // If search text is empty, show all wholesalers
    if (wholeSaleSearchText.trim() === '') {
      purchaseOrder.filtredWholeSalers = this.wholesalers;
    }
  }
  
  chooseWholesaler(wholesaler: any, purchaseOrder: any): void {
    // Trim and set the selected wholesaler's name
    purchaseOrder.selectedWholeSalerName = wholesaler.name.trim();
  
    // Clear the filtered wholesalers list
    purchaseOrder.filtredWholeSalers = [];
  
    // Update the purchase order if a new wholesaler is selected
    if (
      !purchaseOrder.selectedWholesaler ||
      wholesaler.id !== purchaseOrder.selectedWholesaler.id
    ) {
      purchaseOrder.isPurchaseOrderUpdated = true;
      this.selectWholesaler(wholesaler, purchaseOrder);
    }
  }
  
  selectWholesaler(selectedWholesaler: any, purchaseOrder: any): void {
    // Set the selected wholesaler in the purchase order
    purchaseOrder.selectedWholesaler = selectedWholesaler;
  }
  
  chooseUser(user: any): void {
    
    // 1) Clear out the dropdown
    this.filtredUsers = [];
  
    // 2) Let your existing logic select it (if you still need selectUser)
    if (!this.selectedUser || user.id !== this.selectedUser.id) {
      this.selectUser(user);
    }
  
    // 3) Write the name into the input so it actually shows
    this.userSearchText = user.name;
  
    // 4) Push it onto your “tagged” list
    this.tagedUsers.push(user);
    this.userSearchText = '';
  }
  clearUserInput() {
    this.userSearchText = '';
    this.filtredUsers = [];
  }
  
  async deleteUser(user: any): Promise<void> {
    const index = this.tagedUsers.indexOf(user);
    if (index !== -1) {
      this.tagedUsers.splice(index, 1);
    }
  
    const deleteAllMessageTag = "DELETE FROM message_tag WHERE visit_id=? AND user_id=?";
    try {
      await this.mDb.query(deleteAllMessageTag, [this.visit_id, user.id]);
    } catch (error: any) {
      this.loggerService.error(error.message);
    }
  }
  selectUser(selectedUser: any): void {
    this.selectedUser = selectedUser;
  }
  onClickUsers(text: string): void {
    if (text === '') {
      this.filtredUsers = this.users;
    }
  }
  calculateTotalPrice(): void {
    this.purchaseOrders.forEach(purchaseOrder => {
      purchaseOrder.orderTotalprice = 0;
      purchaseOrder.visitProducts.forEach((product: { order_quantity: number | undefined; productPrice: number; }) => {
        if (product.order_quantity !== undefined) {
          purchaseOrder.orderTotalprice += product.productPrice * product.order_quantity;
        }
      });
    });
  }
  selectDocument(document: any): void {
    this.previousTracking = { ...this.currentTracking }; 
    if (this.previousTracking.startTimeTracking !== null) {
      this.previousTracking.endTimeTracking = new Date().getTime();
      this.saveTimeTracking(this.previousTracking);
    }
    this.currentTracking = { 
      documentId: document.id, 
      startTimeTracking: new Date().getTime(), 
      endTimeTracking: null 
    };
    this.currentDocument = this.sanitizer.bypassSecurityTrustResourceUrl(document.document_base64); 
  }
  saveTimeTracking(traking: any) {
    if (traking.documentId != null && traking.startTimeTraking != null && traking.endTimeTraking != null) {
        this.mDb.query('INSERT INTO presentation_time_tracking (id, product_document_id, start_time, end_time, synchronized) VALUES (?,?,?,?,0)', 
        [Date.now(), traking.documentId, traking.startTimeTraking, traking.endTimeTraking]).then((res: any) => {
            if (res.changes && res.changes.lastId) {
                this.savedDocumentTimeTracking.push(res.changes.lastId);
            }
        }).catch((error: any) => {
            this.fileLogger.error('Error in saving saveTimeTracking: ' + error.message);
        });
    }
  }

  async openProductDetails(productId: number) {
    try {
      // Reset the current document and fetch product
      this.currentDocument = '';
      this.documents = [];
  
      // Fetch product details
      const fetchProductQuery = 'SELECT * FROM product WHERE id = ?';
      const products = await this.mDb?.query(fetchProductQuery, [productId]);
      this.currentProduct = products?.values?.[0];
      // Fetch product documents
      const fetchDocumentsQuery = 'SELECT * FROM product_documents WHERE product_id = ?';
      const documents = await this.mDb?.query(fetchDocumentsQuery, [productId]);
      this.documents = documents?.values || [];

      // Open Modal for Product Details
      const modal = await this.modalCtrl.create({
        component: ProductPopupComponent,
        componentProps: {
          product: this.currentProduct,
          documents: this.documents,
        },
        cssClass: 'fullscreen-modal',
        backdropDismiss: false,
        showBackdrop: true,
      });
  
      // Handle modal dismissal
      await modal.present();
      const { data } = await modal.onWillDismiss();
      if (data?.saveTracking) {
        this.currentTracking.endTimeTracking = (new Date()).getTime();
        this.saveTimeTracking(this.currentTracking);
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
    }
  }
  

  openProspectDetails(prospectId: number) {
    console.log("we started here")
    this.prospectService.openProspectDetails(prospectId);
  }
    async showAttachmentImage(attachment: any) {
    const modal = await this.modalCtrl.create({
      component: ImageModalComponent,
      componentProps: { attachment },
      cssClass: 'custom-popup'
    });

    await modal.present();
  }
  async showPODeleteConfirm(purchaseOrder: any) {
    const alert = await this.alertController.create({
      header: this.translate('CONFIRMATION'),
      message:this.translate('ARE_YOU_SURE_YOU_WANT_TO_DELETE_THIS_ATTACHEMENT'),
      buttons: [
        {
          text: this.translate('CANCEL'),
          role: 'cancel',
        },
        {
          text: this.translate('OK'),
          handler: async () => {
            purchaseOrder.attachment.base64Image = '';
            purchaseOrder.attachment.attachmentName = '';
  
            if (purchaseOrder.purchaseOrderId != 0) {
              const updateDeleteAttachment = `
                UPDATE purchase_order
                SET attachmentBase64 = ?, attachmentName = ?, synchronized = 0
                WHERE id = ?
              `;
  
              try {
                await this.mDb.query(updateDeleteAttachment, [
                  purchaseOrder.attachment.base64Image,
                  purchaseOrder.attachment.attachmentName,
                  purchaseOrder.purchaseOrderId,
                ]);
                this.fileLogger.info(`Attachment deleted from purchase order with id= ${purchaseOrder.purchaseOrderId}`);
              } catch (error) {
                if (error instanceof Error) {
                  this.fileLogger.error(error.message);
                } else {
                  this.fileLogger.error('An unknown error occurred while deleting the attachment.');
                }
              }
            }
          },
        },
      ],
    });
  
    await alert.present();
  }
  modelToItemMethodProduct(modelValue: any): any {
    if (this.products.length > 0) {
      for (let i = 0; i < this.products.length; i++) {
        if (this.products[i].product_id === modelValue) {
          return this.products[i];
        }
      }
    }
    return null; 
  }
  /*productClickedMethod(callback: any): void {
    this.selectedProducts = [];
    if (callback.item) {
        this.selectedProducts.push(callback.item);
        const ionAutocompleteElement = document.getElementById("product-autocomplete");
        const ionAutocompleteComponent = ionAutocompleteElement && ionAutocompleteElement.componentOnReady ? await ionAutocompleteElement.componentOnReady() : null;

        if (ionAutocompleteComponent) {
            let searchItems = ionAutocompleteComponent['searchItems'];
            for (let i = 0; i < searchItems.length; i++) {
                if (searchItems[i].product_id === callback.item.product_id) {
                    this.selectedProductId = callback.item.product_id;
                    searchItems.splice(i, 1);
                }
            }
        }
        this.slideTo(`product_${this.selectedProductId}`);
    }
}*/



  productItemsRemoved(callback: any) {
    if (callback && callback.item) {
      this.searchItems.push(callback.item);

      for (let i = 0; i < this.selectedProducts.length; i++) {
        if (this.selectedProducts[i].product_id === callback.item.id) {
          this.selectedProducts.splice(i, 1);
        }
      }
    }
  }
  slideTo(id: string) {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
    }
  }
  async takePictureFromCamera(purchaseOrder: any) {
    try {
      const onGetPicture = (order: any) => {
        console.log('Picture captured for order:', order);
      };
  
      const onCameraImageLoaded = (image: string, order: any) => {
        console.log('Image successfully loaded:', image);
      };
  
      await this.cameraService.takePictureFromCamera(purchaseOrder, onGetPicture, onCameraImageLoaded);
    } catch (error) {
      console.error('Error taking picture:', error);
    }
  }
  onGetPicture(purchaseOrder: PurchaseOrder): void {
    if (purchaseOrder.attachment) {
      purchaseOrder.attachment.base64Image = this.cameraService.getLoadingImage();
  } else {
      purchaseOrder.attachment = {
          name: '', 
          base64Image: this.cameraService.getLoadingImage()}}  
  }
  onCameraImageLoaded(base64CameraResult: string, purchaseOrder: PurchaseOrder): void {
    this.loggerService.info('Starting reader.onloadend success callback');
    
    const name = (new Date().getTime()).toString();
    purchaseOrder.attachment = {
      name: name,
      base64Image: base64CameraResult
    };
    
    purchaseOrder.isPurchaseOrderUpdated = true;
  }
  chooseRecoveryFile(purchaseOrder: any) {
    purchaseOrder.recovery.isRecoveryUpdated = true;
    purchaseOrder.recovery.attachment = {
      name: '',
      base64Image: ''
    };
  
    this.cameraService.chooseFile(purchaseOrder.recovery.attachment, 'recoveryFile');
  }
  choosePoFile(purchaseOrder: any) {
    purchaseOrder.isPurchaseOrderUpdated = true;
    purchaseOrder.attachment = {
      name: '',
      base64Image: ''
    };
    this.cameraService.chooseFile(
      purchaseOrder.attachment,
      `poFile-${purchaseOrder.index}`
    );
  }
  toggleCommentsSuggestions() {
    this.showCommentSuggestion = !this.showCommentSuggestion;
  }
  getProductComment(): string {
    return this.productCommentRef.nativeElement.value;
   
  }
  // called on every keystroke in the comment box
// Updated method for product comments
completeComment(productId: number)
{
  console.log("completeComment called for productId:", productId)

  this.curCommentProductId = productId
  this.showProductCommentSuggestion = true
  this.showGeneralCommentSuggestion = false

  const el = document.getElementById(`product_comment_${productId}`) as HTMLTextAreaElement
  const val = el?.value?.toLowerCase() || ""

  console.log("Current input value:", val)

  // Reset suggestions
  this.productCommentSuggestions = []

  // CORRECTION: Utiliser directement le JSON parsé du currentUser
  if (this.currentUser?.comments_dictionary && this.currentUser?.work_type) {
    const workType = this.canOrder ? 'pharmaceutical' : 'medical';
    console.log("Work type:", workType)
    console.log("Comments dictionary:", this.currentUser.comments_dictionary)

    try {
      // Accès direct au JSON parsé
      const workTypeData = this.currentUser.comments_dictionary[workType]

      if (workTypeData && workTypeData.product_comment && workTypeData.product_comment.values) {
        // Extraire DIRECTEMENT le tableau values
        let suggestions = workTypeData.product_comment.values
        console.log("Raw product suggestions:", suggestions)

        // Vérifier que c'est bien un tableau
        if (Array.isArray(suggestions)) {
          // Filtrer selon le texte saisi
          if (val.trim()) {
            suggestions = suggestions.filter(
              (comment: string) => typeof comment === "string" && comment.toLowerCase().includes(val),
            )
          }

          // Assigner DIRECTEMENT le tableau de strings
          this.productCommentSuggestions = suggestions
          console.log("Final product suggestions:", this.productCommentSuggestions)
        }
      }
    } catch (error) {
      console.error("Error processing comment data:", error)
    }
  } else {
    // Fallback vers l'ancien système
    this.productCommentSuggestions = val
      ? this.commentsDictionary.filter((c) => c.toLowerCase().includes(val))
      : [...this.commentsDictionary]
  }

  if (this.productCommentSuggestions.length === 0) {
    this.showProductCommentSuggestion = false
  }
}
// Ligne ~830 - Ajouter ces méthodes après completeGeneralComment()
completeGeneralComment()
{
  
  console.log("completeGeneralComment called")

  this.showGeneralCommentSuggestion = true
  this.showProductCommentSuggestion = false

  const el = document.querySelector(
    'ion-textarea[ng-reflect-model="visitData.generalNote"] textarea',
  ) as HTMLTextAreaElement
  const val = el?.value?.toLowerCase() || ""

  console.log("Current general comment value:", val)

  // Reset suggestions
  this.generalCommentSuggestions = []

  // CORRECTION: Utiliser directement le JSON parsé du currentUser
  if (this.currentUser?.comments_dictionary && this.currentUser?.work_type) {
    const workType = this.canOrder ? 'pharmaceutical' : 'medical';
    console.log("Work type for general:", workType)
    console.log("Comments dictionary:", this.currentUser.comments_dictionary)

    try {
      // Accès direct au JSON parsé
      const workTypeData = this.currentUser.comments_dictionary[workType]

      if (workTypeData && workTypeData.general_comment && workTypeData.general_comment.values) {
        // Extraire DIRECTEMENT le tableau values
        let suggestions = workTypeData.general_comment.values
        console.log("Raw general suggestions:", suggestions)

        // Vérifier que c'est bien un tableau
        if (Array.isArray(suggestions)) {
          // Filtrer selon le texte saisi
          if (val.trim()) {
            suggestions = suggestions.filter(
              (comment: string) => typeof comment === "string" && comment.toLowerCase().includes(val),
            )
          }

          // Assigner DIRECTEMENT le tableau de strings
          this.generalCommentSuggestions = suggestions
          console.log("Final general suggestions:", this.generalCommentSuggestions)
        }
      }
    } catch (error) {
      console.error("Error processing general comment data:", error)
    }
  } else {
    // Fallback vers l'ancien système
    this.generalCommentSuggestions = val
      ? this.commentsDictionary.filter((c) => c.toLowerCase().includes(val))
      : [...this.commentsDictionary]
  }

  if (this.generalCommentSuggestions.length === 0) {
    this.showGeneralCommentSuggestion = false
  }
}

public chooseProductComment(suggestion: string) {
  if (this.curCommentProductId == null) return

  const p = this.visit.visitProducts.find(
    (v: { product_id: number | null }) => v.product_id === this.curCommentProductId,
  )
  if (p) {
    p.comment = suggestion
  }

  this.showProductCommentSuggestion = false
  this.curCommentProductId = null
}

// New method to choose general comment
public chooseGeneralComment(suggestion: string) {
  this.visitData.generalNote = suggestion
  this.showGeneralCommentSuggestion = false
  this.setNotSavedVisitData()
}




 // Ligne ~3840 - Remplacer clearCommentSuggestions() existante
 clearCommentSuggestions() {
  this.productCommentSuggestions = []
  this.generalCommentSuggestions = []
  this.showProductCommentSuggestion = false
  this.showGeneralCommentSuggestion = false
}
  setOrderPlacement(purchaseOrder: any) {
    purchaseOrder.isPurchaseOrderUpdated = true;
  
    if (purchaseOrder.orderPlacementByPhoneChecked) {
      purchaseOrder.placementMethod = "PHONE";
    } else {
      purchaseOrder.placementMethod = "ON_SITE";
    }
  }
  setGeneratePo(purchaseOrder: any) {
    purchaseOrder.isPurchaseOrderUpdated = true;
  }
  setGenerateDo(purchaseOrder: any) {
    purchaseOrder.isPurchaseOrderUpdated = true;
  }

  validateProductCommentsWithRank(): { isValid: boolean; errorMessage: string } {
    if (!this.visit?.visitProducts) {
      return { isValid: true, errorMessage: "" };
    }
  
    const productsWithCommentButNoRank = this.visit.visitProducts.filter(
      (product: any) => 
        product.comment && 
        product.comment.trim().length > 0 && 
        (product.rank === undefined || product.rank === null || product.rank === 0)
    );
  
    if (productsWithCommentButNoRank.length > 0) {
      const productNames = productsWithCommentButNoRank
        .map((p: any) => p.productName)
        .join(', ');
      
      return {
        isValid: false,
        errorMessage: `Il faut saisir un rank pour les produits avec commentaire: ${productNames}`
      };
    }
  
    return { isValid: true, errorMessage: "" };
  } 
 async saveVisitAndPo() {
    const purchaseOrder = this.visit;
  
    // Check if attachment is required and not provided
    if (this.visit.selectedWholesaler != null && this.needPo && this.visit.attachment.base64Image === '') {
      const alert = await this.alertController.create({
        header: this.translate('LOADING'),
        message: this.translate('PLEASE_CHOOSEN_AN_ATTACHMENT'),
      });
      await alert.present();
      return;
    }
    const commentValidation = this.validateCommentsAccordingToJSON()
    if (!commentValidation.isValid) {
      this.popupService.showAlert(
        this.translate("ERROR"),
        commentValidation.errorMessage,
        this.translate("TOASTER_SUCCESS_TYPE"),
      )
      this.loggerService.error("Comment validation failed: " + commentValidation.errorMessage)
      return
    }

    const rankValidation = this.validateProductCommentsWithRank();
    if (!rankValidation.isValid) {
      this.popupService.showAlert(
        this.translate("ERROR"),
        rankValidation.errorMessage,
        this.translate("TOASTER_SUCCESS_TYPE"),
      );
      this.loggerService.error("Rank validation failed: " + rankValidation.errorMessage);
      return;
    }
  
    // Ligne INSERT
const insertVisitQuery = `INSERT INTO visit 
(id, prospect_id, visit_date, general_note, patient_number, gadget_id, gadget_quantity, user_id, user_name, companion_id, contact_type_id, status, synchronized) 
VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)`;

// Ligne UPDATE  
const updateVisitQuery = `UPDATE visit 
SET prospect_id = ?, visit_date = ?, general_note = ?, patient_number = ?, gadget_id = ?, gadget_quantity = ?, user_id = ?, user_name = ?, companion_id = ?, contact_type_id = ?, synchronized = ? 
WHERE id = ?`;
  
    const result = this.commonService.checkNumbersIsUnique(this.visit.visitProducts, this.purchaseOrders, this.selectedTab);
  
    if (result === this.CODES.RANKS_IS_UNIQUE || result === this.CODES.QUANTITY_AND_WHOLESALER_EXISTS) {
      try {
        
          let visitQuery = '';
          let data: any[] = [];
          let gadgetId = null;
          let companionId = null;
          let contactTypeId = null;
          if (this.visitData.selectedGadget != null) {
            gadgetId = this.visitData.selectedGadget.id;
          }
          if (this.visitData.selectedCompanion != null) {
            companionId = this.visitData.selectedCompanion.id;
          }
          if (this.visitData.selectedContactType != null) { // Nouvelle section
            contactTypeId = this.visitData.selectedContactType.id;
          }
  
          if (this.visit_id === 0) {
            visitQuery = insertVisitQuery;
            this.visit_id = new Date().getTime();
            data = [
              this.visit_id,
              this.selectedProspect!.id,
              this.visitDate!.setHours(0, 0, 0, 0),
              this.visitData.generalNote,
              this.visitData.patientNumber,
              gadgetId,
              this.visitData.gadgetQuantity,
              this.currentUser.user_id,
              this.currentUser.first_last_name,
              companionId,
              contactTypeId,
              'NEW',
              0
            ];
          } else {
            visitQuery = updateVisitQuery;
            data = [
              this.selectedProspect!.id,
              this.visitDate!.setHours(0, 0, 0, 0),
              this.visitData.generalNote,
              this.visitData.patientNumber,
              gadgetId,
              this.visitData.gadgetQuantity,
              this.currentUser.user_id,
              this.currentUser.first_last_name,
              companionId,
              contactTypeId,
              0,
              this.visit_id,
            ];
          }
          const insertedVisit = await this.mDb.run(visitQuery, data);
          
            this.loggerService.info('Save visit done');
  
            if (this.visit_id === 0) {
              this.visit_id = insertedVisit.changes!.lastId;
            }
  
            if (this.tagedUsers.length > 0) {
              await this.saveMessageTag();
            }
  
            if (this.selectedTab === 'VISIT') {
              await this.saveVisitProducts(this.visit);
            }
  
            // Delete Purchase Orders with no selected wholesaler
            for (let i = 0; i < this.purchaseOrders.length; i++) {
              if (this.purchaseOrders[i].selectedWholesaler == null) {
                this.commonService.deletePo(i, this.purchaseOrders[i]);
              }
            }
  
            if (this.selectedTab === 'ORDER' && this.purchaseOrders.length > 0) {
              for (let v = 0; v < this.purchaseOrders.length; v++) {
                const purchaseOrder = this.purchaseOrders[v];
                await this.savePurchaseOrder(purchaseOrder, v);
              }
            }
  
            if (this.savedDocumentTimeTracking.length > 0) {
              for (let i = 0; i < this.savedDocumentTimeTracking.length; i++) {
                const timeTracking = this.savedDocumentTimeTracking[i];
                await this.setVisitToTimeTracking(timeTracking);
              }
            }
         
  
        
        
        this.visitedProspectsIds.push(this.selectedProspect!.id);
        this.selectedProspect!.backgroundColor = this.colors.GREEN;
  
        let message = '';
        if (this.selectedTab === 'VISIT') {
          message = this.translate('VISIT_INSERTED_SUCCESS');
          this.notSavedVisitData = false;
        } else {
          message = this.translate('PO_INSERTED_SUCCESS');
          this.notSavedPoData = false;
        }
  
        if (this.notSavedVisitData === true || this.notSavedPoData === true || this.notSavedLocationData === true) {
          this.notSavedData = true;
        } else {
          this.notSavedData = false;
        }
        // Persist new visits/orders in IndexedDB when running on the web
        if (this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.dbCreationTablesService.databaseName);
        }
        const confirmPopup = await this.alertController.create({
          header: this.translate('SUCCESSFUL_OPERATION'),
          message: message,
          buttons: [
            {
              text: this.LABELS.RETURN,
              role: 'cancel',
              handler: () => {
  
                if (this.route.snapshot.paramMap.get('prospectId') !== "null") {
                  this.router.navigate(['/tab/rapport/null/null/null/null']);
                }
              }
            },
            {
              text: this.LABELS.SEND,
              handler: () => {
                this.send();
                if (this.route.snapshot.paramMap.get('prospectId') !== "null") {
                  this.router.navigate(['/tab/rapport/null/null/null/null']);
                }
              }
            }
          ]
        });
  
        await confirmPopup.present();
  
      } catch (error ) {
        if(error instanceof Error){
          this.popupService.showToasterAlert(this.translate('ERROR') + this.translate('SAVE_ERROR'), error.message, 'danger');
          this.loggerService.error('error ' + error.message);
        }       
      }
    } else if (result === this.CODES.RANKS_IS_EMPTY) {
      this.popupService.showAlert(this.translate('ERROR'), this.translate('RANKS_IS_EMPTY'), this.translate('TOASTER_SUCCESS_TYPE'));
      this.loggerService.error('ranks is empty');
    } else if (result === this.CODES.COMMENT_IS_NOT_EXIST) {
      this.popupService.showAlert(this.translate('ERROR'), this.translate('COMMENT_IS_NOT_EXIST'), this.translate('TOASTER_SUCCESS_TYPE'));
      this.loggerService.error("comment is not exist");
    } else if (result === this.CODES.RANKS_IS_REQUIRED) {
      this.popupService.showAlert(this.translate('ERROR'), this.translate('RANKS_IS_REQUIRED'), this.translate('TOASTER_SUCCESS_TYPE'));
      this.loggerService.error("ranks is required");
    } else if (result === this.CODES.WHOLESALER_IS_REQUIRED) {
      this.popupService.showAlert(this.translate('ERROR'), this.translate('WHOLESALER_IS_REQUIRED'), this.translate('TOASTER_SUCCESS_TYPE'));
      this.loggerService.error("Wholesaler is required");
    } else if (result === this.CODES.QUANTITY_IS_REQUIRED) {
      this.popupService.showAlert(this.translate('ERROR'), this.translate('QUANTITY_IS_REQUIRED'), this.translate('TOASTER_SUCCESS_TYPE'));
      this.loggerService.error("quantity is required");
    }
  }
  async setVisitToTimeTracking(timeTracking: number): Promise<void> {
    try {
      const query = 'UPDATE presentation_time_tracking SET visit_id = ? WHERE id = ?';
      const values = [this.visit_id, timeTracking];
      
      await this.mDb.query(query, values);
      this.loggerService.info("presentation time tracking is required");
    } catch (error) {
      if (error instanceof Error) {
        this.loggerService.error("Error updating presentation time tracking: " + error.message);
      } else {
        this.loggerService.error("Unknown error updating presentation time tracking");
      }
    }
  }
  send(): void {
    this.syncService.send(); 
  }
  async withdrawPo(index: number): Promise<void> {
    this.loggerService.info("function : showResetOrderDialog for réinitialiser ordres");

    const confirmPopup = await this.alertController.create({
      header: this.translate('CONFIRMATION'), 
      message: this.translate('WITHDRAW_PURCHASE_ORDER_MESSAGE'), 
      buttons: [
        {
          text: this.translate('CANCEL'), 
          role: 'cancel',
          handler: () => {
            this.loggerService.info('withdraw purchase order');
          }
        },
        {
          text: this.translate('OK'), 
          handler: () => {
            this.purchaseOrders.splice(index, 1); 
          }
        }
      ]
    });

    await confirmPopup.present(); 
  }
  async saveVisitProducts(purchaseOrder: any): Promise<void> {
    const updateVisitProductQuery = `
      UPDATE visit_product 
      SET visit_id = ?, purchase_order_id = ?, product_id = ?, comment = ?, sample_quantity = ?, 
          order_quantity = ?, rank = ?, smily = ?, sale_quantity = ?, urgent = ?, prescription_quantity = ?, 
          freeOrder = ?, lab_gratuity = ?, synchronized = ? 
      WHERE id = ?`;
  
    const visitProductToSave = [];
    this.visitProductToInsert = [];
  
    this.loggerService.info(`Starting saveVisitProducts with ${purchaseOrder.visitProducts.length} visitProducts`);
  
    for (let i = 0; i < purchaseOrder.visitProducts.length; i++) {
      const vp = purchaseOrder.visitProducts[i];
      this.loggerService.info(`Processing visitProduct[${i}]: ${JSON.stringify(vp)}`);
  
      if (vp.rank !== undefined) {
        visitProductToSave.push(vp);
        this.loggerService.info(`Added to visitProductToSave (has rank): product_id=${vp.product_id}`);
      } else if (vp.order_quantity !== undefined) {
        vp.rank = 0;
        visitProductToSave.push(vp);
        this.loggerService.info(`Added to visitProductToSave (order_quantity fallback): product_id=${vp.product_id}`);
      } else {
        this.loggerService.info(`Skipped visitProduct[${i}]: missing rank and order_quantity`);
      }
    }
  
    this.loggerService.info(`Total products to save: ${visitProductToSave.length}`);
  
    const timeStamp = new Date().getTime();
  
    for (let i = 0; i < visitProductToSave.length; i++) {
      const vp = visitProductToSave[i];
      let data: any[] = [];
  
      // Default/fallback values
      vp.order_quantity = vp.order_quantity ?? 0;
      vp.sample_quantity = vp.sample_quantity ?? 0;
      vp.sale_quantity = vp.sale_quantity ?? 0;
      vp.urgent = vp.urgent ?? false;
      vp.prescription_quantity = vp.prescription_quantity ?? 0;
      vp.wholesaler_id = vp.wholesaler_id ?? null;
      vp.freeOrder = vp.freeOrder ?? 0;
      vp.labGratuity = vp.labGratuity ?? 0;
  
      if (vp.id === 0) {
        const id = timeStamp + i;
  
        data = [
          id,
          this.visit_id,
          purchaseOrder.purchaseOrderId,
          vp.product_id,
          vp.comment,
          vp.sample_quantity,
          vp.order_quantity,
          vp.rank,
          vp.smily,
          vp.sale_quantity,
          vp.urgent,
          vp.prescription_quantity,
          vp.freeOrder,
          vp.labGratuity,
          'NEW',
          0
        ];
  
        vp.id = id;
        this.visitProductToInsert.push(data);
  
        this.loggerService.info(`Prepared NEW visitProduct insert: product_id=${vp.product_id}, data=${JSON.stringify(data)}`);
      } else {
        data = [
          this.visit_id,
          purchaseOrder.purchaseOrderId,
          vp.product_id,
          vp.comment,
          vp.sample_quantity,
          vp.order_quantity,
          vp.rank,
          vp.smily,
          vp.sale_quantity,
          vp.urgent,
          vp.prescription_quantity,
          vp.freeOrder,
          vp.labGratuity,
          0,
          vp.id,
        ];
  
        try {
          this.loggerService.info(`Updating visit_product id=${vp.id}, data=${JSON.stringify(data)}`);
          await this.mDb.query(updateVisitProductQuery, data);
          this.loggerService.info(`Successfully updated visit_product id=${vp.id}`);
        } catch (error) {
          if (error instanceof Error) {
            this.loggerService.error(`Error updating visit_product id=${vp.id}: ${error.message}`);
          } else {
            this.loggerService.error('Unknown error occurred while saving visit product');
          }
        }
      }
    }
  
    this.loggerService.info(`Inserting new visitProducts (${this.visitProductToInsert.length})...`);
    this.insertvisitProduct(); 
  }
  
  async insertvisitProduct(): Promise<void> {
    const insertVisitProductQuery = `
      INSERT INTO visit_product 
      (id, visit_id, purchase_order_id, product_id, comment, sample_quantity, order_quantity, rank, smily, sale_quantity, urgent, prescription_quantity, freeOrder, lab_gratuity, status, synchronized) 
      VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`;
  
    for (let i = 0; i < this.visitProductToInsert.length; i++) {
      const data = this.visitProductToInsert[i];
      try {
        this.loggerService.info(`Inserting visitProduct[${i}]: ${JSON.stringify(data)}`);
        await this.mDb.query(insertVisitProductQuery, data);
        this.loggerService.info(`Successfully inserted visitProduct[${i}]`);
      } catch (error) {
        this.loggerService.error(`Error inserting visitProduct[${i}]: ${error}`);
      }
    }
  }
  
  async savePurchaseOrder(purchaseOrder: any, v: number): Promise<void> {
  this.loggerService.info('savePurchaseOrder called');

  let attachmentName = '';
  if (purchaseOrder.attachment.base64Image !=='') {
    attachmentName = purchaseOrder.purchaseOrderId + '_' + new Date().getTime() + '.png';
    this.loggerService.info(`Attachment name generated: ${attachmentName}`);
  }

  try {
    if (purchaseOrder.purchaseOrderId === 0) {
      this.loggerService.info('Inserting new purchase order...');
      console.log( purchaseOrder.attachment.base64Image,"NAME",
        attachmentName,)
      const id = new Date().getTime() + v;
      const purchaseData = [
        id,
        this.visit_id,
        purchaseOrder.attachment.base64Image,
        attachmentName,
        purchaseOrder.selectedWholesaler.id,
        purchaseOrder.placementMethod,
        purchaseOrder.purchaseOrderTemplateId,
        purchaseOrder.generatePo,
        purchaseOrder.generateDo,
        'WAITING_FOR_VALIDATION',
        0
      ];

      this.loggerService.info(`New purchaseOrder ID: ${id}`);
      this.loggerService.info(`Purchase order data: ${JSON.stringify(purchaseData)}`);

      const result = await this.mDb.query(QUERIES.INSERT_NEW_PURCHASE_ORDER, purchaseData);
      this.loggerService.info(`Insert result: ${JSON.stringify(result)}`);

      purchaseOrder.purchaseOrderId = id;

      this.loggerService.info('Calling saveVisitProducts...');
      await this.saveVisitProducts(purchaseOrder);

      this.loggerService.info('Calling saveRecovery...');
      await this.saveRecovery(purchaseOrder);

    } else {
      this.loggerService.info(`Updating existing purchase order ID: ${purchaseOrder.purchaseOrderId}`);

      if (purchaseOrder.isPurchaseOrderUpdated) {
        this.loggerService.info('Purchase order marked as updated. Proceeding with update...');

        const updateData = [
          purchaseOrder.attachment.base64Image,
          attachmentName,
          purchaseOrder.selectedWholesaler.id,
          purchaseOrder.placementMethod,
          purchaseOrder.generatePo,
          purchaseOrder.generateDo,
          purchaseOrder.purchaseOrderId
        ];

        this.loggerService.info(`Update data: ${JSON.stringify(updateData)}`);

        const updateResult = await this.mDb.query(QUERIES.UPDATE_PURCHASE_ORDER, updateData);
        this.loggerService.info(`Update result: ${JSON.stringify(updateResult)}`);

        if ((updateResult as any).rowsAffected > 0) {
          this.loggerService.info('Update successful. Saving visit products and recovery...');
          await this.saveVisitProducts(purchaseOrder);
          await this.saveRecovery(purchaseOrder);
        } else {
          this.loggerService.info('Update query ran but did not affect any rows.');
        }

      } else {
        this.loggerService.info('Purchase order not marked as updated. Skipping update, saving products and recovery...');
        await this.saveVisitProducts(purchaseOrder);
        await this.saveRecovery(purchaseOrder);
      }
    }

    if (this.sqliteService.platform === 'web') {
      this.loggerService.info('Saving database to IndexedDB (web platform)...');
      await this.sqliteService.sqliteConnection.saveToStore(this.dbCreationTablesService.databaseName);
      this.loggerService.info('Database successfully saved to store.');
    }

    this.loggerService.info('savePurchaseOrder finished successfully.');

  } catch (error) {
    if (error instanceof Error) {
      this.loggerService.error('Error saving purchase order: ' + error.message);
    } else {
      this.loggerService.error('Unknown error occurred while saving purchase order');
    }
  }
}

  async saveMessageTag() {
    const deleteAllMessageTag = 'delete from message_tag where visit_id=?';
    try {
      await this.mDb.query(deleteAllMessageTag, [this.visit_id]);
      
      const insertMassageTagQuery = 'insert into message_tag (id, visit_id, user_id, synchronized) values (?,?,?,?)';
      for (let i = 0; i < this.tagedUsers.length; i++) {
        const id = new Date().getTime() + i;
        try {
          await this.mDb.query(insertMassageTagQuery, [id, this.visit_id, this.tagedUsers[i].id, 0]);
        } catch (error) {
          if (error instanceof Error) {
            this.loggerService.error(error.message);
          } else {
            this.loggerService.error('An unknown error occurred');
          }
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.loggerService.error(error.message);
      } else {
        this.loggerService.error('An unknown error occurred');
      }
    }
  }
  async saveRecovery(purchaseOrder: any) {
    let result = '';
    console.log("ENTERED SAVE RECOVERY",purchaseOrder);
    // Check if recovery information exists
    if (purchaseOrder.recovery.amount == null && purchaseOrder.recovery.payment == null) {
      result = this.CODES.RECOVERY_NOT_EXIST;
    } else if (purchaseOrder.recovery.amount == null) {
      result = this.CODES.RECOVERY_AMOUNT_IS_REQUIRED;
    } else if (purchaseOrder.recovery.payment == null) {
      result = this.CODES.RECOVERY_PAYMENT_IS_REQUIRED;
    }
  
    // If there are no errors in the recovery information
    if (
      result !== this.CODES.RECOVERY_NOT_EXIST &&
      result !== this.CODES.RECOVERY_AMOUNT_IS_REQUIRED &&
      result !== this.CODES.RECOVERY_PAYMENT_IS_REQUIRED
    ) {
      // If an attachment exists, save it
      if (
        purchaseOrder.recovery.attachment != null &&
        purchaseOrder.recovery.attachment.base64Image != null &&
        purchaseOrder.recovery.attachment.base64Image !== ''
      ) {
        await this.saveAttachment(
          purchaseOrder.recovery.attachment,
          purchaseOrder.recovery.attachment_id,
          purchaseOrder.recovery.isRecoveryUpdated,
          purchaseOrder
        );
      } else {
        
        const recoveryId = purchaseOrder.recovery.id || 0; 
  
        if (recoveryId > 0) {
          
          await this.updateRecovery(recoveryId, purchaseOrder);
        } else {
          
          const amount = purchaseOrder.recovery.amount || 0; 
          const payment = purchaseOrder.recovery.payment || 0; 
  
          await this.insertRecovery(amount, payment, purchaseOrder);
        }
      }
    } else if (result === this.CODES.RECOVERY_AMOUNT_IS_REQUIRED) {
      this.popupService.showAlert(
        'Error',
        'Recovery amount is required',
        'success'
      );
      this.loggerService.error('Recovery amount is required');
    } else if (result === this.CODES.RECOVERY_PAYMENT_IS_REQUIRED) {
      this.popupService.showAlert(
        'Error',
        'Recovery payment method is required',
        'success'
      );
      this.loggerService.error('Recovery payment method is required');
    }
  }
  
  saveAttachment(attachment: any, attachment_id: number | null, isUpdated: boolean, purchaseOrder: any) {
    this.loggerService.info(`saveAttachment called | attachment_id=${attachment_id}, isUpdated=${isUpdated}`);
  
    if (attachment_id == null || attachment_id == undefined) {
      this.loggerService.info('attachment_id is null/undefined, defaulting to 0');
      attachment_id = 0;
    }
  
    let attachmentName = '';
    if (attachment.base64Image !== '') {
      attachmentName = `${attachment_id}.png`;
      this.loggerService.info(`Attachment name generated: ${attachmentName}`);
    }
  
    if (attachment_id === 0) {
      const id = new Date().getTime();
      const data = [id, attachment.base64Image, attachmentName, 'NEW', 0];
      this.loggerService.info(`Inserting new attachment | Data: ${JSON.stringify(data)}`);
  
      this.mDb.query(QUERIES.INSERT_NEW_ATTACHMENT, data)
         .then((tr: any) => {
          const insertedId = tr.changes?.lastId ?? id;
          this.loggerService.info(`Attachment inserted | ID: ${insertedId}`);
          if (purchaseOrder.recovery?.id) {
            this.loggerService.info('Updating existing recovery after attachment insert');
            this.updateRecovery(insertedId, purchaseOrder);
          } else {
            this.loggerService.info('Inserting new recovery after attachment insert');
            this.insertRecovery(insertedId, attachment, purchaseOrder);
          }
        })
        .catch((error: any) => {
          this.loggerService.warn(`Failed to insert attachment: ${error.message}`);
        });
  
    } else if (isUpdated) {
      this.loggerService.info(`Updating attachment | ID: ${attachment_id}`);
      this.mDb.query(QUERIES.UPDATE_ATTACHMENT, [attachment.base64Image, attachmentName, attachment_id])
        .then(() => {
          this.loggerService.info(`Attachment updated | ID: ${attachment_id}`);
          this.updateRecovery(attachment_id!, purchaseOrder);
        })
        .catch((error: any) => {
          this.loggerService.warn(`Failed to update attachment: ${error.message}`);
        });
    } else {
      this.loggerService.info('No operation performed on attachment (not new or updated)');
    }
  }
  
  updateRecovery(id: number, purchaseOrder: any) {
    this.loggerService.info(`updateRecovery called | ID: ${id}`);
  
    let recoveryDate = new Date(purchaseOrder.recovery.date);
    recoveryDate.setHours(0, 0, 0, 0);
  
    const data = [
      recoveryDate,
      purchaseOrder.recovery.amount,
      purchaseOrder.recovery.payment,
      purchaseOrder.recovery.description,
      id,
      'UPDATED',
      0,
      purchaseOrder.recovery.id
    ];
  
    this.loggerService.info(`Running updateRecovery query | Data: ${JSON.stringify(data)}`);
  
    const query = "UPDATE recovery SET date=?, amount=?, payment=?, description=?, attachment_id=?, status=?, synchronized=? WHERE id = ?";
  
    this.mDb.query(query, data)
      .then(() => {
        this.loggerService.info(`Recovery updated | ID: ${purchaseOrder.recovery.id}`);
        this.getRecovery(purchaseOrder);
  
        purchaseOrder.recovery = {
          date: new Date(),
          amount: null,
          payment: null,
          description: null,
          isRecoveryUpdated: false,
          attachment: { base64Image: '', attachmentName: '' }
        };
      })
      .catch((error: any) => {
        this.loggerService.warn(`Failed to update recovery: ${error.message}`);
      });
  }
  formatDateForSQL(date: Date): string {
    const yyyy = date.getFullYear();
    const mm = ('0' + (date.getMonth() + 1)).slice(-2);
    const dd = ('0' + date.getDate()).slice(-2);
    return `${yyyy}-${mm}-${dd}`; // ex: "2025-06-25"
  }
  insertRecovery(id: number, attachment: any, purchaseOrder: any) {
    this.loggerService.info(`insertRecovery called | attachment_id=${id}`);
  
    if (this.visit_id !== null && this.visit_id !== 0) {
      if (purchaseOrder.recovery.description === undefined) {
        purchaseOrder.recovery.description = '';
      }
  
      const recoveryId = new Date().getTime();
  
      const recoveryDate = new Date(purchaseOrder.recovery.date);
      recoveryDate.setHours(0, 0, 0, 0);
      const formattedDate = this.formatDateForSQL(recoveryDate);

      const data = [
        recoveryId,
        formattedDate,
        purchaseOrder.recovery.amount,
        purchaseOrder.recovery.payment,
        purchaseOrder.recovery.description,
        purchaseOrder.purchaseOrderId,
        id
      ];
  
      this.loggerService.info(`Inserting recovery | Data: ${JSON.stringify(data)}`);
  
      const saveRecoveryQuery = `INSERT INTO recovery 
        (id, date, amount, payment, description, purchase_order_id, attachment_id, status, synchronized) 
        VALUES (?, ?, ?, ?, ?, ?, ?, 'NEW', 0)`;
  
      this.mDb.run(saveRecoveryQuery, data)
        .then((rs: any) => {
          this.loggerService.info(`Recovery inserted | ID: ${recoveryId}`);
          purchaseOrder.recovery.id = rs.changes?.lastId ?? recoveryId;
          purchaseOrder.recovery.attachment_id = id;
          purchaseOrder.recoveryList.push(purchaseOrder.recovery);
  
          purchaseOrder.recovery = {
            date: new Date(),
            amount: null,
            payment: null,
            description: null,
            isRecoveryUpdated: false,
            attachment: { base64Image: '', attachmentName: '' }
          };
        })
        .catch((error: any) => {
          this.loggerService.warn(`Failed to insert recovery: ${error.message}`);
        });
    } else {
      this.loggerService.warn('insertRecovery skipped | visit_id is null or 0');
    }
  }
  getRecovery(purchaseOrder: any) {
    this.loggerService.info(`getRecovery called | purchaseOrderId=${purchaseOrder.purchaseOrderId}`);
  
    purchaseOrder.recoveryList = [];
    let recovery: any = [];
  
    const query = `SELECT r.*, a.attachmentBase64, a.attachmentName 
                   FROM recovery r 
                   LEFT JOIN attachment a ON (r.attachment_id=a.id) 
                   WHERE r.status <> 'DELETED' AND r.purchase_order_id = ?`;
  
    this.mDb.query(query, [purchaseOrder.purchaseOrderId])
      .then((res: any) => {
        this.loggerService.info(`getRecovery returned ${res.values.length} rows`);
        console.log(res)
        if (res.values.length > 0) {
          for (let i = 0; i < res.values.length; i++) {
            let attachment = {
              base64Image: res.values[i].attachmentBase64,
              name: res.values[i].attachmentName
            };
            recovery = res.values[i];
            recovery.attachment = attachment;
            purchaseOrder.recoveryList.push(recovery);
  
            purchaseOrder.recovery = {
              date: new Date(),
              amount: null,
              payment: null,
              description: null,
              isRecoveryUpdated: false,
              attachment: { base64Image: '', attachmentName: '' }
            };
          }
        }
      })
      .catch((error: any) => {
        this.loggerService.warn(`Failed to fetch recovery list: ${error.message}`);
      });
  }
  
  async deleteRecovery(index: number, recovery: any, purchaseOrder: any) {
    this.loggerService.info(`deleteRecovery called | recoveryId=${recovery.id}`);
  
    const alert = await this.alertController.create({
      header: this.translate('CONFIRMATION'),
      message: this.translate('ARE_YOU_SURE_YOU_WANT_TO_DELETE_THIS_OVERLAY'),
      buttons: [
        {
          text: this.translate('CANCEL'),
          role: 'cancel',
          handler: () => {
            this.loggerService.info('User cancelled recovery deletion');
          }
        },
        {
          text: this.translate('OK'),
          handler: () => {
            this.loggerService.info(`Deleting recovery | recoveryId=${recovery.id}, attachmentId=${recovery.attachment_id}`);
            this.commonService.deleteRecord('recovery', 'id', recovery.id);
            this.commonService.deleteRecord('attachment', 'id', recovery.attachment_id);
            this.loggerService.info('Recovery deleted successfully');
            purchaseOrder.recoveryList.splice(index, 1);
          }
        }
      ]
    });
  
    await alert.present();
  }
  
  selectToUpdateRecovery(recovery: any, purchaseOrder: any): void {
    purchaseOrder.recovery.id = recovery.id;
    purchaseOrder.recovery.date = recovery.date;
    purchaseOrder.recovery.amount = recovery.amount;
    purchaseOrder.recovery.payment = recovery.payment;
    purchaseOrder.recovery.description = recovery.description;
    purchaseOrder.recovery.attachment.base64Image = recovery.attachment.base64Image;
    purchaseOrder.recovery.attachment.name = recovery.attachment.name;
    purchaseOrder.recovery.attachment_id = recovery.attachment_id;
    purchaseOrder.recovery.isRecoveryUpdated = true;
  }
  
  async getCurrentPosition() {
    console.info('Click actualiser Ma position Buttom : getCurrentPosition');
    const loading = await this.loadingController.create({
      message: 'Chargement position...',
    });
    await loading.present();

    try {
      const position = await Geolocation.getCurrentPosition();

      this.zoom = 17;
      this.currentLocation.lat != position.coords.latitude;
      this.currentLocation.lng != position.coords.longitude;
      this.currentLocation.times != position.timestamp;

      this.setNotSavedLocationData(); // Replace with your method
      console.log('Position:', this.currentLocation);
    } catch (error: any) {
      console.error('Error in getCurrentPosition:', error.message);
      await this.handleError(error);
    } finally {
      await loading.dismiss();
    }
  }



async handleError(error: any) {
  let message = '';
  switch (error.code) {
    case 1: // PERMISSION_DENIED
      message = 'Vous avez refusé le suivi GPS.';
      break;
    case 2: // POSITION_UNAVAILABLE
      message = 'Votre position n\'est pas activée.';
      break;
    case 3: // TIMEOUT
      message = 'Délai dépassé. Veuillez vérifier l\'activation de votre position.';
      break;
    default:
      message = error.message;
  }

  const alert = await this.alertController.create({
    header: 'Erreur GPS',
    message,
    buttons: ['OK'],
  });
  await alert.present();
}
  
async saveCurrentPosition() {
  try {
    if (
      (this.currentLocation.lat === null && this.currentLocation.lng === null) ||
      (this.currentLocation.lat === 0 && this.currentLocation.lng === 0)
    ) {
      const alert = await this.alertController.create({
        header: 'Attention',
        message: this.translate('VERIFY_POSITION'),
        buttons: ['OK'],
      });
      await alert.present();
    } else {
      const query = `INSERT INTO geolocation (pos_timestamp, latitude, longitude) VALUES (?,?,?)`;
      await this.mDb.query(query, [
        this.currentLocation.times,
        this.currentLocation.lat,
        this.currentLocation.lng,
      ]);

      const selectVisitQuery =
        'SELECT v.* FROM visit v WHERE v.status <> ? AND v.prospect_id = ? AND v.visit_date = ? AND v.user_id = ?';
      const resultSet = await this.mDb.query(selectVisitQuery, [
        'DELETED',
        this.selectedProspect.id,
        this.visitDate!.getTime(),
        this.userId,
      ]);

      this.visitId = 0;
      if (resultSet.values!.length === 1) {
        this.visitId = resultSet.values![0].id;
      }

      if (this.visitId !== 0) {
        this.updateCurrentPosition(this.visitId);
      } else {
        this.insertCurrentPosition();
      }

      this.notSavedLocationData = false;

      this.notSavedData =
        this.notSavedVisitData || this.notSavedPoData || this.notSavedLocationData;

      const successAlert = await this.alertController.create({
        header: 'Opération réussie',
        message: this.translate('POSITION_SAVED_SUCCESSFULLY'),
        buttons: ['OK'],
      });
      await successAlert.present();
    }
  } catch (error: any) {
    console.error('Error saving position:', error.message);
    await this.alertController
      .create({
        header: 'Erreur',
        message: error.message,
        buttons: ['OK'],
      })
      .then(alert => alert.present());
  }
}

async updateCurrentPosition(visitId: number) {
  try {
    const updatePositionQuery =
      'UPDATE visit SET lat = ?, lng = ?, synchronized = ? WHERE id = ?';
    await this.mDb.query(updatePositionQuery, [
      this.currentLocation.lat,
      this.currentLocation.lng,
      0,
      visitId,
    ]);
    console.log('Position updated successfully.');
  } catch (error: any) {
    console.error('Error updating position:', error.message);
  }
}


async insertCurrentPosition() {
  try {
    const insertVisitQuery =
      'INSERT INTO visit (id, prospect_id, visit_date, general_note, patient_number, gadget_id, gadget_quantity, user_id, user_name, lat, lng, status, synchronized) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)';
    const data = [
      new Date().getTime(),
      this.selectedProspect.id,
      this.visitDate!.getTime(),
      this.visitData.generalNote,
      this.visitData.patientNumber,
      null,
      this.visitData.gadgetQuantity,
      this.currentUser.user_id,
      this.currentUser.first_last_name,
      this.currentLocation.lat,
      this.currentLocation.lng,
      'NEW',
      0,
    ];
    const resultSet = await this.mDb.query(insertVisitQuery, data);
    console.log('New visit inserted successfully.');

    if (this.visitId === 0) {
      const result = await this.mDb.query('SELECT max(id) from visit');
      this.visitId = result.values![0].id;
      //this.pointedProspectsIds.push(this.selectedProspect.id);
      this.selectedProspect.backgroundColor = this.colors.ORANGE;
    }
  } catch (error: any) {
    console.error('Error inserting new visit:', error.message);
  }
}
async saveCurrentandPosition() {
  try {
    // 1️⃣ Appel de la sauvegarde de la géolocalisation dans geolocation + position dans visit
    await this.saveCurrentPosition();

    // 2️⃣ Mise à jour des coordonnées dans prospect
    const updateQuery = `UPDATE prospect SET lat = ?, lng = ? WHERE id = ?`;

    await this.mDb.query(updateQuery, [
      this.currentLocation.lat,
      this.currentLocation.lng,
      this.selectedProspect.id,
    ]);

    const alert = await this.alertController.create({
      header: 'Succès',
      message: this.translate('POSITION_UPDATED_SUCCESSFULLY'),
      buttons: ['OK'],
    });
    await alert.present();
  } catch (error: any) {
    console.error('Erreur lors de la mise à jour de la position du prospect:', error.message);
    await this.alertController
      .create({
        header: 'Erreur',
        message: error.message,
        buttons: ['OK'],
      })
      .then(alert => alert.present());
  }
}


async onPositionClick() {
  this.markersPositions = [];
  let  position: any = null;

  try {
    position = await Geolocation.getCurrentPosition({
      timeout: 15000,
      enableHighAccuracy: true,
      maximumAge: 0,
    });
  } catch (error: any) {
    console.error('Error fetching current position:', error);
    await this.handleError(error);
  }

  if (position) {
    this.currentLocation.lat = position.coords.latitude;
    this.currentLocation.lng = position.coords.longitude;
    this.currentLocation.name = 'Current Location';
    this.markersPositions.push(this.currentLocation);
  } else if (this.selectedProspect?.lat && this.selectedProspect?.lng) {
    // fall back to selected prospect coordinates when location is unavailable
    this.currentLocation.lat = this.selectedProspect.lat;
    this.currentLocation.lng = this.selectedProspect.lng;
  }

  try {
    const query = `
      SELECT firstname || ' ' || lastname AS name, lat, lng
      FROM prospect
      WHERE lat IS NOT NULL AND lng IS NOT NULL AND lat <> 0 AND lng <> 0
        AND id = ?
        AND status <> 'NOT_AFFECTED'
    `;

    const prospects = await this.mDb.query(query, [this.selectedProspect.id]);
    for (const prospect of prospects.values!) {
      const marker = {
        lat: prospect.lat,
        lng: prospect.lng,
        name: prospect.name,
        icon: {
          path: 'M 0,0 C -2,-20 -10,-22 -10,-30 A 10,10 0 1,1 10,-30 C 10,-22 2,-20 0,0 z',
          fillColor: 'red',
          fillOpacity: 0.8,
          strokeWeight: 0,
        },
      };
      this.markersPositions.push(marker);
    }
  } catch (error) {
    console.error('Error fetching prospects:', error);
  }

  setTimeout(() => this.renderBottomMap(), 0);
}
validateCommentsAccordingToJSON(): { isValid: boolean; errorMessage: string } {
  console.log("🔍 validateCommentsAccordingToJSON called")

  // Vérifier si le JSON parsé existe dans currentUser
  if (!this.currentUser?.comments_dictionary || !this.currentUser?.work_type) {
    console.log("❌ No comments dictionary or work_type found")
    return { isValid: true, errorMessage: "" } // Pas de validation si pas de JSON
  }

  const workType = this.canOrder ? 'pharmaceutical' : 'medical';
  console.log("🔍 Checking validation for work_type:", workType)

  // Accéder directement au JSON parsé du currentUser
  const commentData = this.currentUser.comments_dictionary[workType]

  if (!commentData) {
    console.log("❌ No comment data found for work_type:", workType)
    return { isValid: true, errorMessage: "" } // Pas de validation si pas de données
  }

  console.log("📋 Comment data for", workType, ":", commentData)

  // 1️⃣ VALIDATION DES COMMENTAIRES PRODUITS
  if (commentData.product_comment?.required !== undefined) {
    const requiredCount = commentData.product_comment.required
    console.log("🔍 Product comments required:", requiredCount, "type:", typeof requiredCount)

    if (typeof requiredCount === "number" && requiredCount > 0) {
      // Compter les commentaires produits saisis
      let productCommentsCount = 0

      if (this.visit?.visitProducts) {
        for (const visitProduct of this.visit.visitProducts) {
          if (visitProduct.comment && visitProduct.comment.trim().length > 0) {
            productCommentsCount++
          }
        }
      }

      console.log(`📊 Product comments found: ${productCommentsCount}, required: ${requiredCount}`)

      if (productCommentsCount < requiredCount) {
        const message = `Il faut saisir ${requiredCount} commentaire${requiredCount > 1 ? "s" : ""} produit${requiredCount > 1 ? "s" : ""}`
        console.log("❌ Product comments validation failed:", message)
        return { isValid: false, errorMessage: message }
      }
    }
  }

  // 2️⃣ VALIDATION DU COMMENTAIRE GÉNÉRAL
  if (commentData.general_comment?.required !== undefined) {
    const isGeneralRequired = commentData.general_comment.required
    console.log("🔍 General comment required:", isGeneralRequired, "type:", typeof isGeneralRequired)

    // Vérifier si c'est true (boolean) ou 1 (number)
    const isRequired =
      (typeof isGeneralRequired === "boolean" && isGeneralRequired) ||
      (typeof isGeneralRequired === "number" && isGeneralRequired !== 0)

    if (isRequired) {
      const generalComment = this.visitData?.generalNote?.trim() || ""
      console.log("📝 General comment content:", generalComment)

      if (generalComment.length === 0 && this.selectedTab == 'VISIT') {
        const message = "Champ commentaire général obligatoire"
        console.log("❌ General comment validation failed:", message)
        return { isValid: false, errorMessage: message }
      }
    }
  }

  console.log("✅ All comment validations passed")
  return { isValid: true, errorMessage: "" }
}



}
    

          

        
  
  
  
  
  
