<ion-app>
  <ion-router-outlet></ion-router-outlet>
  <app-console></app-console>
  
  <ion-footer *ngIf="showFooter" class="global-footer">
    <ion-toolbar>
      <ion-tab-bar>
        <ion-tab-button [routerLink]="['/home']">
          <ion-icon name="home"></ion-icon>
          <ion-label>HOME</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/planning']">
          <ion-icon name="calendar"></ion-icon>
          <ion-label>PLANNING</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/prospect-detail']">
          <ion-icon name="people"></ion-icon>
          <ion-label>PROSPECTS</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/report']">
          <ion-icon name="document-text"></ion-icon>
          <ion-label>RAPPORT</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/visit-history']">
          <ion-icon name="time"></ion-icon>
          <ion-label>HISTORIQUE</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/calendar']">
          <ion-icon name="calendar-outline"></ion-icon>
          <ion-label>CALENDRIER</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/goals']">
          <ion-icon name="trophy"></ion-icon>
          <ion-label>OBJECTIFS</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/expense-list']">
          <ion-icon name="card"></ion-icon>
          <ion-label>FRAIS</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/action-marketing']">
          <ion-icon name="megaphone"></ion-icon>
          <ion-label>MARKETING</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/message']">
          <ion-icon name="mail"></ion-icon>
          <ion-label>MESSAGE</ion-label>
        </ion-tab-button>
        <ion-tab-button [routerLink]="['/notifications']">
          <ion-icon name="notifications"></ion-icon>
          <ion-label>NOTIFICATIONS</ion-label>
        </ion-tab-button>
      </ion-tab-bar>
    </ion-toolbar>
  </ion-footer>
</ion-app>