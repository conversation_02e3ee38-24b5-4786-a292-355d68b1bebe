export interface Product {
    id: number;
    name: string;
    // Add other properties that a product has
  }
  
  export interface Prospect {
    id: number;
    name: string;
    // Add other properties that a prospect has
  }
  export const MOCK_PRODUCTS = [
    { id: '1', name: 'Product 1' },
    { id: '2', name: 'Product 2' },
    { id: '3', name: 'Product 3' }
  ];
  
  export const MOCK_PROSPECTS = [
    { id: '1', name: 'Prospect 1' },
    { id: '2', name: 'Prospect 2' },
    { id: '3', name: 'Prospect 3' }
  ];