import { InjectionToken } from '@angular/core';

export const LABO_PATH = new InjectionToken('LABO_PATH', {
  providedIn: 'root',
  factory: () => ({
    dev_path: "https://demo.bird-notes.com/api",
    farmavans_path: 'https://letsco.tn:9901',
    upharma_path: 'https://letsco.tn:9902',
    promed_path: 'https://app.bird-notes.com:9401',
    stoderma_path: 'https://app.bird-notes.com:9402',
    pharmavision_path: 'https://app.bird-notes.com:9403',
    glow_path: 'https://app.bird-notes.com:9404',
    therapia_path: 'https://app.bird-notes.com:9400',
    demo_path: 'http://194.163.132.114:30002/api',
    bmm_tn_path: 'https://bmmgroup.biz:100',
    bmm_test_path: 'https://bmmgroup.biz:600',
    shbpharma_path: 'https://app.bird-notes.com:9405',
    belpharma_path: 'https://app.bird-notes.com:9406',
  }),
});

export const APP_INFO = new InjectionToken('APP_INFO', {
  providedIn: 'root',
  factory: () => ({
    version: '5.0.0',
    VERSION_NUMBER: 500
  }),
});

export const MESSAGES = new InjectionToken('MESSAGES', {
  providedIn: 'root',
  factory: () => ({
  LOADING: 'Loading, please wait...',
  BAD_CREDENTIALS: "Nom d'utilisateur ou mot de passe incorrect",
  NO_INTERNET: "Vérifiez votre connection internet",
  BIEN: 'Info',
  ERROR: 'Erreur',
  INFO: 'Info',
  RANKS_IS_NOT_UNIQUE: 'Vous avez saisi le même ordre pour deux produits differents',
  RNAK_IS_ZERO: 'Vous avez saisi un ordre égal à zéro',
  RANK_EXCIT_THE_LIMIT: 'Vous avez saisi un ordre supérieur aux nombres des produits',
  RANKS_IS_EMPTY: "Veuillez saisir l'ordre des produits que vouz avez présenté",
  RANKS_IS_REQUIRED: "Vous n'avez pas saisi un ordre pour le produit que vous avez présenté",
  WHOLESALER_IS_REQUIRED: "Vous n'avez pas choisi le grossiste",
  QUANTITY_IS_REQUIRED: "Vous n'avez pas saisi les quantités des produits",
  RECOVERY_AMOUNT_IS_REQUIRED: "Vous n'avez pas saisi le montant de recouvrement",
  RECOVERY_PAYMENT_IS_REQUIRED: "Vous n'avez pas saisi la méthode de payment du recouvrement",
  CHECK_INTERNET: "Impossible de se connecter au serveur, vérifiez votre connection internet",
  SYNC_ERROR: "Une erreur est survenue lors de la synchronisation",
  GADGET_REQUIRED : "Veuillez saisir la quantité du gadget selectionné",
  COMMENT_IS_NOT_EXIST: "Veuillez saisir au moins un commentaire pour un des produits présentés",
  ATTACHMENT_IS_NOT_EXIST: "Vous avez oublié la pièce jointe, veuillez choisir le fichier de votre bon de commande",
  WHOLESALER_IS_NOT_EXIST: "Vous avez oublié de choisir le grossiste de la commande",
  PRICE_IS_ZERO: "Vous avez oublié de saisir la quantité pour les produits commandés",
  TOASTER_SUCCESS_TYPE: 'Success',
  TOASTER_ERROR_TYPE: 'Error',
  VISIT_INSERTED_SUCCESS: 'Votre rapport est bien enregistré',
  PO_INSERTED_SUCCESS: 'Votre commande est bien enregistrée',
  SAVE_ERROR: 'Une erreur est survenue',
  SYNCHRONISATION_SEND_SUCCESS: "L'envoie des données est terminé avec succès",
  SYNCHRONISATION_RECEIVE_SUCCESS:  "La réception des données est terminé avec succès",
  SYNCHRONISATION_ERROR: 'Une erreur est survenue lors de la synchronisation',
  SYNCHRONISATION_INFO: "Vous n'avez pas synchronisé depuis",
  UPLOAD_SUCCESS: "Le téléchargement de votre pièce jointe est terminé",
  IMPOSSIBLE_DELETE_EXPENSE_REPORT_SYNCHRONIZE: "Vous ne pouvez pas supprimer une note de frais synchronisée",
  ACCESS_DENIED_ERROR : "Votre session a expiré, merci de bien vouloir vous reconnecter"
  }),
});

export const CODES = new InjectionToken('CODES', {
  providedIn: 'root',
  factory: () => ({
    RANKS_IS_NOT_UNIQUE: 0,
    RANKS_IS_REQUIRED: 1,
    RANK_EXCIT_THE_LIMIT: 2,
    RANKS_IS_UNIQUE: 3,
    RANKS_IS_EMPTY: 4,
    COMMENT_IS_NOT_EXIST: 5,
    GADGET_REQUIRED: 6,
    QUANTITY_AND_WHOLESALER_EXISTS : 7,
    WHOLESALER_IS_REQUIRED : 8,
    RECOVERY_NOT_EXIST : 9,
    RECOVERY_AMOUNT_IS_REQUIRED : 10,
    RECOVERY_PAYMENT_IS_REQUIRED : 11,
    QUANTITY_IS_REQUIRED : 12
    
  }),
});

interface Colors {
  RED: string;
  GREEN: string;
  ORANGE: string;
  WHITE: string;
  BLACK: string;

}

export const COLORS = new InjectionToken<Colors>('COLORS', {
  providedIn: 'root',
  factory: () => ({
    RED: 'red',
    GREEN: '#74b90a',
    ORANGE: '#E99921',
    WHITE: '#FFFFFF',
    BLACK: '#000000'
  }),
});
export const MAP = new InjectionToken('MAP', {
  providedIn: 'root',
  factory: () => ({
    LAT_DEFAULT_VALUE: 0.0,
    LNG_DEFAULT_VALUE: 0.0,
    LAT_CENTER_VALUE: 37.11760369812085,
    LNG_CENTER_VALUE: 10.518839132053268,
    ZOOM_2 : 2,
    ZOOM_6 : 6,
    ZOOM_8 : 8,
    ZOOM_12: 12,
    ZOOM_17: 17
  }),
});

export const PAGE_TITLES = new InjectionToken('PAGE_TITLES', {
  providedIn: 'root',
  factory: () => ({
    HOME: 'Accueil',
    RAPPORT: 'Saisie nouveau rapport',
    VISIT_HISTORY: 'Historique des visites',
    ADD_PROSPECT: 'Ajouter prospect',
    ADD_NOTEFRAIS: 'Ajouter note de frais',
    LIST_NOTEFRAIS: 'Liste des notes de frais',
    CALENDAR: 'Calendrier des activités',
    PLANNING: 'Planification',
    ACTIONMARKETING: 'Gestion des actions marketing',
    NOTIFICATION: 'Notification',
    BUDGET_ALLOCATION: 'Budget aloué',
    NEXT_ACTION_RULE: 'Règle de la prochaine action',
    OPPORTUNITYNOTE : 'Gestion des notes des oppotunités',
    TROUBLESHOOTING : 'Dépannage',
    GOAL : 'Objectif'
  }),
});

export const ORDER = new InjectionToken('ORDER', {
  providedIn: 'root',
  factory: () => ({
    SPECIALITY: 'Ordonner par : Spécialité',
    NAME : 'Ordonner par : Nom',
    SECTOR : 'Ordonner par : Secteur'
  }),
});

export const NAME_FILE = new InjectionToken('NAME_FILE', {
  providedIn: 'root',
  factory: () => ({
    FILE_LOG: 'BirdNoteLog.txt'
  }),
});

export const QUERIES = new InjectionToken('QUERIES', {
  providedIn: 'root',
  factory: () => ({
  SELECT_ALL_FROM_LOCALITY: 'SELECT * from locality ORDER BY name',
  SELECT_ALL_FROM_PROSPECT: 'SELECT * from prospect ',
  SELECT_ALL_NAME_ID_FROM_PROSPECT: "SELECT p.id, p.firstname, p.lastname FROM prospect p ORDER BY p.firstname, p.lastname and p.status <> 'NOT_AFFECTED'",
  SELECT_ALL_NAME_ID_FROM_PHARMACIE: " SELECT p.id, p.firstname, p.lastname  from prospect p inner join speciality s on p.speciality_id == s.id  WHERE s.name like 'PH%' ",
  SELECT_ALL_FROM_NOTEFRAIS: 'SELECT n.*, t.name as expenseTypeName, at.name as activityType from expense n inner join expense_type t on t.id = n.expenseTypeId Left outer join activity a on (n.activityId = a.id) left outer  join activityType at on (a.activityTypeId = at.id)',
  SELECT_EXPENSE_BY_DATE : 'SELECT n.*, t.name as expenseTypeName, at.name as activityType from expense n inner join expense_type t on t.id = n.expenseTypeId Left outer join activity a on (n.activityId = a.id) left outer  join activityType at on (a.activityTypeId = at.id) where n.expenseDate >= ?  AND n.expenseDate <= ?',
  SELECT_ALL_FROM_ACTIONMARKETING : "SELECT * from marketing_action where status <> 'DELETED'",
  SELECT_ALL_FROM_OPPORTINITYNOTE : "SELECT * from opportunitynote where status <> 'DELETED'",
  SELECT_ALL_FROM_NOTIFICATION :'SELECT * from notifications order by id desc',
  SELECT_ALL_FROM_SPECIALITY: 'SELECT * from speciality ORDER BY name',
  SELECT_ALL_FROM_POTENTIAL: 'SELECT * from potential',
  SELECT_ALL_FROM_TYPE: 'SELECT * from expense_type ORDER BY name',
  SELECT_ALL_FROM_SECTOR: 'SELECT * from sector ORDER BY name',
  SELECT_ALL_FROM_ESTABLISHMENT :'SELECT * FROM establishment ORDER BY name',
  SELECT_ALL_FROM_PRODUCT: 'SELECT * from product ORDER BY name',
  SELECT_ALL_FROM_WHOLESALER: "SELECT p.* from prospect p inner join speciality s on s.id = p.speciality_id where s.action % 2 = 1 and p.status <> 'NOT_AFFECTED' ORDER BY p.firstname, p.lastname ",
  SELECT_ALL_FROM_USERS: 'SELECT * from users ORDER BY name',
  SELECT_ALL_FROM_DELEGATES: 'SELECT * from users where delegate_id is not null ORDER BY name',
  SELECT_ALL_FROM_GADGET: 'SELECT * from gadget',
  SELECT_FROM_GADGET_BY_TYPE: 'SELECT * from gadget where type=?',
  INSERT_PROSPECT: `
    INSERT OR REPLACE INTO prospect (
      id, firstname, lastname, activity, potential, address, gsm, phone, email, 
      note, secretary, grade, speciality_id, sector_id, locality_id, lat, lng, 
      map_address, status, synchronized, validation, type_id, establishment_id, fiscal_number
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `,
  UPDATE_PROSPECT:
  "UPDATE prospect SET firstname=?, lastname=?,  activity=?, potential=?,address=?,gsm=?,phone=?,email=?,note=?, secretary=?, grade=?, speciality_id=?, sector_id =?, locality_id=?, lat=?,lng=?,map_address=?, status=?,  synchronized=?, validation=?, type_id=?, establishment_id=?, fiscal_number=? WHERE id = ?",
  UPDATE_NOTIFICATION :'UPDATE notifications SET status=1',

  INSERT_NEW_NOTEFRAIS: "INSERT OR REPLACE INTO expense (id, activityId, expenseDate, expenseTypeId, amount,description,attachementBase64,attachmentName, status,synchronized) VALUES (?,?,?,?,?,?,?,?,?,?)",
  INSERT_NOTEFRAIS: "INSERT OR REPLACE INTO expense (id, activity_id, expense_date, expense_type_id, montant, description, attachement_base64, attachment_name, status, synchronized) VALUES (?,?,?,?,?,?,?,?,'SENT', 1)",
  
  INSERT_NEW_ACTIONMARKETING: 'INSERT OR REPLACE INTO marketing_action (name, budget ,product_id, prospect_id, marketingAction_date, synchronized ,status, description) VALUES (?,?,?,?,?,?,?,?)',
  INSERT_ACTIONMARKETING: "INSERT OR REPLACE INTO marketing_action (id,name, budget ,product_id, prospect_id, marketingAction_date, synchronized ,status, description) VALUES (?,?,?,?,?,?,1, 'SENT', ?)",
  INSERT_NOTES: "INSERT OR REPLACE INTO note (id, sector_ids, speciality_ids ,activities, note, link) VALUES (?,?,?,?,?,?)",

  INSERT_NEW_PLANNING: 'INSERT INTO planning (id, prospect_id, planning_date, status, synchronized ) VALUES (?,?,?,?,?)',
  INSERT_PLANNING: "INSERT OR REPLACE INTO planning (id, prospect_id, planning_date, status, synchronized ) VALUES (?,?,?,'SENT', 1)",
  
  INSERT_NEW_PLANNING_VALIDATION: "INSERT OR REPLACE INTO planning_validation (id, notes, status, planning_validation_date, synchronized) VALUES (?,?,?,?,?)",
  INSERT_PLANNING_VALIDATION: "INSERT OR REPLACE INTO planning_validation (id, notes, planning_validation_date, status, synchronized) VALUES (?,?,?,?,1)",
  INSERT_PURCHASE_ORDER: "INSERT OR REPLACE INTO purchase_order (id , visit_id , attachmentBase64 , attachmentName , placement_method , wholesaler_id ,purchase_order_template_id, synchronized , status ) VALUES (?,?,?,?,?,?,?,1,'SENT')",
  INSERT_RECOVERY: "INSERT OR REPLACE INTO recovery  (id , date , payment , amount , attachment_id , description , purchase_order_id , status , synchronized ) VALUES (?,?,?,?,?,?,?,'SENT',1)",
  INSERT_ATTACHMENT: "INSERT OR REPLACE INTO attachment (id, attachmentBase64 , attachmentName ,status, synchronized ) VALUES (?,?,?,'SENT',1)",

 
  INSERT_PROSPECT_ORDER_PREDICTION  : "INSERT OR REPLACE INTO prospect_order_prediction (id, prediction_date, prospect_id , product_id , order_quantity) VALUES (?,?,?,?,?)",
  INSERT_NEW_ACTIVITY: 'INSERT OR REPLACE INTO activity (id, activity_date, hour_number, activityTypeId, comment ,status , synchronized) VALUES (?,?,?,?,?,?,?)',
  INSERT_ACTIVITY: "INSERT OR REPLACE INTO activity (id, activity_date, hour_number, activityTypeId, comment ,status , synchronized) VALUES (?,?,?,?,?, 'SENT', 1)",
  
  SELECT_MIN_ID_REPORT: 'SELECT min(id) as minId from  visit',
  SELECT_MIN_ID_ACTIONMARKETING: 'SELECT min(id) as minId from marketing_action',
  SELECT_MIN_ID_OPPORTUNITYNOTE: 'SELECT min(id) as minId from opportunitynote',
  SELECT_MAX_ID_GEOLOCATION:   'SELECT max(id) as maxId from geolocation' ,
  SELECT_MIN_ID_PLANNING: 'SELECT min(id) as minId from planning',
  SELECT_MIN_ID_PLANNING_VALIDATION: 'SELECT min(id) as minId from planning_validation',
  SELECT_MIN_ID_PROSPECT: 'SELECT min(id) as minId from prospect',
  SELECT_MIN_ID_NOTEFRAIS: 'SELECT min(id) as minId from expense',
  SELECT_MIN_ID_PURCHASEORDER: 'SELECT min(id) as minId from purchase_order',
  SELECT_MIN_ID_ACTIVITY: 'SELECT min(id) as minId from activity',
  INSERT_PRODUCT: 'INSERT OR REPLACE INTO product (id, name, price, buying_price, version, quantity_unit, package_quantity, stock, description) VALUES (?,?,?,?,?,?,?,?,?)',
  INSERT_WHOLESALER: 'INSERT OR REPLACE INTO wholesaler (id, name, responsible, phone, address, description) VALUES (?, ?, ?, ?, ?, ?)',
  UPDATE_PRODUCT: 'UPDATE product SET name = ?, price =? where id = ?',
  UPDATE_WHOLESALER: 'UPDATE wholesaler SET name = ?, responsible=?, phone=?, address=?, description=? where id = ?',
  UPDATE_PURCHASE_ORDER: 'UPDATE purchase_order SET  attachmentBase64=?, attachmentName=?, wholesaler_id=?, placement_method = ?,generate_po =?, generate_do=?, synchronized = 0 where id = ?',
  INSERT_POTENTIAL_PRODUCT: 'INSERT OR REPLACE INTO potential_product (id, potential_id, product_id, prospect_id) VALUES (?,?,?,?)',
  UPDATE_POTENTIAL_PRODUCT: 'UPDATE potential_product SET  potential_id=?, product_id=?, prospect_id =? where id = ?',
  UPDATE_ACTIONMARKETING_STATUS: 'UPDATE marketing_action SET status = ? where id = ?',
  UPDATE_EXPENSE_STATUS: 'UPDATE expense SET status = ? where id = ?',
  UPDATE_OPPORTUNITYNOTEN : 'UPDATE opportunitynote SET status = ? where id = ?',
  INSERT_GADGET: 'INSERT OR REPLACE INTO gadget (id, name, type) VALUES (?,?,?)',
  INSERT_ESTABLISHMENT: 'INSERT OR REPLACE INTO establishment (id, name, activity) VALUES (?,?,?)',
  UPDATE_GADGET: 'UPDATE gadget SET name = ? where id = ?',
  INSERT_SECTOR: 'INSERT OR REPLACE INTO sector (id, name) VALUES (?,?)',
  INSERT_LOCALITY: 'INSERT OR REPLACE INTO locality (id, name, sector_id) VALUES (?,?,?)',
  INSERT_SPECIALITY: 'INSERT OR REPLACE INTO speciality (id, name, action) VALUES (?,?,?)',
  INSERT_USERS: 'INSERT OR REPLACE INTO users (id, name, delegate_id) VALUES (?,?,?)',

  UPDATE_ATTACHMENT: 'UPDATE attachment SET  attachmentBase64=?, attachmentName=?, synchronized = 0 where id = ?',
 

  INSERT_ACTIVITY_TYPE: 'INSERT OR REPLACE INTO activityType (id, name) VALUES (?,?)',
  INSERT_POTENTIAL: 'INSERT OR REPLACE INTO potential (id, name) VALUES (?,?)',
  INSERT_NOTIFICATIONS: 'INSERT OR REPLACE INTO notifications (id, notification_text, notification_day, notification_month, notification_year,notification_hour, notification_minutes, status) VALUES (?,?,?,?,?,?,?,?)',
  INSERT_RANGE: 'INSERT OR REPLACE INTO range (id, name) VALUES (?,?)',
  DELETE_RANGE: 'delete from range',
  INSERT_PRODUCT_RANGE: 'INSERT OR REPLACE INTO product_range (range_id, product_id) VALUES (?,?)',
  INSERT_PRODUCT_DOCUMENTS: 'INSERT OR REPLACE INTO product_documents (name, document_base64, product_id) VALUES (?,?,?)',
  DELETE_PRODUCT_DOCUMENTS: 'DELETE FROM product_documents WHERE product_id = ?',
  DELETE_PRODUCT_RANGE: 'delete from product_range where product_id = ?',
  SELECT_ACTIVITY_TYPE_ID: 'SELECT id from activityType',
  SELECT_PROSPECT: "SELECT * from prospect where status <> 'NOT_AFFECTED' ORDER BY firstname, lastname",
  SELECT_PROSPECT_TO_SEND: "SELECT * from prospect where status <> '' and synchronized = 0 and status <> 'NOT_AFFECTED'",
  SELECT_NOTEFRAIS: 'SELECT * from expense',
  SELECT_EXPENSE_REPORT_TO_SEND: 'SELECT * from expense where synchronized = 0',
  SELECT_ACTIVITY_TO_SEND: 'SELECT * from activity where synchronized = 0',
  SELECT_PLANNING_TO_SEND: 'SELECT * from planning where synchronized = 0',
  SELECT_PLANNING_VALIDATION_TO_SEND: 'SELECT * from planning_validation where synchronized = 0',
  SELECT_ACTIONMARKETING_TO_SEND: 'SELECT * from marketing_action where synchronized = 0',
  SELECT_PURCHASEORDER_TO_SEND: 'SELECT * from purchase_order where synchronized = 0',
  SELECT_RECOVERY_TO_SEND: 'SELECT * from recovery where synchronized = 0',
  SELECT_ATTACHMENT_TO_SEND: 'SELECT * from attachment where synchronized = 0',
  SELECT_MESSAGE_TO_SEND: 'SELECT * from message where synchronized = 0',
  SELECT_TAGED_USER_TO_SEND: 'SELECT * from message_tag where synchronized = 0',

  SELECT_OPPORTUNITYNOTE_TO_SEND: 'SELECT * from opportunitynote where synchronized = 0',
  SELECT_LOCATION_TO_SEND:'SELECT * from geolocation',

  SELECT_ACTIVITY_TO_DELETE: "SELECT id from activity where status = 'DELETED' and synchronized = 1 ",
  SELECT_PLANNING_TO_DELETE: "SELECT id from planning where status = 'DELETED' and synchronized = 1 ",
  SELECT_PLANNING_VALIDATION_TO_DELETE: "SELECT id from planning_validation where  status =  'DELETED' and synchronized = 1",
  SELECT_VISIT_PRODUCT_TO_DELETE: "SELECT id from  visit_product where  status =  'DELETED' and synchronized = 1",
  SELECT_VISIT_TO_DELETE: "SELECT id from  visit where  status =  'DELETED' and synchronized = 1",
  SELECT_EXPENSE_REPORT_TO_DELETE: "SELECT id from  expense where  status =  'DELETED' and synchronized = 1",
  SELECT_PURCHASE_ORDER_TO_DELETE: "SELECT id from purchase_order where status  = 'DELETED' and synchronized = 1 ",
  SELECT_RECOVERY_TO_DELETE: "SELECT id from recovery where status  = 'DELETED' and synchronized = 1 ",
  SELECT_ATTACHMENT_TO_DELETE: "SELECT id from attachment where status  = 'DELETED' and synchronized = 1 ",
  SELECT_ACTIONMARKETING_TO_DELETE: "SELECT id from marketing_action where status= 'DELETED' and synchronized = 1",
  SELECT_OPPORTUNITYNOTE_TO_DELETE: "SELECT * from opportunitynote where status= 'DELETED'",

  SELECT_REPORT_BY_MONTH_AND_YEAR: "SELECT * from  visit WHERE visit_date_month = ? and visit_date_year = ? ",
  SELECT_VISIT_TO_SEND: "SELECT * from  visit WHERE  user_id = ? and synchronized = 0",
  SELECT_TIME_TRACKING_TO_SEND: "SELECT ptt.*, pd.product_id as productId, pd.name as documentName from  presentation_time_tracking ptt inner join product_documents pd on ptt.product_document_id = pd.id WHERE synchronized = 0",
  SELECT_VISIT_PRODUCT_TO_SEND: "SELECT vp.* from  visit_product vp INNER JOIN visit v ON (vp.visit_id = v.id)  WHERE  v.user_id = ? and vp.synchronized = 0",
  UPDATE_REPORT_PROSPECT_ID: "UPDATE  visit SET prospect_id = ?  WHERE prospect_id = ?",
  UPDATE_MARKETING_ACTION_PROSPECT_ID: "UPDATE marketing_action SET prospect_id = ?  WHERE prospect_id = ?",
  UPDATE_PLANNING_PROSPECT_ID: "UPDATE planning SET prospect_id = ?  WHERE prospect_id = ?",
  UPDATE_OPPORTINUTY_NOTE_PROSPECT_ID: "UPDATE opportunitynote  SET prospect_id = ?  WHERE prospect_id = ?",
  UPDATE_PURCHASE_ORDER_PROSPECT_ID: "UPDATE purchase_order SET prospect_id = ?  WHERE prospect_id = ?",
  UPDATE_REPORTS_ID: "UPDATE  visit SET id = ? WHERE id = ?",
  UPDATE_NOTEFRAIS_ID: "UPDATE expense SET id = ?, synchronized = 1 WHERE id = ?",
  UPDATE_PURCHASEORDER_ID: "UPDATE purchase_order SET id = ?, synchronized = 1 WHERE id = ? ",
  UPDATE_ACTIVITY: "UPDATE activity SET hour_number = ?,activityTypeId = ?, comment = ?, status = ? , synchronized = ? WHERE id = ?",
  UPDATE_PLANNING_ID: "UPDATE planning SET id = ?, synchronized = 1,status= 1 WHERE id = ?",
  UPDATE_ACTIONMARKETING_ID: "UPDATE marketing_action SET id = ?, synchronized = 1 WHERE id = ?",
  UPDATE_OPPORTUNITYNOTE_ID: "UPDATE opportunitynote SET id = ?, synchronized = 1 WHERE id = ?",
  UPDATE_REPORT: "UPDATE  visit SET prospect_id = ? WHERE prospect_id = ?",
  SELECT_USER: "SELECT * FROM user WHERE username =? AND password = ?",
  SELECT_LOCALITY_BY_SECTOR: 'SELECT * from locality where sector_id = ? order by name',
  SELECT_ESTABLISHMENT_BY_ACTIVITY: 'SELECT * from establishment where activity = ? order by name',
  INSERT_USER: 'INSERT OR REPLACE INTO user (user_id,username, password, first_last_name, work_type, first_sync, auto_sync, sync_cycle, comments_dictionary, open_report_period, open_expense_period, working_days) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)',
  SELECT_FROM_USER_WHERE_ID_1: 'SELECT * FROM user WHERE user_id= ?',
  UPDATE_USER: 'UPDATE user SET username=?, password=?, first_last_name=?,work_type=?, auto_sync=?, sync_cycle=? , comments_dictionary=?, open_report_period=?, open_expense_period =?, working_Days=? ',
  UPDATE_USER_CONFIG: 'UPDATE user SET auto_sync=?, lock_after_sync=?, multi_wholesaler=?, sync_cycle=?, comments_dictionary=?, open_report_period=?, work_type =?, open_expense_period =?, working_days = ?',
  SELECT_SECTOR_ID: 'SELECT s.id as id from sector s',
  SELECT_LOCALITY_ID: ' SELECT id from locality',
  SELECT_PRODUCT_ID: 'SELECT id, version from product',
  SELECT_POTENTIAL_PRODUCT_ID: 'SELECT id from potential_product',
  SELECT_POTENTIAL_PRODUCT: 'SELECT id from potential_product',
  SELECT_GADGET_ID: 'SELECT id from gadget',
  SELECT_PLANNING_WAITING_VALIDATION: "SELECT id from planning_validation where status ='NEW' OR status='WAITING_FOR_VALIDATION' OR status='TO_BE_REVIEWED'",
  SELECT_EXPENSE_WAITING_VALIDATION: "SELECT id from expense where status ='NEW' OR status='WAITING_FOR_VALIDATION'",
  
  
  SELECT_SPECIALITY_ID: 'SELECT id from speciality',
  SELECT_TYPE_ID: 'SELECT id from expense_type',
  SELECT_PROSPECT_ID: "SELECT id from prospect where and status <> 'NOT_AFFECTED' ORDER BY firstname, lastname",
  SELECT_EXISTING_PROSPECTS: "SELECT id, status from prospect where synchronized =  1 and status <> 'NOT_AFFECTED'",
  SELECT_EXISTING_PLANNINGS: 'SELECT id from planning where synchronized =  1 AND planning_date >= ? ',
  UPDATE_PROSPECT_STATUS: "Update prospect set status = ? where prospect_id = ?",
  
  FIND_PROSPECT_BY_ID: "SELECT p.id, p.firstname, p.lastname,p.lat, p.lng, p.speciality_id, s.name as speciality, p.potential as potentialId, pt.id as prospectTypeId, s.action as action from prospect p inner join speciality s on p.speciality_id == s.id inner join prospect_type pt on pt.id=p.type_id WHERE p.id = ? and p.status <> 'NOT_AFFECTED'",

  FIND_PROSPECT: "SELECT p.*, s.name as speciality, p.potential as potentialId, pt.id as prospectTypeId, s.action as action from prospect p inner join speciality s on p.speciality_id == s.id inner join prospect_type pt on pt.id=p.type_id WHERE p.id = ? and p.status <> 'NOT_AFFECTED'", 


  FIND_PROSPECT_NAME: "SELECT p.firstname, p.lastname FROM prospect p  WHERE (p.id= ?) and p.status <> 'NOT_AFFECTED' ",
  SELECT_NOTEFRAIS_ID: 'SELECT id from expense',
  SELECT_REPORTS_ID: 'SELECT id  from visit',
  SELECT_PROSPECT_ID_POSITIVE: 'SELECT id from prospect where prospect_id > 0',
  SELECT_NOTEFRAIS_ID_POSITIVE: 'SELECT id from expense where id > 0',
  UPDATE_NOTEFRAIS_SYNCHRONISED: "UPDATE expense SET synchronized = 1 WHERE id = ?",
  UPDATE_VISIT_SYNCHRONISED: "UPDATE  visit SET synchronized = 1 where id = ?",
  UPDATE_PROSPECT_SYNCHRONISED: "UPDATE  prospect SET synchronized = 1 where prospect_id = ?",
  UPDATE_VISIT_PRODUCT_SYNCHRONISED: "UPDATE  visit_product SET synchronized = 1  where id = ?",
  UPDATE_PURCHASE_ORDER_SYNCHRONISED: "UPDATE purchase_order SET synchronized = 1  where id = ? ",
  UPDATE_PROSPECT_TO_MOBILE: 'UPDATE prospect SET id = ?, firstname=?,  lastname=?, activity=?, potential=?,address=?,gsm=?,phone=?,email=?,note=?, secretary=?,grade=?,  speciality_id=?, sector_id =?, locality_id=?, lat=?,lng=?,map_address=?, status=?, synchronized=?, validation=? WHERE synchronized = ?',
  DELETE_NEW_VALIDATED_PROSPECT :'DELETE from prospect where synchronized = ?',
   
  UPDATE_ACTIONMARKETING : 'UPDATE marketing_action SET  name =? ,budget = ? ,product_id = ? ,prospect_id = ?, description= ? where id = ?',
  UPDATE_ACTIONMARKETING_FULL :  "UPDATE marketing_action SET  name =? ,budget = ? ,product_id = ? ,prospect_id = ? ,description = ?, marketingAction_date = ? , synchronized=? where id = ?",
  INSERT_OPPORTUNITYNOTE: 'INSERT OR REPLACE INTO opportunitynote (id,expense_timestamp,name, budget ,product_id, prospect_id,pharmacie_id,synchronized ,status, description, attachementBase64, attachmentName ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)',
  INSERT_POSITION: 'INSERT OR REPLACE INTO geolocation (pos_timestamp, latitude, longitude ) VALUES (?,?,?)',
  INSERT_NEW_PURCHASE_ORDER: 'INSERT OR REPLACE INTO purchase_order (id, visit_id, attachmentBase64 , attachmentName , wholesaler_id, placement_method, purchase_order_template_id, generate_po, generate_do, status, synchronized ) VALUES (?,?,?,?,?,?,?,?,?,?,?)',
  INSERT_NEW_ATTACHMENT: 'INSERT OR REPLACE INTO attachment (id, attachmentBase64 , attachmentName ,status, synchronized ) VALUES (?,?,?,?,?)',
  
  
  UPDATE_OPPORTUNITYNOTE : 'UPDATE opportunitynote SET  name =? ,budget = ? ,product_id = ? ,prospect_id = ?, description= ? where id = ?',
  UPDATE_OPPORTUNITYNOTE_FULL: " UPDATE opportunitynote SET   expense_timestamp = ? , name =? ,budget = ? ,product_id = ? ,prospect_id = ?,pharmacie_id = ?,description = ?, attachementBase64 = ?, attachmentName = ?,  synchronized=?  where id = ?",
  DELETE_LOCALITY_BY_ID: 'DELETE FROM  locality WHERE id = ? ',
  DELETE_SECTOR_BY_ID: 'DELETE FROM  sector WHERE id = ? ',
  DELETE_PRODUCT_BY_ID: 'DELETE FROM  product WHERE id = ?',
  DELETE_WHOLESALER_BY_ID: 'DELETE FROM  wholesaler WHERE id = ?',
  DELETE_POTENTIAL_PRODUCT_BY_ID: 'DELETE FROM  potential_product WHERE id = ?',
  DELETE_GADGET_BY_ID: 'DELETE FROM  gadget WHERE id = ?',
  DELETE_SPECIALITY_BY_ID: 'DELETE FROM  speciality WHERE id = ?',
  DELETE_PROSPECT_BY_ID: 'DELETE FROM  prospect WHERE id = ?',
  DELETE_PROSPECT_BY_SYNCHRONIZED: 'DELETE FROM  prospect WHERE synchronized = ?',
  DELETE_TYPE_BY_ID: 'DELETE FROM  expense_type WHERE id = ?',
  DELETE_ACTIVITYS_BY_ID: 'DELETE FROM  activity WHERE id = ?',
  DELETE_PLANNING_VALIDATION_BY_ID: 'DELETE FROM  planning_validation WHERE id = ?',
  DELETE_PLANNING_BY_ID: 'DELETE FROM  planning WHERE id = ?',
  DELETE_ACTIONMARKETING_BY_ID: 'DELETE FROM  marketing_action WHERE id = ? ',
  DELETE_OPPORTUNITYNOTE_BY_ID: 'DELETE FROM  opportunitynote WHERE id = ? ',
  DELETE_LOCATION:'DELETE FROM geolocation',

  SELECT_SYNCH_NOTEFRAIS: "SELECT synchronized FROM  expense where id=?",
  DELETE_EXPENSE_REPORT_BEFORE_SYNCH : "DELETE FROM expense where id=?",

  SELECT_SYNCH_MARKETING_ACTION: "SELECT synchronized FROM  marketing_action where id=?",
  DELETE_MARKETING_ACTION_BEFORE_SYNCH : "DELETE FROM marketing_action where id=?",

  SELECT_SYNCH_ACTIVITY: "SELECT synchronized FROM  activity where id=?",
  DELETE_ACTIVITY_BEFORE_SYNCH : "DELETE FROM activity where id=?",

  SELECT_SYNCH_PLANNING_VALIDATION: "SELECT synchronized FROM  planning_validation where (planning_validation_date=?) ",
  DELETE_PLANNING_VALIDATION_BEFORE_SYNCH : "DELETE FROM planning_validation where (planning_validation_date =?) ",

  DELETE_PLANNING_BEFORE_SYNCH: "DELETE from planning WHERE (planning_date = ?) ",

  UPDATE_EXPENSE_TO_DELETE: "UPDATE expense SET status= 'DELETED' WHERE id = ?",
  UPDATE_PLANNING_TO_DELETE: "UPDATE planning SET status= 'DELETED' WHERE id = ?",
  UPDATE_PLANNING_TO_DELETE_BY_WEEK : "UPDATE planning SET status= 'DELETED' WHERE (planning_date = ?) ",
  UPDATE_PLANNING_VALIDATION_ID: "UPDATE planning_validation SET id = ? WHERE id = ?",
  UPDATE_ACTIONMARKETING_TO_DELETE: "UPDATE marketing_action SET status= 'DELETED' WHERE id = ?",
  UPDATE_ACTIVITY_TO_DELETE: "UPDATE activity SET status= 'DELETED' WHERE id = ?",
  UPDATE_OPPORTUNITYNOTE_TO_DELETE: "UPDATE opportunitynote SET status= 'DELETED' WHERE id = ?",
  UPDATE_PLANNING_VALIDATION_TO_DELETE:"Update planning_validation set status = 'DELETED'  WHERE (planning_validation_date = ?) ",
 
  DELETE_HISTORY_VISIT_ID: "update  visit_product set status = 'DELETED' WHERE id = ?",
  SELECT_MIN_DATE_FROM_REPORT: 'SELECT  visit_date from  visit ORDER BY visit_date ASC LIMIT 1',
  INSERT_VISIT: "INSERT OR REPLACE INTO  visit (id, visit_date, prospect_id, general_note,  patient_number,gadget_id,gadget_quantity, user_Id, user_Name,companion_id, status, synchronized) VALUES (?,?,?,?,?,?,?,?,?,?, 'SENT', 1)",
  INSERT_VISIT_PRODUCT: "INSERT OR REPLACE INTO  visit_product (id, visit_id, product_id, comment,  sample_quantity, order_quantity, rank, smily, sale_quantity, urgent, prescription_quantity, freeOrder , lab_gratuity, purchase_order_id, status, synchronized) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?, 'SENT', 1)",
  
  UPDATE_PLANNING_VALIDATION: 'UPDATE planning_validation SET notes=?, status=?,planning_validation_date = ? where id = ?',

  SELECT_WORK_TYPE_FROM_USER: 'SELECT work_type FROM user',
  SELECT_IDS_FROM_SPECIALITY: 'SELECT id from speciality',
  SELECT_IDS_FROM_TYPE: 'SELECT id from expense_type',
  UPDATE_USER_WORK_TYPE: 'UPDATE user SET work_type = ?',
  SELECT_LAST_SYNCHRONISATION: 'SELECT last_synchronisation,time from user',
  SELECT_MULTI_WHOLESALER : 'SELECT multi_wholesaler from user',
  SELECT_NOTIFICATIONS_NUMBER: 'SELECT count (id) as count from notifications where status = 0',
  UPDATE_LAST_SYNCHRONISATION: 'UPDATE user SET last_synchronisation=?,time=?',
  UPDATE_LAST_RECEIVE:'UPDATE user set last_receive_date = ?',
  SELECT_LAST_RECEIVE: 'SELECT last_receive_date from user',
  INSERT_GOAL_ITEM:'INSERT OR REPLACE INTO goal_item (activity, potential_id, product_id, sector_id, speciality_id, prospect_type_id,  value, goal_id ) VALUES (?,?,?,?,?,?,?,?)',
  INSERT_GOAL:'INSERT OR REPLACE INTO goal (id, name ,item_order, first_date , last_date , type  ) VALUES (?,?,?,?,?,?)',
  DELETE_ALL_GOAL_ITEM:'DELETE FROM goal_item ',
  DELETE_ALL_GOAL:'DELETE FROM goal ',

  DELETE_ALL_POT_ITEM:'DELETE FROM purchase_order_template_item',
  DELETE_ALL_POT:'DELETE FROM purchase_order_template',
  INSERT_POT_ITEM:'INSERT OR REPLACE INTO purchase_order_template_item (id, product_id, quantity, freeOrder, labGratuity, purchase_order_template_id ) VALUES (?,?,?,?,?,?)',
  INSERT_POT:'INSERT OR REPLACE INTO purchase_order_template (id, name, first_date , last_date, gifts_id  ) VALUES (?,?,?,?,?)',
  
  DELETE_ALL_FQR_ITEM:'DELETE FROM free_quantity_rule_item',
  INSERT_FQR_ITEM:'INSERT OR REPLACE INTO free_quantity_rule_item (product_id, potential_id, prospect_type_id, orderQuantity, freeQuantity, labGratuity) VALUES (?,?,?,?,?,?)',
  
  INSERT_EXPENSE_TYPE: 'INSERT OR REPLACE INTO expense_type (id, name, amount, required_attachment) VALUES (?,?,?,?)',
  DELETE_ALL_EXPENSE_REPORT:'DELETE FROM expense_type ',
  DELETE_ALL_CHARGE_PLAN:'DELETE FROM charge_plan ',
  INSERT_CHARGE_PLAN:'INSERT INTO charge_plan  (speciality_id, product_id, rank) VALUES (?,?,?)',
  SELECT_LAST_NOTIFICATION:'SELECT max(id) as last_id from notifications',
  SELECT_ALL_FROM_RANGES:'SELECT * from range order by name',
  SELECT_ALL_FROM_NEXT_ACTION_RULE: 'SELECT * FROM next_action_rule',
INSERT_NEXT_ACTION_RULE: 'INSERT OR REPLACE INTO next_action_rule (totalRevenue, period, action) VALUES (?, ?, ?)',
DELETE_ALL_NEXT_ACTION_RULES: 'DELETE FROM next_action_rule',
INSERT_USER_PERMISSION: 'INSERT OR REPLACE INTO user_permissions (permission_name, user_id) VALUES (?, ?)',
DELETE_ALL_USER_PERMISSIONS: 'DELETE FROM user_permissions',
SELECT_USER_PERMISSIONS: 'SELECT * FROM user_permissions WHERE user_id = ?',
CHECK_USER_PERMISSION: 'SELECT COUNT(*) as count FROM user_permissions WHERE user_id = ? AND permission_name = ?',
INSERT_BUDGET_ALLOCATION: 'INSERT OR REPLACE INTO budget_allocation (id, year, monthlyBudget, type) VALUES (?,?,?,?)',
DELETE_ALL_BUDGET_ALLOCATIONS: 'DELETE FROM budget_allocation',
SELECT_BUDGET_ALLOCATIONS: 'SELECT * FROM budget_allocation ORDER BY year DESC',
SELECT_BUDGET_ALLOCATION_BY_YEAR: 'SELECT * FROM budget_allocation WHERE year = ?',
// Contact Types queries
SELECT_CONTACT_TYPES: 'SELECT * FROM contact_type ORDER BY name',
SELECT_CONTACT_TYPE_BY_ID: 'SELECT * FROM contact_type WHERE id = ?',
SELECT_CONTACT_TYPES_BY_ACTION: 'SELECT * FROM contact_type WHERE action = ? ORDER BY name',
INSERT_CONTACT_TYPE: 'INSERT OR REPLACE INTO contact_type (id, name, action, icon) VALUES (?, ?, ?, ?)',
DELETE_ALL_CONTACT_TYPES: 'DELETE FROM contact_type',

// Preferences queries
SELECT_PREFERENCES: 'SELECT * FROM preference ORDER BY name',
SELECT_PREFERENCE_BY_ID: 'SELECT * FROM preference WHERE id = ?',
INSERT_PREFERENCE: 'INSERT OR REPLACE INTO preference (id, name) VALUES (?, ?)',
DELETE_ALL_PREFERENCES: 'DELETE FROM preference',

// Interests queries
SELECT_INTERESTS: 'SELECT * FROM interest ORDER BY name',
SELECT_INTEREST_BY_ID: 'SELECT * FROM interest WHERE id = ?',
INSERT_INTEREST: 'INSERT OR REPLACE INTO interest (id, name) VALUES (?, ?)',
DELETE_ALL_INTERESTS: 'DELETE FROM interest',

  }),
});