<ion-header class="fullscreen-header">
  <ion-toolbar color="primary">
    <ion-title>Product Details</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="close()" class="close-button">
        <ion-icon name="close" size="large"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="fullscreen-content" [scrollY]="true">
  <!-- Informations du produit -->
  <div *ngIf="product" class="product-info">
    <h2>{{ product.name }}</h2>
  </div>

  <!-- Affichage des documents -->
  <div *ngIf="hasDocuments" class="documents-container">
    
    <!-- Navigation buttons (seulement si plusieurs documents) -->
    <div *ngIf="hasMultipleDocuments" class="carousel-navigation">
      <ion-button 
        fill="solid" 
        color="primary"
        (click)="prevSlide()" 
        [disabled]="currentSlide === 0"
        class="nav-button prev-button">
        <ion-icon name="chevron-back" size="large"></ion-icon>
      </ion-button>
      
      <ion-button 
        fill="solid" 
        color="primary"
        (click)="nextSlide()" 
        [disabled]="currentSlide === documents.length - 1"
        class="nav-button next-button">
        <ion-icon name="chevron-forward" size="large"></ion-icon>
      </ion-button>
    </div>

    <!-- Document actuel -->
    <div class="slide-container">
      <div class="image-wrapper">
        <ion-img
          [src]="currentDocument.document_base64"
          [alt]="currentDocument.name || 'Document'"
          class="document-image">
        </ion-img>
      </div>
      <p class="document-name">{{ currentDocument.name || 'Document sans nom' }}</p>
    </div>

    <!-- Indicateurs de pagination (seulement si plusieurs documents) -->
    <div *ngIf="hasMultipleDocuments" class="pagination-dots">
      <span 
        *ngFor="let doc of documents; let i = index"
        class="dot"
        [class.active]="i === currentSlide"
        (click)="goToSlide(i)">
      </span>
    </div>

    <!-- Compteur (seulement si plusieurs documents) -->
    <div *ngIf="hasMultipleDocuments" class="slide-counter">
      {{ currentSlide + 1 }} / {{ documents.length }}
    </div>
  </div>

  <!-- Message si pas de documents -->
  <div *ngIf="!hasDocuments" class="no-documents">
    <ion-icon name="document-outline" size="large"></ion-icon>
    <p>Aucun document disponible</p>
  </div>
</ion-content>

<ion-footer class="fullscreen-footer">
  <ion-toolbar>
    <ion-button expand="block" color="success" (click)="saveTracking()" class="save-button">
      <ion-icon name="save" slot="start"></ion-icon>
      SAVE
    </ion-button>
  </ion-toolbar>
</ion-footer>
