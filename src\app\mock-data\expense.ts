
import { ExpenseType } from '../models/expense';
import { Expense } from '../models/expense';

export const MOCK_EXPENSE_TYPE: ExpenseType[] = [
  {
    id: 1,
    name: 'Travel Expense',
    amount: 500,
    requiredAttachment: false,
  },

  {
    id: 2,
    name: 'Office Supplies',
    amount: 200,
    requiredAttachment: false,
  },

  {
    id: 3,
    name: 'Entertainment',
    amount: 1000,
    requiredAttachment: false,
  }];
export const MOCK_EXPENSE: Expense[] = [
  {
    id: 1,
    activityId: 1,
    expenseDate: new Date().getTime(),
    description: 'Travel expenses for business meeting',
    montant: 500,
    expenseTypeId: 1,
    attachementBase64: 'base64stringofanattachment',
    attachmentName: 'receipt.jpg',
    status: '',
    synchronized: 1,
  },

  {
    id: 2,
    activityId: 5,
    expenseDate: new Date(2024, 7, 15).getTime(),
    description: 'Office supplies purchase',
    montant: 200,
    expenseTypeId: 2,
    attachementBase64: 'anotherbase64string',
    attachmentName: 'invoice.pdf',
    status: 'Nouveau',
    synchronized: 1,
  },

  {
    id: 3,
    activityId: 4,
    expenseDate: new Date(2024, 7, 20).getTime(),
    description: 'Entertainment expenses',
    montant: 1000,
    expenseTypeId: 3,
    attachementBase64: 'thirdbase64string',
    attachmentName: 'ticket.jpg',
    status: 'Nouveau',
    synchronized: 1,
  },
  {
    id: 4,
    activityId: 4,
    expenseDate: new Date(2024, 7, 15).getTime(),
    description: 'Entertainment expenses',
    montant: 1000,
    expenseTypeId: 3,
    attachementBase64: 'thirdbase64string',
    attachmentName: 'ticket.jpg',
    status: 'Nouveau',
    synchronized: 1,
  }]