import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { LoggerService } from './logger.service';
import { Platform, AlertController, ModalController } from '@ionic/angular';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { environment } from 'src/environments/environment';
import { DbnameVersionService } from './dbname-version.service';
import { Observable, from, Observer } from 'rxjs';
import { TablesUpgrades } from '../upgrades/tables/tables';
import { Prospect } from '../models/planning';
import { ProspectDetailComponent } from '../components/prospect-detail/prospect-detail.component';
import { ProspectPopupComponent } from '../components/prospect-popup/prospect-popup.component';


@Injectable()
export class ProspectService {
  public mDb!: SQLiteDBConnection;
  public databaseName: string;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;
  private versionUpgrades =TablesUpgrades;
 

  constructor(
    private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
    private logger: LoggerService,
    private platform: Platform,
    private modalController: ModalController
  ) {
    this.databaseName = environment.databaseNames.find(x => x.name.includes('tables'))?.name || 'default';
  }

  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    await this.createInitialData();
}

  private async createInitialData(): Promise<void> {
    // Initialiser les données de la base de données ici si nécessaire
  }

  getProspectNumber(): Observable<number> {
    const selectQuery = 'SELECT count(id) as count FROM prospect WHERE status <> "DELETED" AND status <> "NOT_AFFECTED"';
    return new Observable((observer: Observer<number>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const count = result.values[0].count as number;
          observer.next(count);
          observer.complete();
        },
        (error: any) => {
          this.logger.info('Error executing SQL: ' + JSON.stringify(error));
          observer.error(error);
        }
      );
    });
  }

  public async openProspectDetails(prospectId: number): Promise<any> {
    const selectQuery = `
      SELECT p.*,
             s.name   AS sectorName,
             l.name   AS localityName,
             sp.name  AS specialityName,
             pt.name  AS potentialName,
             es.name  AS establishmentName
        FROM prospect p
        INNER JOIN sector     s  ON p.sector_id      = s.id
        INNER JOIN locality   l  ON p.locality_id    = l.id
        INNER JOIN potential  pt ON p.potential      = pt.id
        INNER JOIN speciality sp ON p.speciality_id  = sp.id
        LEFT JOIN establishment es ON es.id         = p.establishment_id
       WHERE p.id = ? AND p.status <> 'NOT_AFFECTED'
    `;
    // 1) fetch the record
    const result: any = await this.mDb.query(selectQuery, [prospectId]);
    if (!result.values?.length) {
      throw new Error('No prospect found for ID ' + prospectId);
    }
    const selectedProspect = result.values[0] as Prospect;

    // 2) immediately pop up the modal
    const modal = await this.modalController.create({
      component: ProspectPopupComponent,
      componentProps: { prospect: selectedProspect }
    });
    await modal.present();

    // 3) return it in case some caller still wants the raw data
    return selectedProspect;
  }
  

  // Adjust the showProspectPopup method in the ProspectService to accept both prospectId and relatedData
async showProspectPopup( prospect: Prospect) {
  const modal = await this.modalController.create({
    component: ProspectPopupComponent,
    componentProps: { prospect: prospect }
  });
  await modal.present();
}

async saveProspect(prospect: any, latLng: any, mapAddress: string, isEditMode: boolean): Promise<void> {
  console.log(prospect);

  if (!prospect.id) {
    prospect.id = new Date().getTime();
  }

  try {
    // Check if latLng is defined
    if (!latLng) {
      throw new Error("Latitude and longitude are not defined");
    }

    prospect.lat = latLng.lat;
    prospect.lng = latLng.lng;
    prospect.mapAddress = mapAddress;

 

    let query: string;
    let params: any[];

    if (isEditMode) {
      query = `UPDATE prospect SET 
        firstname = ?, lastname = ?, activity = ?, potential = ?, address = ?, 
        gsm = ?, phone = ?, email = ?, note = ?, secretary = ?, grade = ?, 
        speciality_id = ?, sector_id = ?, locality_id = ?, lat = ?, 
        lng = ?, map_address = ?, status = ?, synchronized = ?, 
        validation = ?, type_id = ?, establishment_id = ? ,fiscal_number = ? 
        WHERE id = ?`;
      params = [
        prospect.firstname, prospect.lastname, prospect.activity, prospect.potentialId,
        prospect.address, prospect.gsm, prospect.phone, prospect.email, prospect.note,
        prospect.secretary, prospect.grade, prospect.specialityId, prospect.sectorId,
        prospect.localityId, latLng.lat, latLng.lng, mapAddress, "UPDATE", 0, 0,
        prospect.typeId, prospect.establishmentId, prospect.fiscalNumber ,prospect.id
      ];
    } else {
      query = `INSERT INTO prospect (id, firstname, lastname, activity, potential, address, 
        gsm, phone, email, note, secretary, grade, speciality_id, sector_id, locality_id, 
        lat, lng, map_address, status, synchronized, validation, type_id, 
        establishment_id, fiscal_number) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
      params = [
        prospect.id, prospect.firstname, prospect.lastname, prospect.activity, prospect.potentialId,
        prospect.address, prospect.gsm, prospect.phone, prospect.email, prospect.note,
        prospect.secretary, prospect.grade, prospect.specialityId, prospect.sectorId,
        prospect.localityId, latLng.lat, latLng.lng, mapAddress, "NEW", 0, 0,
        prospect.typeId, prospect.establishmentId, prospect.fiscalNumber
      ];
    }

    console.log(query);
    await this.mDb.run(query, params);
   // Sauvegarder les relations many-to-many
   await this.saveProspectRelations(prospect);
    // Save to store for web platform
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  } catch (error) {
    this.logger.info('Error saving prospect: ' + JSON.stringify(error));
    throw error;
  }
}

async saveProspectRelations(prospect: any): Promise<void> {
  const prospectId = prospect.id;
  console.log("Saving relations for prospect ID:", prospectId);
  console.log("Selected interests:", prospect.selectedInterests);
  console.log("Selected contact types:", prospect.selectedContactTypes);
  console.log("Selected preferences:", prospect.selectedPreferences);

  try {
    // Supprimer les anciennes relations
    await this.mDb.run("DELETE FROM prospect_interest WHERE prospect_id = ?", [prospectId]);
    await this.mDb.run("DELETE FROM prospect_contact_type WHERE prospect_id = ?", [prospectId]);
    await this.mDb.run("DELETE FROM prospect_preference WHERE prospect_id = ?", [prospectId]);

    // Insérer les nouvelles relations - Interests
    if (prospect.selectedInterests?.length) {
      for (const interestId of prospect.selectedInterests) {
        await this.mDb.run("INSERT INTO prospect_interest (prospect_id, interest_id) VALUES (?, ?)", [
          prospectId, interestId
        ]);
      }
    }

    // Insérer les nouvelles relations - Contact Types
    if (prospect.selectedContactTypes?.length) {
      for (const contactTypeId of prospect.selectedContactTypes) {
        await this.mDb.run("INSERT INTO prospect_contact_type (prospect_id, contact_type_id) VALUES (?, ?)", [
          prospectId, contactTypeId
        ]);
      }
    }

    // Insérer les nouvelles relations - Preferences
    if (prospect.selectedPreferences?.length) {
      for (const preferenceId of prospect.selectedPreferences) {
        await this.mDb.run("INSERT INTO prospect_preference (prospect_id, preference_id) VALUES (?, ?)", [
          prospectId, preferenceId
        ]);
      }
    }
    
    console.log("Relations saved successfully");
  } catch (error) {
    this.logger.info('Error saving prospect relations: ' + JSON.stringify(error));
    throw error;
  }
}

}  