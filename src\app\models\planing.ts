export class Planning {
    
        id!:number ;
        planningDate !:number ;
        prospectId!:number ; 
        synchronized !:number ;
        status!:string ;
}
export class Prospect {
        id!:any ;
        firstname !:string ;
        lastname !:string ;
        activity !:string ;
        potential !:number ; 
        address !:string ;
        gsm !:string ;
        phone !:string ;
        email !:string ;
        note !:string ;
        secretary !:string ;
        grade !:string ;
        specialityId !:number ; 
        speciality ?: string;
        sectorId !:number ; 
        localityId !:number ; 
        lat !:number ; 
        lng !:number ; 
        mapAddress !:string ;
        status !:string ;
        synchronized!:boolean;
        validation !:number ; 
        typeId !:number ; 
        potential_id?: number;
        prospect_type_id?: number;
        establishmentId !:number ;
        backgroundColor ?: string;
        sectorName?         : string;
        localityName?       : string;
        specialityName?     : string;
        potentialName?      : string;
        establishmentName?  : string;
        potentialId?: number;
        fiscalNumber?: string
        selectedInterests?: number[]
        selectedContactTypes?: number[]
        selectedPreferences?: number[]
}