<ion-header>
  <ion-toolbar color="primary">
    <ion-title>{{ LABELS.PROSPECT }}</ion-title>
    <ion-buttons slot="end">
      <!-- EDIT button -->
      <ion-button color="warning" (click)="onEditProspect(prospect.id)">
        {{ LABELS.EDIT }}
      </ion-button>
      <!-- RETURN button -->
      <ion-button (click)="dismiss()">
        {{ LABELS.RETURN }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content class="ion-padding popup-body">
  <ion-card>
    <ion-card-header>
      <ion-card-title class="name">
        {{ prospect.firstname }} {{ prospect.lastname }}
      </ion-card-title>
    </ion-card-header>

    <ion-card-content class="details">
      <p><strong>{{ LABELS.SECTOR }}:</strong> {{ prospect.sectorName }}</p>
      <p><strong>{{ LABELS.LOCALITY }}:</strong> {{ prospect.localityName }}</p>
      <p *ngIf="prospect.establishmentId != null">
        <strong>{{ LABELS.ESTABLISHMENT }}:</strong> {{ prospect.establishmentName }}
      </p>
      <p *ngIf="prospect.speciality">
        <strong>{{ LABELS.SPECIALITY }}:</strong> {{ prospect.specialityName }}
      </p>
      <p *ngIf="prospect.potential != null">
        <strong>{{ LABELS.POTENTIAL }}:</strong> {{ prospect.potentialName }}
      </p>
      <p *ngIf="prospect.phone || prospect.gsm">
        <strong>{{ LABELS.PHONE }}:</strong> {{ prospect.phone || '—' }}
        &nbsp;—&nbsp;
        <strong>{{ LABELS.GSM }}:</strong> {{ prospect.gsm || '—' }}
      </p>
      <p *ngIf="prospect.email">
        <strong>{{ LABELS.EMAIL }}:</strong> {{ prospect.email }}
      </p>
      <p *ngIf="prospect.secretary">
        <strong>{{ LABELS.SECRETARY }}:</strong> {{ prospect.secretary }}
      </p>
      <p *ngIf="prospect.note">
        <strong>{{ LABELS.REMARK }}:</strong> {{ prospect.note }}
      </p>
    </ion-card-content>
  </ion-card>

  <div
    #mapContainer
    *ngIf="prospect.lat != null && prospect.lng != null"
    class="map-wrapper"
  ></div>
</ion-content>
