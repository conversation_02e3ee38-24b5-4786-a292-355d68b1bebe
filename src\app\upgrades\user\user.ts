export const UserVersionUpgrades = [
    {
        toVersion: 1,
        statements: [
            `CREATE TABLE IF NOT EXISTS user (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            password TEXT,
            first_last_name TEXT,
            work_type TEXT,
            working_days INTEGER,
            last_synchronisation TEXT,
            last_receive_date INTEGER,
            first_sync BOOLEAN,
            time TEXT,
            auto_sync BOOLEAN,
            lock_after_sync BOOLEAN,
            multi_wholesaler BOOLEAN,
            sync_cycle NUMBER,
            comments_dictionary TEXT,
            open_report_period NUMBER,
            open_expense_period NUMBER);`
        ]
    }
];

export const UsersVersionUpgrades = [
    {
        toVersion: 1,
        statements: [
            `CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            name TEXT,
            delegate_id INTEGER);`
        ]
    }
];
