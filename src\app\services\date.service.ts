
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DateService {

 

  getDayFromDate(date: Date): number {
    return date.getDate();
  }

  getMonthFromDate(date: Date): number {
    return date.getMonth() + 1; 
  }

  getYearFromDate(date: Date): number {
    return date.getFullYear();
  }

  getSundayDate(mondayDate: Date): Date {
    const sundayDate = new Date(mondayDate);
    sundayDate.setDate(sundayDate.getDate() + 6);
    sundayDate.setHours(23, 59, 59, 0);
    return sundayDate;
  }

  getDateOfDay(currentTab: number, dateSelected: Date): Date {
    const date = new Date(dateSelected);
    switch (currentTab) {
      case 0: return date;
      case 1: date.setDate(date.getDate() + 1); return date;
      case 2: date.setDate(date.getDate() + 2); return date;
      case 3: date.setDate(date.getDate() + 3); return date;
      case 4: date.setDate(date.getDate() + 4); return date;
      case 5: date.setDate(date.getDate() + 5); return date;
      default: return date;
    }
  }

  getAllDaysOfWeek(dateSelected: Date): number[] {
    const days = [];
    const date = new Date(dateSelected);

    for (let i = 0; i < 6; i++) {
      const tempDate = new Date(date);
      tempDate.setDate(date.getDate() + i);
      days.push(this.getDayFromDate(tempDate));
    }

    return days;
  }

  getAllMonths(dateSelected: Date): number[] {
    const months = [];
    const date = new Date(dateSelected);

    for (let i = 0; i < 6; i++) {
      const tempDate = new Date(date);
      tempDate.setDate(date.getDate() + i);
      months.push(this.getMonthFromDate(tempDate));
    }

    return months;
  }

  getAllYears(dateSelected: Date): number[] {
    const years = [];
    const date = new Date(dateSelected);

    for (let i = 0; i < 6; i++) {
      const tempDate = new Date(date);
      tempDate.setDate(date.getDate() + i);
      years.push(this.getYearFromDate(tempDate));
    }

    return years;
  }

  getInitialDate(): Date {
    const currentDate = new Date();
    currentDate.setDate(1);
    return currentDate;
  }

  getMondayOfWeek(): Date {
    const currentDate = new Date();
    const dayOfWeek = currentDate.getDay();
    const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    
    const mondayDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() + daysToMonday);
    mondayDate.setHours(0, 0, 0, 0);
    return mondayDate;
  }

  getMondayOfWeekSelected(date: Date): Date {
    const monday = date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1);
    return new Date(date.setDate(monday));
  }

  numberOfDaysBetween2Dates(date1: Date, date2: Date): number {
    const timeDiff = Math.abs(date1.getTime() - date2.getTime());
    return Math.round(timeDiff / (1000 * 3600 * 24));
  }

  daysBetween2Dates(date1: Date, date2: Date): number {
    const timeDiff = date1.getTime() - date2.getTime();
    return timeDiff / (1000 * 3600 * 24);
  }

  getCurrentDate(): number {
    return Date.now();
  }
  addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  } 
   formatDate(timestamp: number): string {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Les mois sont de 0 à 11
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
}
}
