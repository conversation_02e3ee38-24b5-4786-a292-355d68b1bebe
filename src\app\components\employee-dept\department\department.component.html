<ion-list lines="full">
  <ion-list-header id="department-cmp-list-header">
    Department
  </ion-list-header>

  <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()">
    <ion-item>
      <ion-input id="department-cmp-name" type="text" label="name" formControlName="name" required></ion-input>
    </ion-item>
    <ion-item>
      <ion-input id="department-cmp-location" type="text" label="location" formControlName="location"></ion-input>
    </ion-item>
    <ion-row>
      <ion-col>
        <ion-button type="submit" color="primary" shape="full" expand="block" [disabled]="!departmentForm.valid">
          Submit
        </ion-button>
      </ion-col>
    </ion-row>
  </form>
</ion-list>
