import { TroubleShootingService } from '../../services/trouble-shooting.service';
import { Component, OnInit } from '@angular/core';
import { AlertController, IonicModule, ModalController } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ConsoleService } from 'src/app/services/console-service.service';


@Component({
  selector: 'app-trouble-shooting',
  templateUrl: './trouble-shooting.component.html',
  styleUrls: ['./trouble-shooting.component.scss'],
  standalone: true,
  imports: [IonicModule, FormsModule, CommonModule,FormsModule,ReactiveFormsModule]

})
export class TroubleShootingComponent  implements OnInit{
  pageTitle: string = 'Trouble Shooting';
  request: string = '';
  result: any;
  showConsole: boolean = false;

  constructor(private troubleShootingService: TroubleShootingService,
    private Router: Router,
  
    private consoleService: ConsoleService,
  ) {}
  ngOnInit() {
    // Charger l'état initial de la console
    this.showConsole = this.consoleService.getConsoleVisibility();
    const footer = document.querySelector(".global-footer") as HTMLElement
    if (footer) {
      footer.style.display = "none"
    }
  }
  ngOnDestroy() {
   
    const footer = document.querySelector(".global-footer") as HTMLElement
    if (footer) {
      footer.style.display = "block"
    }
  }

  async execute() {
    try {
      this.result = await this.troubleShootingService.executeRequest(this.request);
    } catch (error) {
      console.error('Error executing request', error);
    }
  }

  async export() {
    try {
      await this.troubleShootingService.exportDatabase();
    } catch (error) {
      console.error('Error exporting database', error);
    }
  }
  goBack() {
    this.Router.navigate(['/login']); 
  }
  toggleConsole() {
    this.consoleService.toggleConsole();
    this.showConsole = this.consoleService.getConsoleVisibility();
  }
} 