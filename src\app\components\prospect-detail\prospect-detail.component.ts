import { Component, OnInit, Inject } from '@angular/core';
import { ActivatedRoute, Router }       from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Plugins } from '@capacitor/core';
import { LoggerService } from 'src/app/services/logger.service'; 
import { PopupService } from 'src/app/services/popup-service.service'; 
import { MAP } from 'src/app/constants'; 
import { environment } from 'src/environments/environment'; 
import { NAME_FILE } from 'src/app/constants';
import { SQLiteService } from 'src/app/services/sqlite.service';
import { CapacitorSQLite, SQLiteDBConnection } from '@capacitor-community/sqlite';
import { IonicModule } from '@ionic/angular';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators, NgForm } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ProspectService } from 'src/app/services/prospect-service.service'; 
import { ChangeDetectorRef, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { AlertController } from '@ionic/angular';
import { QUERIES } from 'src/app/constants'; 
import { Prospect } from 'src/app/models/planing';
import { CommonService } from 'src/app/services/common-service.service';
import { FilterService } from 'src/app/services/filterService.service';
import { Geolocation } from '@capacitor/geolocation';

import { NavController, LoadingController } from '@ionic/angular';
import { Interest } from 'src/app/models/interest';
import { ContactType } from 'src/app/models/contact-type';
import { Preference } from 'src/app/models/preference';







@Component({
  selector: 'app-prospect-detail',
  templateUrl: './prospect-detail.component.html',
  styleUrls: ['./prospect-detail.component.scss'],
  standalone: true,
  imports: [IonicModule, FormsModule, CommonModule, FormsModule, ReactiveFormsModule]
  
})
export class ProspectDetailComponent implements OnInit, AfterViewInit {
  public pageTitle!: string
  public zoom: number | undefined
  public isEditMode = false;
  public googleMapLink: string | undefined
  public positionTime: string | undefined

  // Labels for form fields
  public LABELS = {
    ADD_PROSPECT: "Add Prospect",
    ASSISTANT: "Assistant",
    ASSOCIATE_PROFESSOR: "Professeur agrégé",
    ENTER_FIRST_NAME: "Enter First Name",
    FIRST_NAME_REQUIRED: "First Name is required",
    ENTER_NAME: "Enter Last Name",
    LAST_NAME_REQUIRED: "Last Name is required",
    CHIEF_SERVICE: "Chef service",
    CHOOSE_SPECIALITY: "Choose Speciality",
    SPECIALITY_REQUIRED: "Speciality is required",
    CHOOSE_POTENTIAL: "Choose Potential",
    POTENTIAL_REQUIRED: "Potential is required",
    CHOOSE_TYPE: "Choose Type",
    TEACHER: "Professeur",
    TYPE_REQUIRED: "Type is required",
    CHOOSE_ACTIVITY: "Choose Activity",
    ACTIVITY_REQUIRED: "Activity is required",
    CHOOSE_SECTOR: "Choose Sector",
    SECTOR_REQUIRED: "Sector is required",
    CHOOSE_LOCALITY: "Choose Locality",
    LOCALITY_REQUIRED: "Locality is required",
    CHOOSE_ESTABLISHMENT: "Choose Establishment",
    CHOOSE_GRADE: "Choose Grade",
    DOCTOR: "Docteur",
    GRADE: "",
    GSM: "GSM",
    PHONE: "Phone",
    PHONE_REQUIRED: "Phone is required",
    EMAIL: "Email",
    EMAIL_IS_NOT_VALID: "Email is not valid",
    ADRESS: "Address",
    ADRESS_REQUIRED: "Address is required",
    REMARK: "Remark",
    RESIDENT: "Résident",
    SECRETARY: "Secretary",
    TYPE_AN_ADDRESS: "Type an address",
    MY_POSITION: "My Position",
    UPDATE_MY_POSITION: "Update my location",
    SAVE: "Save",
    RETURN: "Return",
    INTERNAL: "Interne",
    PARAMEDICAL: "Paramédical",
    PHARMACIST: "Pharmacien",
    // Nouveaux labels
    FISCAL_NUMBER: "Matricule Fiscal",
    CHOOSE_INTERESTS: "Centres d'intérêt",
    CHOOSE_CONTACT_TYPES: "Types de contact",
    CHOOSE_PREFERENCES: "Préférences",
  }

  public activities: any[] = []
  public selectedRow: any = null
  public types: any[] = []
  public establishments: any[] = []
  public potentials: any[] = []
  public localities: any[] = []
  public formLocalities: any[] = []
  public sectors: any[] = []
  public allLocalities: any[] = []
  public prospectToEdit: any = null
  public submitted = false
  public latLng: { lat: number; lng: number } | undefined
  public latLngCenter: { lat: number; lng: number } | undefined

  // Nouvelles propriétés
  public interests: Interest[] = []
  public contactTypes: ContactType[] = []
  public preferences: Preference[] = []

  @ViewChild("mapContainer", { static: false }) mapContainer!: ElementRef<HTMLDivElement>
  private map!: google.maps.Map
  private marker!: google.maps.Marker
  public mapOptions = {
    types: ["(places)"],
    componentRestrictions: { country: "tn" },
  }
  public autocomplete = { object: "" }
  public mapAddress = ""
  public location: any = {}

  // Reactive Form
  public myForm!: FormGroup
  public inputs = {
    firstname: "",
    lastname: "",
    speciality: "",
    potentiel: "",
    locality: "",
    activity: "",
    sector: "",
    gsm: "",
    tel: "",
    email: "",
    adresse: "",
    note: "",
    secretary: "",
    grade: "",
    type: "",
    establishment: "",
  }

  messages: any
  options: { timeout: number; enableHighAccuracy: boolean; maximumAge: number } | undefined
  SQLiteService: any
  private db!: SQLiteDBConnection
  specialities: any
  public currentProspect: Prospect = new Prospect()
  prospect: Prospect | undefined;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private prospectService: ProspectService,
    private cdr: ChangeDetectorRef,
    private alertCtrl: AlertController,
    private fb: FormBuilder,
    private navCtrl: NavController,
    private logger: LoggerService,
    private http: HttpClient,
    private loadingController: LoadingController,
    private sqliteService: SQLiteService,
    private popupService: PopupService,
    private commonService: CommonService,
    private filterService: FilterService,
    @Inject(MAP) private mapConstants: any,
    @Inject(QUERIES) private QUERIES: any,
    @Inject(NAME_FILE) private nameFileConstants: { FILE_LOG: string }
  ) {
    this.latLng = {
      lat: this.mapConstants?.LAT_DEFAULT_VALUE ?? 0,
      lng: this.mapConstants?.LNG_DEFAULT_VALUE ?? 0
    };
  }

  ngAfterViewInit() {
    if (window.google?.maps) {
      this.renderMap()
    }
  }

  async ngOnInit() {
    // 1) grab your DB reference
    this.db = this.prospectService.mDb

    // 2) build the reactive form
    this.myForm = this.fb.group({
      firstname: ["", Validators.required],
      lastname: ["", Validators.required],
      speciality: ["", Validators.required],
      potentiel: ["", Validators.required],
      locality: ["", Validators.required],
      activity: ["", Validators.required],
      sector: ["", Validators.required],
      gsm: [""],
      tel: ["", Validators.required],
      email: ["", [Validators.email]],
      adresse: ["", Validators.required],
      note: [""],
      secretary: [""],
      grade: [""],
      type: ["", Validators.required],
      establishment: [""],
    })

    // 5) initialize all lookups
    this.initSpecialities(0)
    this.initSectors(0)
    this.initPotential(0)
    this.initTypes(0)
     this.initActivities()
     this.initLocalities()
    this.initEstablishments(0)
    this.initGpsOptions()

    // 6) Initialiser les nouvelles données
     this.initInterests()
     this.initContactTypes()
     this.initPreferences() 
    // 3) decide Edit vs Add based on URL
    const idParam = this.route.snapshot.paramMap.get("id")
    if (idParam) {
      // → EDIT
      this.pageTitle = "Edit Prospect"
      this.isEditMode = true;
      await this.loadProspectDetails(Number(idParam))
    } else {
      this.isEditMode = false;
      // → ADD NEW
      this.pageTitle = "Add Prospect"
      this.currentProspect = new Prospect()
    }

    // 4) rest of your existing setup
    this.logger.setStorageFilename(this.nameFileConstants.FILE_LOG)
    this.zoom = this.mapConstants.ZOOM_6
    this.googleMapLink = "YOUR_GOOGLE_MAP_LINK"
    this.positionTime = ""
    
    this.selectedRow = null
    this.prospectToEdit = null
    this.latLng = {
      lat: this.mapConstants.LAT_DEFAULT_VALUE ?? 0,
      lng: this.mapConstants.LNG_DEFAULT_VALUE ?? 0,
    }
    this.latLngCenter = {
      lat: this.mapConstants.LAT_CENTER_VALUE ?? 0,
      lng: this.mapConstants.LNG_CENTER_VALUE ?? 0,
    }
    this.mapOptions = {
      types: ["(places)"],
      componentRestrictions: { country: "tn" },
    }
    this.autocomplete = { object: "" }
    this.mapAddress = ""
    this.location = {}

    this.logger.info("function : initAddProspect / initEditProspect")
    this.latLng.lat = this.mapConstants.LAT_DEFAULT_VALUE
    this.latLng.lng = this.mapConstants.LNG_DEFAULT_VALUE
    this.zoom = this.mapConstants.ZOOM_2
    this.mapAddress = ""

  }

  // Nouvelles méthodes d'initialisation
  async initInterests() {
    try {
      const result = await this.db.query("SELECT * FROM interest ORDER BY name")
      if (result?.values) {
        this.interests = result.values.map((row: any) => ({
          id: row.id,
          name: row.name,
          selected: false,
        }))
      }
    } catch (error) {
      this.logger.error("Error fetching interests: " + (error instanceof Error ? error.message : String(error)))
    }
  }

  async initContactTypes() {
    try {
      const result = await this.db.query("SELECT * FROM contact_type ORDER BY name")
      if (result?.values) {
        this.contactTypes = result.values.map((row: any) => ({
          id: row.id,
          name: row.name,
          action: row.action,
          icon: row.icon,
          selected: false,
        }))
      }
    } catch (error) {
      this.logger.error("Error fetching contact types: " + (error instanceof Error ? error.message : String(error)))
    }
  }

  async initPreferences() {
    try {
      const result = await this.db.query("SELECT * FROM preference ORDER BY name")
      if (result?.values) {
        this.preferences = result.values.map((row: any) => ({
          id: row.id,
          name: row.name,
          selected: false,
        }))
      }
    } catch (error) {
      this.logger.error("Error fetching preferences: " + (error instanceof Error ? error.message : String(error)))
    }
  }

  // Méthodes pour gérer les changements de sélection
  onInterestChange(event: any) {
    this.currentProspect.selectedInterests = event.detail.value || [];
    console.log("Interest selection changed:", this.currentProspect.selectedInterests);
  }

  onContactTypeChange(event: any) {
    this.currentProspect.selectedContactTypes = event.detail.value || [];
    console.log("Contact type selection changed:", this.currentProspect.selectedContactTypes);
  }

  onPreferenceChange(event: any) {
    this.currentProspect.selectedPreferences = event.detail.value || [];
    console.log("Preference selection changed:", this.currentProspect.selectedPreferences);
  }



  // Mettre à jour la méthode de chargement pour l'édition
  async loadProspectDetails(id: number) {
    try {
      this.currentProspect = await this.getProspectById(id)

     

      if (this.currentProspect.lat != null && this.currentProspect.lng != null) {
        this.latLng = { lat: this.currentProspect.lat, lng: this.currentProspect.lng }
      }

      if (this.currentProspect.mapAddress) {
        this.mapAddress = this.currentProspect.mapAddress
      }
       // Charger les relations
      await this.loadProspectRelations(id)

      this.cdr.detectChanges()
      this.renderMap()
    } catch (error) {
      console.error("Failed to load prospect:", error)
    }
  }

  // Nouvelle méthode pour charger les relations
  private async loadProspectRelations(prospectId: number) {
    try {
      // Charger les intérêts
      const interestsResult = await this.db.query("SELECT interest_id FROM prospect_interest WHERE prospect_id = ?", [
        prospectId,
      ])
      this.currentProspect.selectedInterests = interestsResult.values?.map((row) => row.interest_id) || []

      // Charger les types de contact
      const contactTypesResult = await this.db.query(
        "SELECT contact_type_id FROM prospect_contact_type WHERE prospect_id = ?",
        [prospectId],
      )
      this.currentProspect.selectedContactTypes = contactTypesResult.values?.map((row) => row.contact_type_id) || []

      // Charger les préférences
      const preferencesResult = await this.db.query(
        "SELECT preference_id FROM prospect_preference WHERE prospect_id = ?",
        [prospectId],
      )
      this.currentProspect.selectedPreferences = preferencesResult.values?.map((row) => row.preference_id) || []

      // Mettre à jour les états de sélection locaux
      this.interests.forEach((interest) => {
        interest.selected = this.currentProspect.selectedInterests!.includes(interest.id)
      })

      this.contactTypes.forEach((contactType) => {
        contactType.selected = this.currentProspect.selectedContactTypes!.includes(contactType.id)
      })

      this.preferences.forEach((preference) => {
        preference.selected = this.currentProspect.selectedPreferences!.includes(preference.id)
      })
    } catch (error) {
      this.logger.error("Error loading prospect relations: " + (error instanceof Error ? error.message : String(error)))
    }
  }

  async saveProspect() {
    console.log(this.currentProspect);

    if (!this.currentProspect.id) {
      this.currentProspect.id = new Date().getTime();
    }

    try {
      // Check if this.latLng is defined
      if (!this.latLng) {
        throw new Error("Latitude and longitude are not defined");
      }


      // Appeler la méthode du service
      await this.prospectService.saveProspect(this.currentProspect, this.latLng, this.mapAddress, this.isEditMode);

      // Success message
      const alert = await this.alertCtrl.create({
        header: "Operation Successful",
        message: this.isEditMode
          ? "Prospect updated successfully. An administrator will validate it."
          : "Prospect added successfully. An administrator will validate it.",
        buttons: ["OK"],
      });
      await alert.present();
    } catch (error) {
      this.logger.error("Error in saveProspect: " + (error instanceof Error ? error.message : String(error)));
      // Show error alert
      const alert = await this.alertCtrl.create({
        header: "Error",
        message: "Failed to save prospect. Please try again.",
        buttons: ["OK"],
      });
      await alert.present();
    }
  }

  // ... Toutes les autres méthodes existantes restent inchangées ...

  async initSpecialities(searchAll: number) {
    this.logger.info("function: initSpecialities")
    this.specialities = []

    if (searchAll === 1) {
      this.specialities.push({
        id: undefined,
        name: "All",
      })
    }

    console.log("Database object (this.db):", this.db)
    try {
      const result = await this.db.query("SELECT * FROM speciality ORDER BY name")
      console.log("Database query result (specialities):", result)

      if (result && result.values && Array.isArray(result.values)) {
        for (let i = 0; i < result.values.length; i++) {
          const speciality = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.specialities.push(speciality)
        }
        this.cdr.detectChanges()
        this.logger.info("Specialities initialized successfully")
      } else {
        this.logger.warn("No specialities found or result structure is unexpected")
      }

      console.log("Specialities:", this.specialities)
    } catch (error) {
      this.logger.error(
        "Error fetching specialities from database: " + (error instanceof Error ? error.message : String(error)),
      )
    }
  }

  async initSectors(searchAll: number) {
    this.logger.info("function: initSectors")
    this.sectors = []

    if (searchAll === 1) {
      this.sectors.push({
        id: undefined,
        name: "All",
      })
    }

    try {
      const result = await this.db.query("SELECT * FROM sector ORDER BY name")
      console.log("Database query result (sectors):", result)

      if (result && result.values && Array.isArray(result.values)) {
        for (let i = 0; i < result.values.length; i++) {
          const sector = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.sectors.push(sector)
        }
        this.cdr.detectChanges()
        this.logger.info("Sectors initialized successfully")
      } else {
        this.logger.warn("No sectors found or result structure is unexpected")
      }
    } catch (error) {
      this.logger.error(
        "Error fetching sectors from database: " + (error instanceof Error ? error.message : String(error)),
      )
    }
  }

  async initPotential(searchAll: number) {
    this.potentials = []

    if (searchAll === 1) {
      this.potentials.push({
        id: undefined,
        name: "All",
      })
    }

    try {
      const result = await this.db.query("SELECT * FROM potential")

      if (result && result.values && Array.isArray(result.values)) {
        for (let i = 0; i < result.values.length; i++) {
          const potential = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.potentials.push(potential)
        }
        this.cdr.detectChanges()
        this.logger.info("Potentials initialized successfully")
      } else {
        this.logger.warn("No potentials found or result structure is unexpected")
      }
    } catch (error) {
      this.logger.error(
        "Error fetching potentials from database: " + (error instanceof Error ? error.message : String(error)),
      )
    }
  }

  async initTypes(searchAll: number) {
    this.logger.info("function: initTypes")
    this.types = []

    if (searchAll === 1) {
      this.types.push({
        id: undefined,
        name: "All",
      })
    }

    try {
      const result = await this.db.query("SELECT * FROM prospect_type ORDER BY name")

      if (result && result.values && Array.isArray(result.values)) {
        for (let i = 0; i < result.values.length; i++) {
          const type = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.types.push(type)
        }
        this.cdr.detectChanges()
        this.logger.info("Types initialized successfully")
      } else {
        this.logger.warn("No types found or result structure is unexpected")
      }
    } catch (error) {
      this.logger.error(
        "Error fetching types from database: " + (error instanceof Error ? error.message : String(error)),
      )
    }
  }

  async initActivities(): Promise<void> {
    this.logger.info("function: initActivities")
    const rawActivities = this.commonService.getAllActivities()
    rawActivities.sort((a, b) => a.value.localeCompare(b.value))
    this.activities = rawActivities.map((act) => ({ id: act.id, name: act.value }))
    this.cdr.detectChanges()
  }

  async initLocalities(): Promise<void> {
    this.logger.info("function: initLocalities")
    const rawLocalities = await this.filterService.getAllLocalities(false)
    rawLocalities.sort((a, b) => a.name.localeCompare(b.name))
    this.localities = rawLocalities
    this.cdr.detectChanges()
  }

  async initEstablishments(searchAll: number) {
    this.logger.info("function: initEstablishments")
    this.establishments = []

    if (searchAll === 1) {
      this.establishments.push({
        id: undefined,
        name: "All",
      })
    }

    try {
      const result = await this.db.query("SELECT * FROM establishment ORDER BY name")

      if (result && result.values && Array.isArray(result.values)) {
        for (let i = 0; i < result.values.length; i++) {
          const establishment = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.establishments.push(establishment)
        }
        this.cdr.detectChanges()
        this.logger.info("Establishments initialized successfully")
      } else {
        this.logger.warn("No establishments found or result structure is unexpected")
      }
    } catch (error) {
      this.logger.error(
        "Error fetching establishments from database: " + (error instanceof Error ? error.message : String(error)),
      )
    }
  }

  async initGpsOptions() {
    this.options = {
      timeout: 15000,
      enableHighAccuracy: true,
      maximumAge: 0,
    }

    try {
      const result = await this.sqliteService.findOneBy(this.db, "user", { gps_timeout: 1 })
      if (result && result.gps_timeout) {
        this.options.timeout = result.gps_timeout
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error("Error fetching GPS timeout from the database: " + error.message)
      } else {
        this.logger.error("An unknown error occurred while fetching GPS timeout from the database")
      }
    }
  }

  getCurrentLocation(event: any) {
    this.logger.info("function : getCurrentLocation with parameter event = " + JSON.stringify(event))
    this.latLng!.lat = event.latLng.lat()
    this.latLng!.lng = event.latLng.lng()
    this.renderMap()
  }

  onAddressSelection(selectedAddress: any) {
    this.logger.info(
      "function : onAddressSelection with parameter selectedAddress = " + JSON.stringify(selectedAddress),
    )
    this.latLng!.lat = selectedAddress.geometry.location.lat()
    this.latLng!.lng = selectedAddress.geometry.location.lng()
    this.mapAddress = selectedAddress.formatted_address
    this.zoom = this.mapConstants?.ZOOM_17 ?? 17
    this.renderMap()
  }

  async getCurrentPosition() {
    this.logger.info("Click Ma position Button: getCurrentPosition")
    const loading = await this.loadingController.create({
      message: "test",
    })
    await loading.present()

    try {
      const position = await Geolocation.getCurrentPosition()
      if (this.latLng) {
        this.latLng.lat = position.coords.latitude
        this.latLng.lng = position.coords.longitude
      } else {
        this.latLng = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        }
      }
      this.zoom = this.mapConstants.ZOOM_17
      this.renderMap()
      this.logger.info("Position in: " + position.coords.latitude)
    } catch (error: any) {
      this.logger.error("Error in getCurrentPosition function: " + error.message)
      let alertMessage = ""
      if (error.code === error.PERMISSION_DENIED) {
        alertMessage = this.messages.YOU_HAVE_DECLINED_GPS_TRACKING
      } else if (error.code === error.POSITION_UNAVAILABLE) {
        alertMessage = this.messages.YOUR_LOCATION_IS_NOT_ACTIVATED
      } else if (error.code === error.TIMEOUT) {
        alertMessage = this.messages.LOCATION_RETRIEVAL_TIMEOUT_PLEASE_CHECK_YOUR_LOCATION_ACTIVATION
      } else {
        alertMessage = error.message
      }
      const alert = await this.alertCtrl.create({
        header: this.messages.GPS_ERROR,
        message: alertMessage,
        buttons: ["OK"],
      })
      await alert.present()
    } finally {
      await loading.dismiss()
    }
  }

  async sectorChanged(sectorId: number) {
    this.logger.info("function: sectorChanged")
    try {
      const result = await this.db.query("SELECT * FROM locality WHERE sector_id = ?", [sectorId])

      if (result && result.values && Array.isArray(result.values)) {
        this.localities = []
        for (let i = 0; i < result.values.length; i++) {
          const locality = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.localities.push(locality)
        }
        this.logger.info(`${this.localities.length} localities successfully loaded for the selected sector.`)
        this.cdr.detectChanges()
      } else {
        this.localities = []
        this.logger.info("No localities found for the selected sector.")
      }
    } catch (error) {
      this.logger.error("Error in sectorChanged function: " + (error instanceof Error ? error.message : String(error)))
    }
  }

  async localityChanged(localityId: number) {
    this.logger.info("function: localityChanged")
    try {
      const sectorRow = await this.filterService.getSectorByLocality(localityId)
      const sectorOption = this.sectors.find((s) => s.id == sectorRow.id) || { id: sectorRow.id, name: sectorRow.name }
      this.currentProspect.sectorId = sectorOption.id

      if (sectorOption.id != null) {
        this.localities = await this.filterService.filterLocalitiesBySector(sectorOption.id)
      } else {
        this.localities = await this.filterService.getAllLocalities(true)
      }

      const found = this.localities.find((l) => l.id == localityId) || null
      this.currentProspect.localityId = found ? found.id : null
      
    } catch (error) {
      this.logger.error(
        "Error in localityChanged function: " + (error instanceof Error ? error.message : String(error)),
      )
    }
  }

  async activityChanged(activityId: string) {
    this.establishments = []
    try {
      const result = await this.db.query("SELECT * FROM establishment WHERE activity = ?", [activityId])

      if (result && result.values && Array.isArray(result.values)) {
        if (result.values.length > 0) {
          for (let i = 0; i < result.values.length; i++) {
            const element = {
              id: result.values[i].id,
              name: result.values[i].name,
            }
            this.establishments.push(element)
          }
          this.logger.info(
            `${this.establishments.length} establishments successfully loaded for the selected activity.`,
          )
        } else {
          this.logger.info("No establishments found for the selected activity.")
        }
      } else {
        this.logger.warn("Unexpected result structure from the database query.")
      }
    } catch (error) {
      this.logger.error(
        "Error in activityChanged function: " + (error instanceof Error ? error.message : String(error)),
      )
    }
  }

  async save() {
    this.logger.info("function: add prospect")
    const firstname = this.replaceVowel(this.currentProspect.firstname)
    const lastname = this.replaceVowel(this.currentProspect.lastname)

    try {
      if (!this.currentProspect.id) {
        const result = await this.db.query(
          "SELECT firstname, lastname FROM prospect WHERE UPPER(firstname) LIKE ? AND UPPER(lastname) LIKE ? AND status <> 'NOT_AFFECTED'",
          [`%${firstname.toUpperCase()}%`, `%${lastname.toUpperCase()}%`],
        )

        if (result?.values && Array.isArray(result.values)) {
          if (result.values.length > 0) {
            let similarsProspects = ""
            for (let i = 0; i < result.values.length; i++) {
              const prospect = result.values[i]
              if (prospect && typeof prospect === "object" && "firstname" in prospect && "lastname" in prospect) {
                similarsProspects += `${prospect.firstname} ${prospect.lastname}, `
              }
            }

            const alert = await this.alertCtrl.create({
              header: "Existing Lead",
              message: `Similar prospect(s) already exist: ${similarsProspects}. Do you want to continue?`,
              buttons: [
                {
                  text: "Cancel",
                  role: "cancel",
                  handler: () => {
                    this.logger.info("Cancelled adding existing prospect")
                  },
                },
                {
                  text: "OK",
                  handler: () => {
                    this.saveProspect()
                  },
                },
              ],
            })
            await alert.present()
          } else {
            this.saveProspect()
          }
        } else {
          this.logger.warn("Unexpected result structure from the database query.")
          this.saveProspect()
        }
      } else {
        this.saveProspect()
      }
    } catch (err) {
      if (err instanceof Error) {
        this.logger.error("Error in add prospect function: " + err.message)
      } else {
        this.logger.error("An unknown error occurred in add prospect function")
      }
    }
  }

  navigateHome() {
    this.router.navigate(["/home"])
  }

  replaceVowel(str: string): string {
    str = str.toLowerCase()
    str = str
      .replace(/a/g, "_")
      .replace(/e/g, "_")
      .replace(/o/g, "_")
      .replace(/i/g, "_")
      .replace(/u/g, "_")
      .replace(/y/g, "_")
    return str
  }

  checkValidity(): boolean {
    return (
      this.inputs.firstname.length === 0 ||
      this.inputs.lastname.length === 0 ||
      this.inputs.speciality.length === 0 ||
      this.inputs.potentiel.length === 0 ||
      this.inputs.locality.length === 0 ||
      this.inputs.activity.length === 0 ||
      this.inputs.sector.length === 0 ||
      this.inputs.type.length === 0
    )
  }

  async filterLocalitiesBySector(sectorId: number) {
    this.logger.info("function: filterLocalitiesBySector with parameter: sectorId: " + sectorId)
    this.localities = []
    this.localities.push({
      id: undefined,
      name: "All",
    })

    try {
      const query = "SELECT * FROM locality WHERE sectorId = ?"
      const result = await this.db.query(query, [sectorId])

      console.log("Query result for localities:", result)

      if (result && result.values && Array.isArray(result.values)) {
        for (let i = 0; i < result.values.length; i++) {
          const locality = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.localities.push(locality)
        }

        console.log("Localities added to localities:", this.localities)
      } else {
        this.logger.warn(`No localities found for sectorId: ${sectorId}`)
      }

      this.cdr.detectChanges()
    } catch (error) {
      this.logger.error(
        `Error in filterLocalitiesBySector function: ${error instanceof Error ? error.message : String(error)}`,
      )
    }
  }

  async getLocalitiesBySectorToEdit(sectorSelected: any) {
    this.prospectToEdit.sectorSelected = sectorSelected
    const sectorId = sectorSelected.id
    this.logger.info("function: getLocalitiesBySectorToEdit with parameter: sectorId: " + sectorId)

    this.formLocalities = []

    try {
      const query = "SELECT * FROM locality WHERE sectorId = ?"
      const result = await this.db.query(query, [sectorId])

      console.log("Query result for localities:", result)

      if (result && result.values && Array.isArray(result.values)) {
        for (let i = 0; i < result.values.length; i++) {
          const locality = {
            id: result.values[i].id,
            name: result.values[i].name,
          }
          this.formLocalities.push(locality)
        }

        console.log("Localities added to formLocalities:", this.formLocalities)
      } else {
        this.logger.warn(`No localities found for sectorId: ${sectorId}`)
      }

      this.cdr.detectChanges()
    } catch (error) {
      this.logger.error(
        `Error in getLocalitiesBySectorToEdit function: ${error instanceof Error ? error.message : String(error)}`,
      )
    }
  }

  async getProspectById(id: number): Promise<Prospect> {
    const query = `
      SELECT
        p.*,
        s.name as sectorName,
        l.name as localityName,
        sp.name as specialityName,
        e.name as establishmentName,
        pot.name as potentialName
      FROM prospect p
      LEFT JOIN sector s ON p.sector_id = s.id
      LEFT JOIN locality l ON p.locality_id = l.id
      LEFT JOIN speciality sp ON p.speciality_id = sp.id
      LEFT JOIN establishment e ON p.establishment_id = e.id
      LEFT JOIN potential pot ON p.potential = pot.id
      WHERE p.id = ?`

    try {
      const result = await this.db.query(query, [id])
      if (result.values && result.values.length > 0) {
        const prospectData = result.values[0]
        const prospect: Prospect = new Prospect()
        prospect.id = prospectData.id
        prospect.firstname = prospectData.firstname
        prospect.lastname = prospectData.lastname
        prospect.grade = prospectData.grade
        prospect.note = prospectData.note
        prospect.typeId = prospectData.type_id
        prospect.sectorName = prospectData.sectorName
        prospect.sectorId = prospectData.sector_id
        prospect.localityName = prospectData.localityName
        prospect.localityId = prospectData.locality_id
        prospect.activity = prospectData.activity
        prospect.address = prospectData.address
        prospect.gsm = prospectData.gsm
        prospect.lat = prospectData.lat
        prospect.lng = prospectData.lng
        prospect.mapAddress = prospectData.map_address
        prospect.establishmentName = prospectData.establishmentName
        prospect.establishmentId = prospectData.establishment_id
        prospect.secretary = prospectData.secretary
        prospect.specialityId = prospectData.speciality_id
        prospect.specialityName = prospectData.specialityName
        prospect.potentialName = prospectData.potentialName
        prospect.potentialId = prospectData.potential
        prospect.phone = prospectData.phone
        prospect.email = prospectData.email
        prospect.fiscalNumber = prospectData.fiscal_number
        return prospect
      } else {
        throw new Error(`No prospect found with ID ${id}`)
      }
    } catch (error) {
      console.error("Error retrieving prospect:", error)
      throw error
    }
  }

  validateForm(form: NgForm) {
    this.submitted = true
    Object.keys(form.controls).forEach((key) => {
      const control = form.controls[key]
      control?.markAsTouched()
      control?.updateValueAndValidity()
      if (control && control.invalid) {
        this.logger.warn(`Invalid field: ${key} - ${JSON.stringify(control.errors)}`)
      }
    })

    if (form.valid) {
      this.save()
    } else {
      this.alertCtrl
        .create({
          header: "Missing Information",
          message: "Please fill in all mandatory fields before saving.",
          buttons: ["OK"],
        })
        .then((alert) => alert.present())
    }
  }

  private renderMap() {
    if (!this.mapContainer || !window.google?.maps) {
      return
    }

    const center = this.latLng ?? { lat: this.mapConstants.LAT_CENTER_VALUE, lng: this.mapConstants.LNG_CENTER_VALUE }
    this.map = new window.google.maps.Map(this.mapContainer.nativeElement, {
      center,
      zoom: this.zoom ?? this.mapConstants.ZOOM_12,
    })

    if (this.latLng) {
      this.marker = new window.google.maps.Marker({ position: this.latLng, map: this.map })
    }

    this.map.addListener("click", (e: google.maps.MapMouseEvent) => {
      if (!e.latLng) return
      this.latLng = { lat: e.latLng.lat(), lng: e.latLng.lng() }
      if (this.marker) this.marker.setMap(null)
      this.marker = new window.google.maps.Marker({ position: e.latLng, map: this.map })
    })
  }
}
  
  
  
  

  
  