<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/"></ion-back-button>
    </ion-buttons>
    <ion-title>{{LABELS.ADD_PROSPECT}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [scrollY]="true">
  <form #myForm="ngForm" [class.submitted]="submitted" novalidate>
    <ion-grid>
      
      <!-- Firstname, Lastname, Speciality -->
      <ion-row>
        <ion-col>
          <ion-item [class.invalid-field]="submitted && !currentProspect.firstname"
                    [class.ion-invalid]="submitted && !currentProspect.firstname">
            <ion-label position="floating">{{LABELS.ENTER_FIRST_NAME}} (*)</ion-label>
            <ion-input type="text" name="firstname" [(ngModel)]="currentProspect.firstname" required></ion-input>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.firstname" color="danger">
            {{LABELS.FIRST_NAME_REQUIRED}}
          </ion-note>
        </ion-col>
        
        <ion-col>
          <ion-item [class.invalid-field]="submitted && !currentProspect.lastname"
                    [class.ion-invalid]="submitted && !currentProspect.lastname">
            <ion-label position="floating">{{LABELS.ENTER_NAME}} (*)</ion-label>
            <ion-input type="text" name="lastname" [(ngModel)]="currentProspect.lastname" required></ion-input>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.lastname" color="danger">
            {{LABELS.LAST_NAME_REQUIRED}}
          </ion-note>
        </ion-col>
        
        <ion-col>
          <ion-item (click)="specialitySelect.open()" 
                    [class.invalid-field]="submitted && !currentProspect.specialityId"
                    [class.ion-invalid]="submitted && !currentProspect.specialityId">
            <ion-label>{{LABELS.CHOOSE_SPECIALITY}} (*)</ion-label>
            <ion-select interface="action-sheet" name="specialityId" 
                       [(ngModel)]="currentProspect.specialityId" required #specialitySelect>
              <ion-select-option *ngFor="let speciality of specialities" [value]="speciality.id">
                {{speciality.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.specialityId" color="danger">
            {{LABELS.SPECIALITY_REQUIRED}}
          </ion-note>
        </ion-col>
      </ion-row>

      <!-- Potential, Type, Activity, Sector -->
      <ion-row>
        <ion-col>
          <ion-item (click)="potentialSelect.open()" 
                    [class.invalid-field]="submitted && !currentProspect.potentialId"
                    [class.ion-invalid]="submitted && !currentProspect.potentialId">
            <ion-label>{{LABELS.CHOOSE_POTENTIAL}} (*)</ion-label>
            <ion-select name="potentialId" [(ngModel)]="currentProspect.potentialId" required #potentialSelect>
              <ion-select-option *ngFor="let potential of potentials" [value]="potential.id">
                {{potential.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.potentialId" color="danger">
            {{LABELS.POTENTIAL_REQUIRED}}
          </ion-note>
        </ion-col>
        
        <ion-col>
          <ion-item (click)="typeSelect.open()" 
                    [class.invalid-field]="submitted && !currentProspect.typeId"
                    [class.ion-invalid]="submitted && !currentProspect.typeId">
            <ion-label>{{LABELS.CHOOSE_TYPE}} (*)</ion-label>
            <ion-select name="typeId" [(ngModel)]="currentProspect.typeId" required #typeSelect>
              <ion-select-option *ngFor="let type of types" [value]="type.id">
                {{type.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.typeId" color="danger">
            {{LABELS.TYPE_REQUIRED}}
          </ion-note>
        </ion-col>
        
        <ion-col>
          <ion-item (click)="activitySelect.open()" 
                    [class.invalid-field]="submitted && !currentProspect.activity"
                    [class.ion-invalid]="submitted && !currentProspect.activity">
            <ion-label>{{LABELS.CHOOSE_ACTIVITY}} (*)</ion-label>
            <ion-select
              name="activity"
              [(ngModel)]="currentProspect.activity"
              required
              (ionChange)="activityChanged(currentProspect.activity!)"
              #activitySelect>
              <ion-select-option *ngFor="let activity of activities" [value]="activity.id">
                {{ activity.name }}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.activity" color="danger">
            {{LABELS.ACTIVITY_REQUIRED}}
          </ion-note>
        </ion-col>
        
        <ion-col>
          <ion-item (click)="sectorSelect.open()" 
                    [class.invalid-field]="submitted && !currentProspect.sectorId"
                    [class.ion-invalid]="submitted && !currentProspect.sectorId">
            <ion-label>{{LABELS.CHOOSE_SECTOR}} (*)</ion-label>
            <ion-select name="sectorId" [(ngModel)]="currentProspect.sectorId" required 
                       (ionChange)="sectorChanged($event.detail.value)" #sectorSelect>
              <ion-select-option *ngFor="let sector of sectors" [value]="sector.id">
                {{sector.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.sectorId" color="danger">
            {{LABELS.SECTOR_REQUIRED}}
          </ion-note>
        </ion-col>
      </ion-row>

      <!-- Matricule Fiscal -->
      <ion-row>
        <ion-col>
          <ion-item>
            <ion-label position="floating">{{LABELS.FISCAL_NUMBER}}</ion-label>
            <ion-input type="text" name="fiscalNumber" [(ngModel)]="currentProspect.fiscalNumber"></ion-input>
          </ion-item>
        </ion-col>
      </ion-row>

            <!-- Centres d'intérêt, Types de contact, Préférences - Listes déroulantes multiples -->
            <ion-row>
              <ion-col>
                <ion-item (click)="interestsSelect.open()">
                  <ion-label>{{LABELS.CHOOSE_INTERESTS}}</ion-label>
                  <ion-select
                    name="interests"
                    [value]="currentProspect.selectedInterests"
                    multiple="true"
                    (ionChange)="onInterestChange($event)"
                    #interestsSelect>
                    <ion-select-option *ngFor="let interest of interests" [value]="interest.id">
                      {{interest.name}}
                    </ion-select-option>
                  </ion-select>
                </ion-item>
              </ion-col>
             
              <ion-col>
                <ion-item (click)="contactTypesSelect.open()">
                  <ion-label>{{LABELS.CHOOSE_CONTACT_TYPES}}</ion-label>
                  <ion-select
                    name="contactTypes"
                    [value]="currentProspect.selectedContactTypes"
                    multiple="true"
                    (ionChange)="onContactTypeChange($event)"
                    #contactTypesSelect>
                    <ion-select-option *ngFor="let contactType of contactTypes" [value]="contactType.id">
                      <ion-icon *ngIf="contactType.icon" [name]="contactType.icon" slot="start"></ion-icon>
                      {{contactType.name}}
                    </ion-select-option>
                  </ion-select>
                </ion-item>
              </ion-col>
             
              <ion-col>
                <ion-item (click)="preferencesSelect.open()">
                  <ion-label>{{LABELS.CHOOSE_PREFERENCES}}</ion-label>
                  <ion-select
                    name="preferences"
                    [value]="currentProspect.selectedPreferences"
                    multiple="true"
                    (ionChange)="onPreferenceChange($event)"
                    #preferencesSelect>
                    <ion-select-option *ngFor="let preference of preferences" [value]="preference.id">
                      {{preference.name}}
                    </ion-select-option>
                  </ion-select>
                </ion-item>
              </ion-col>
            </ion-row>
      
      <!-- Locality, Establishment, Grade -->
      <ion-row>
        <ion-col>
          <ion-item (click)="localitySelect.open()" 
                    [class.invalid-field]="submitted && !currentProspect.localityId"
                    [class.ion-invalid]="submitted && !currentProspect.localityId">
            <ion-label>{{LABELS.CHOOSE_LOCALITY}} (*)</ion-label>
            <ion-select name="localityId" [(ngModel)]="currentProspect.localityId" required 
                       (ionChange)="localityChanged($event.detail.value)" #localitySelect>
              <ion-select-option *ngFor="let locality of localities" [value]="locality.id">
                {{locality.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <ion-note *ngIf="submitted && !currentProspect.localityId" color="danger">
            {{LABELS.LOCALITY_REQUIRED}}
          </ion-note>
        </ion-col>
        
        <ion-col>
          <ion-item (click)="establishmentSelect.open()">
            <ion-label>{{LABELS.CHOOSE_ESTABLISHMENT}}</ion-label>
            <ion-select name="establishmentId" [(ngModel)]="currentProspect.establishmentId" #establishmentSelect>
              <ion-select-option *ngFor="let est of establishments" [value]="est.id">
                {{est.name}}
              </ion-select-option>
            </ion-select>
          </ion-item>
        </ion-col>
        
        <ion-col>
          <ion-item (click)="gradeSelect.open()">
            <ion-label>{{LABELS.CHOOSE_GRADE}}</ion-label>
            <ion-select name="grade" [(ngModel)]="currentProspect.grade" #gradeSelect>
              <ion-select-option value="Interne">{{LABELS.INTERNAL}}</ion-select-option>
              <ion-select-option value="Résident">{{LABELS.RESIDENT}}</ion-select-option>
              <ion-select-option value="Assistant">{{LABELS.ASSISTANT}}</ion-select-option>
              <ion-select-option value="Chef service">{{LABELS.CHIEF_SERVICE}}</ion-select-option>
              <ion-select-option value="Professeur agrégé">{{LABELS.ASSOCIATE_PROFESSOR}}</ion-select-option>
              <ion-select-option value="Professeur">{{LABELS.TEACHER}}</ion-select-option>
              <ion-select-option value="Docteur">{{LABELS.DOCTOR}}</ion-select-option>
              <ion-select-option value="Paramédical">{{LABELS.PARAMEDICAL}}</ion-select-option>
              <ion-select-option value="Pharmacien">{{LABELS.PHARMACIST}}</ion-select-option>
            </ion-select>
          </ion-item>
        </ion-col>
      </ion-row>

      <!-- GSM, Phone, Email -->
      <ion-row>
        <ion-col>
          <ion-item>
            <ion-label position="floating">{{LABELS.GSM}}</ion-label>
            <ion-input
              type="text"
              name="gsm"
              [(ngModel)]="currentProspect.gsm"
              inputmode="numeric">
            </ion-input>
        
            <ion-buttons slot="end">
              <a
              [href]="'https://wa.me/' + (currentProspect?.gsm || '')"
              target="_blank">
              <ion-button fill="clear">
                <ion-icon name="logo-whatsapp" slot="icon-only" color="success"></ion-icon>
              </ion-button>
            </a>
            
            </ion-buttons>
          </ion-item>
        </ion-col>
        
        
        <ion-col>
          <ion-item [class.invalid-field]="submitted && currentProspect.activity === 'P' && !currentProspect.phone"
                    [class.ion-invalid]="submitted && currentProspect.activity === 'P' && !currentProspect.phone">
            <ion-label position="floating">{{LABELS.PHONE}} <span *ngIf="currentProspect.activity === 'P'">(*)</span></ion-label>
            <ion-input type="text" name="phone" [(ngModel)]="currentProspect.phone" inputmode="numeric" 
                      [required]="currentProspect.activity === 'P'"></ion-input>
          </ion-item>
          <ion-note *ngIf="submitted && currentProspect.activity === 'P' && !currentProspect.phone" color="danger">
            {{LABELS.PHONE_REQUIRED}}
          </ion-note>
        </ion-col>
        
        <ion-col>
          <ion-item>
            <ion-label position="floating">{{LABELS.EMAIL}}</ion-label>
            <ion-input
              type="email"
              name="email"
              [(ngModel)]="currentProspect.email"
              email>
            </ion-input>
        
            <ion-buttons slot="end">
              <a
                [href]="'mailto:' + (currentProspect?.email || '')"
                target="_blank">
                <ion-button fill="clear">
                  <ion-icon name="mail-outline" slot="icon-only" color="primary"></ion-icon>
                </ion-button>
              </a>
            </ion-buttons>
          </ion-item>
        </ion-col>
        
      </ion-row>

      <!-- Address -->
      <ion-row>
        <ion-col>
          <ion-item [class.invalid-field]="submitted && currentProspect.activity === 'P' && !currentProspect.address"
                    [class.ion-invalid]="submitted && currentProspect.activity === 'P' && !currentProspect.address">
            <ion-label position="floating">{{LABELS.ADRESS}} <span *ngIf="currentProspect.activity === 'P'">(*)</span></ion-label>
            <ion-textarea name="address" [(ngModel)]="currentProspect.address" maxlength="255" 
                         [required]="currentProspect.activity === 'P'"></ion-textarea>
          </ion-item>
          <ion-note *ngIf="submitted && currentProspect.activity === 'P' && !currentProspect.address" color="danger">
            {{LABELS.ADRESS_REQUIRED}}
          </ion-note>
        </ion-col>
      </ion-row>

      <!-- Map Section -->
      <ion-row class="ion-justify-content-center" style="margin-bottom: 8px;">
        <ion-col>
          <ion-button expand="block" class="main_btn" style="min-height: 33px; line-height: 35px;" 
                     (click)="getCurrentPosition()">
            {{ LABELS.UPDATE_MY_POSITION }}
          </ion-button>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col>
          <div #mapContainer style="width:100%; height:250px; border:1px solid #ccc;"></div>
        </ion-col>
      </ion-row>

      <!-- Action Buttons -->
      <ion-row>
        <ion-col>
          <ion-button expand="block" color="primary" (click)="validateForm(myForm)">
            {{LABELS.SAVE}}
          </ion-button>
        </ion-col>
        <ion-col>
          <ion-button expand="block" color="light" (click)="navigateHome()">
            {{LABELS.RETURN}}
          </ion-button>
        </ion-col>
      </ion-row>

    </ion-grid>
  </form>
</ion-content>