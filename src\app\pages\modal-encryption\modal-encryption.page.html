<app-header title="Encryption Modal" closeLabel="Close" closeSlot="end" (close)="close()"></app-header>


<ion-content>
  <ion-list *ngFor="let dbName of dbList; index as idx">
    <ion-item mode="ios">
      <ion-toggle id="toggle-modal-encryption" slot="end" [checked]="isEncryptionChecked[idx]" [disabled]="isEncryptionDisabled[idx]" (ionChange)="handleEncryptionToggle(idx)">
        {{dbName.split("SQLite.db")[0]}}
      </ion-toggle>
    </ion-item>
  </ion-list>
</ion-content>
