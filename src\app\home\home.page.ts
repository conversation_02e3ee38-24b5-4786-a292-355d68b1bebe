import { <PERSON>mpo<PERSON>,  <PERSON><PERSON><PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON> } from "@angular/core"
import { CommonModule } from "@angular/common"
import { IonicModule } from "@ionic/angular"
import { FormsModule } from "@angular/forms"
import { RouterModule } from "@angular/router"
import  { <PERSON>dal<PERSON>ontroller } from "@ionic/angular"
import { PostsPage } from "src/app/pages/author-posts/posts/posts.page"
import { EmployeesPage } from "src/app/pages/employee-dept/employees/employees.page"
import  { InitializeAppService } from "src/app/services/initialize.app.service"
import  { SQLiteService } from "src/app/services/sqlite.service"
import { App } from "@capacitor/app"
import { ModalPassphrasePage } from "src/app/pages/modal-passphrase/modal-passphrase.page"
import { ModalEncryptionPage } from "src/app/pages/modal-encryption/modal-encryption.page"
import  { TranslationService } from "src/app/services/traduction-service.service"
import  { ThemeService } from "src/app/services/theme.service"
import  { LoginService } from "src/app/services/login-service.service"
import { SyncService } from "src/app/services/sync.service"
import  { Router } from "@angular/router"
import { HeaderComponent } from "../header/header.component"
import  { SyncState, SyncStateService } from "../services/sync-state.service"
import  { Subscription } from "rxjs"

@Component({
  selector: "app-home",
  templateUrl: "home.page.html",
  imports: [HeaderComponent, CommonModule, IonicModule, FormsModule, RouterModule],
  standalone: true,
})
export class HomePage implements OnInit, OnDestroy {
  isListDisplay = false
  isAndroid = false
  isNative = false
  isElectron = false
  isEncrypt = false
  translations: any = {}

  private syncSubscription?: Subscription
  private translationSubscription?: Subscription
  // ✅ Subscription pour écouter les changements de langue
  private languageSubscription?: Subscription

  currentLanguage = "an.json"

  syncState: SyncState = {
    isReceiving: false,
    isSending: false,
    message: "",
  }

  constructor(
    private initAppService: InitializeAppService,
    private sqliteService: SQLiteService,
    private modalCtrl: ModalController,
    private translationService: TranslationService,
    private themeService: ThemeService,
    private LoginService: LoginService,
    private SyncService: SyncService,
    private router: Router,
    private syncStateService: SyncStateService,
  ) {
    this.isListDisplay = this.initAppService.isAppInit
  }

  async ngOnInit() {
    if (this.initAppService.platform === "android") {
      this.isAndroid = true
    }
    if (this.initAppService.platform === "electron") {
      this.isElectron = true
    }

    this.isNative = this.sqliteService.native
    this.isEncrypt =
      (this.isNative || this.isElectron) && (await this.sqliteService.isInConfigEncryption()).result ? true : false

    // ✅ S'abonner aux changements de traductions
    this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
      console.log("🔄 HomePage - Traductions mises à jour:", translations)
      this.translations = translations
    })

    // ✅ S'abonner aux changements de langue
    this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
      console.log("🌐 HomePage - Langue changée vers:", language)
      this.currentLanguage = language
    })

    // S'abonner aux changements d'état de synchronisation
    this.syncSubscription = this.syncStateService.syncState$.subscribe((state) => {
      this.syncState = state
    })

    console.log("✅ HomePage - Initialisé avec langue:", this.translationService.getCurrentLanguage())
  }

  ngOnDestroy() {
    if (this.syncSubscription) {
      this.syncSubscription.unsubscribe()
    }
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    // ✅ Nettoyer la subscription de langue
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
  }

  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ HomePage - Traduction manquante: ${key}`)
    }
    return translation || key
  }

  // ✅ Changement de langue simplifié - utilise le service centralisé
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 HomePage - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ HomePage - Langue changée avec succès")
    } catch (error) {
      console.error("❌ HomePage - Erreur changement langue:", error)
    }
  }

  async authorpostsClick() {
    const modal = await this.modalCtrl.create({
      component: PostsPage,
      canDismiss: true,
    })
    modal.present()
  }

  async employeesClick() {
    const modal = await this.modalCtrl.create({
      component: EmployeesPage,
      canDismiss: true,
    })
    modal.present()
  }

  exitApp() {
    App.exitApp()
  }

  async setPassphrase() {
    const modalPassphrase = await this.modalCtrl.create({
      component: ModalPassphrasePage,
      breakpoints: [0.1, 0.55, 0.85],
      initialBreakpoint: 0.55,
      cssClass: "custom-modal",
    })
    await modalPassphrase.present()
  }

  async dbEncryption() {
    const modalEncryption = await this.modalCtrl.create({
      component: ModalEncryptionPage,
      breakpoints: [0.1, 0.85, 1],
      initialBreakpoint: 0.85,
      cssClass: "custom-modal",
    })
    await modalEncryption.present()
  }

  switchTheme(): void {
    this.themeService.switchTheme()
  }

  async receive() {
    await this.SyncService.receive()
  }

  async send() {
    await this.SyncService.send()
  }

  logout() {
    this.LoginService.logout()
  }

  onThemeChange(): void {
    this.themeService.switchTheme()
  }
}
