import { Inject, Injectable } from '@angular/core';
import { CapacitorSQLite, SQLiteDBConnection } from '@capacitor-community/sqlite';
import { LoggerService } from './logger.service'; // Assurez-vous que ce service est configuré ou adaptez-le
import { QUERIES } from 'src/app/constants'; 
import { environment } from 'src/environments/environment';
import { TablesUpgrades } from '../upgrades/tables/tables';
import { SQLiteService } from './sqlite.service';
import { DbnameVersionService } from './dbname-version.service';
import { DateService } from './date.service';
import { LoadingController } from '@ionic/angular';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { InjectionToken } from '@angular/core';
import { LoginService } from 'src/app/services/login-service.service';
import { PopupService } from 'src/app/services/popup-service.service';
import { DbCreationTablesService } from './db-creation-tables.service';
import { UserService } from './user-service.service';
import { CommonService } from './common-service.service';
import { BudgetAllocation } from '../models/budget-allocations';
import { SyncStateService } from './sync-state.service';

@Injectable({
  providedIn: 'root'
})
export class SyncService {
  private mDb=  this.dbCreationTablesService.getDatabase();
  public isSyncronizing = false;
  currentUser :any
  userId: any;


  public databaseName: string;
  constructor(
    private commonService: CommonService,
    private userService: UserService,
    private popupService: PopupService,
    private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
    private fileLogger: LoggerService,
    private http: HttpClient,
    private loadingCtrl: LoadingController ,
    private DateService: DateService,
    @Inject(QUERIES) private queries: any,
    private LoginService:LoginService,// Ensure this service exists for logging
    private dbCreationTablesService: DbCreationTablesService, // Ensure this service exists for creating tables
    private syncStateService: SyncStateService,
    ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }
  private async getApiUrl(endpoint = ""): Promise<string> {
    try {
      const baseUrl = this.LoginService.getCurrentLaboUrl()

      // Handle the case where baseUrl is null
      if (baseUrl === null) {
        // You must decide how to handle an unconfigured base URL here.
        // Option 1: Throw an error (recommended for explicit configuration)
        throw new Error("Base URL is not configured. Please select a labo first.")
        // Option 2: Fallback to a hardcoded URL (if acceptable for your app's logic)
        // return `https://demo.bird-notes.com/api${endpoint}`;
      }

      const hasApiPath = baseUrl.includes("/api")
      const isLocalhost = baseUrl.includes("localhost")
      let apiPath = ""
      if (!hasApiPath && !isLocalhost) {
        apiPath = "/api"
      }
      return `${baseUrl}${apiPath}${endpoint}`
    } catch (error) {
      console.error("Error getting API URL:", error)
      // Fallback to a default URL if there's an error or if baseUrl is null and you chose to fallback
      // This fallback is from your original code. If you want no static URLs, this should be removed
      // and the calling code should handle the error from the `try` block above.
      return `https://demo.bird-notes.com/api${endpoint}`
    }
  }
  synchronizeVisitHistory(prospectId: number): Promise<any> {
    // Implementation of the synchronization logic
    return new Promise((resolve, reject) => {
      // Example: Perform API call or local database sync
      console.log('Synchronizing for prospectId:', prospectId);
      resolve({ success: true });
    });
  }

  private async execQuery(code: string, sql: string, params: any[]) {
    console.debug(`[SYNC][${code}] →`, sql, "\nbinds:", params);
    try {
      return await this.mDb.query(sql, params);
    } catch (err) {
      console.error(
        `[SYNC][${code}] FAILED\nSQL: ${sql}\nbinds: ${JSON.stringify(params)}`,
        err
      );
      throw err;
    }
  }

  private formatQuery(query: string, params: any[]): string {
    // This is a basic example. Adjust as necessary based on your query parameter handling.
    params.forEach((param, index) => {
      query = query.replace(`$${index + 1}`, `'${param}'`);
    });
    return query;
  }
  private async deleteAndInsert(tableName: string, records: any[], insertQuery: string): Promise<void> {
    this.fileLogger.info(`Start updating ${tableName}`);
    
    try {
      // Delete all records from the table
      await this.mDb.run(`DELETE FROM ${tableName}`);
  
      // Get the table schema to identify valid columns
      const schemaResult = await this.mDb.query(`PRAGMA table_info(${tableName})`);
      if (!schemaResult.values) {
        throw new Error(`Failed to retrieve schema for table ${tableName}`);
      }
      const validColumns = schemaResult.values.map((col: any) => col.name);
  
      // Prepare and execute insert queries
      for (const record of records) {
        const keys = Object.keys(record).filter(key => validColumns.includes(key));
        const values = keys.map(key => record[key]);
        const placeholders = keys.map(() => '?').join(', ');
        
        // Construct the INSERT query dynamically
        const query = `INSERT OR REPLACE INTO ${tableName} (${keys.join(', ')}) VALUES (${placeholders})`;
        await this.mDb.run(query, values);
      }
    } catch (error: any) {
      this.fileLogger.error(`Error updating ${tableName}: ${error.message}`);
    }
  }
  

  async deleteAndInsertWholeSalers(wholesalers: any[]): Promise<void> {
    const insertQuery = this.queries.INSERT_WHOLESALER;
    await this.deleteAndInsert('wholesaler', wholesalers, insertQuery);
  }

  async deleteAndInsertPotentialProducts(potentialProducts: any[]): Promise<void> {
    const insertQuery = this.queries.INSERT_POTENTIAL_PRODUCT;
    await this.deleteAndInsert('potential_product', potentialProducts, insertQuery);
  }

  private async deleteAndInsertChargePlan(chargePlan: any[]): Promise<void> {
    this.fileLogger.info("Start updating charge plan");
    try {
      await this.mDb.query(this.queries.DELETE_ALL_CHARGE_PLAN);
      for (const plan of chargePlan) {
        await this.mDb.query(this.queries.INSERT_CHARGE_PLAN, [plan.specialityId, plan.productId, plan.rank]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error: any) {
      this.fileLogger.info("Error in updating charge plan: " + error.message);
    }
  }

  private async deleteAndInsertGoals(goals: any[]): Promise<void> {
    this.fileLogger.info("Start updating goals");
    try {
      await this.mDb.query(this.queries.DELETE_ALL_GOAL);
      for (const goal of goals) {
        await this.mDb.run(this.queries.INSERT_GOAL, [goal.id, goal.itemsOrder,goal.name,  goal.firstDate, goal.lastDate, goal.goalType]);

        
      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error: any) {
      this.fileLogger.info("Error in updating goals: " + error.message);
    }
  }
  private async insertPlannings(plannings: any[]): Promise<void> {
    this.fileLogger.info('Insertion of plannings started');
    try {
      for (const planning of plannings) {
        const timestamp = new Date(planning.date).setHours(0, 0, 0, 0);
        await this.mDb.run(this.queries.INSERT_PLANNING, [
          planning.identifier,
          planning.prospect,
          timestamp,
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      this.fileLogger.info('Insertion of plannings finished');
    } catch (error: any) {
      this.fileLogger.error('Error in inserting plannings: ' + error.message);
    }
  }
  private async insertPlanningValidations(planningValidations: any[]): Promise<void> {
    this.fileLogger.info('Insertion of planning validations started');
    try {
      for (const validation of planningValidations) {
        const timestamp = new Date(validation.date).setHours(0, 0, 0, 0);
        await this.mDb.run(this.queries.INSERT_PLANNING_VALIDATION, [
          validation.identifier,
          validation.notes,
          timestamp,
          validation.status,
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      this.fileLogger.info('Insertion of planning validations finished');
    } catch (error: any) {
      this.fileLogger.error('Error in inserting planning validations: ' + error.message);
    }
  }
  private async deleteAndInsertGoalItems(goalItems: any[]): Promise<void> {
    this.fileLogger.info("Start updating goal items");
    try {
      await this.mDb.query(this.queries.DELETE_ALL_GOAL_ITEM);
      for (const item of goalItems) {
        await this.mDb.run(this.queries.INSERT_GOAL_ITEM, [
          item.activity, 
          item.potentialId, 
          item.productId, 
          item.sectorId, 
          item.specialityId, 
          item.prospectTypeId, 
          item.value, 
          item.goal
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error: any) {
      this.fileLogger.info("Error in updating goal items: " + error.message);
    }
  }


  private async deleteAndInsertPurchaseOrderTemplates(purchaseOrderTemplates: any[]): Promise<void> {
    this.fileLogger.info("Start updating purchaseOrderTemplates");
    try {
      await this.mDb.execute(this.queries.DELETE_ALL_POT);
      for (const template of purchaseOrderTemplates) {
        const gifts = template.gifts.map((gift :any )=> gift.name).join(', ');
        const firstDate = new Date(template.firstDate).setHours(0, 0, 0, 0);
        const lastDate = new Date(template.lastDate).setHours(23, 59, 59, 0);
        await this.mDb.run(this.queries.INSERT_POT, [
          template.id, 
          template.name, 
          firstDate, 
          lastDate, 
          gifts
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error:any) {
      this.fileLogger.info("Error in updating purchaseOrderTemplates: " + error.message);
    }
  }

  private async deleteAndInsertPurchaseOrderTemplateItems(purchaseOrderTemplates: any[]): Promise<void> {
    this.fileLogger.info("Start updating purchaseOrderTemplateItem");
    try {
      await this.mDb.execute(this.queries.DELETE_ALL_POT_ITEM);
      for (const template of purchaseOrderTemplates) {
        for (const item of template.purchaseOrderTemplateItem) {
          await this.mDb.run(this.queries.INSERT_POT_ITEM, [
            item.id, 
            item.productId, 
            item.quantity, 
            item.freeOrder, 
            item.labGratuity, 
            item.purchaseOrderTemplateId
          ]);

        }
      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error: any) {
      this.fileLogger.info("Error in updating purchaseOrderTemplateItem: " + error.message);
    }
  }

  private async deleteAndInsertFreeQuantityRuleItems(freeQuantityRuleItems: any[]): Promise<void> {
    this.fileLogger.info("Start updating FreeQuantityRuleItem");
    try {
      await this.mDb.query(this.queries.DELETE_ALL_FQR_ITEM);
      for (const item of freeQuantityRuleItems) {
        await this.mDb.run(this.queries.INSERT_FQR_ITEM, [
          item.productId, 
          item.potentialId, 
          item.prospectTypeId, 
          item.orderQuantity, 
          item.freeQuantity, 
          item.labGratuity
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error:any) {
      this.fileLogger.info("Error in updating FreeQuantityRuleItem: " + error.message);
    }
  }

  private async deleteAndInsertExpenseTypes(expenseTypes: any[]): Promise<void> {
    this.fileLogger.info("Start updating expense types");
    try {
      await this.mDb.query(this.queries.DELETE_ALL_EXPENSE_REPORT);
      for (const type of expenseTypes) {
        await this.mDb.run(this.queries.INSERT_EXPENSE_TYPE, [
          type.id, 
          type.name, 
          type.price, 
          type.requiredAttachment
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error:any) {
      this.fileLogger.info("Error in updating expense types: " + error.message);
    }
  }
  private async insertCurrentExpenses(expenses: any[]): Promise<void> {
    this.fileLogger.info('Insertion of current expenses started');
    try {
      for (const expense of expenses) {
        await this.mDb.run(this.queries.INSERT_NOTEFRAIS, [
          expense.identifier,
          expense.activityId,
          expense.date,
          expense.expenseTypeDto.id,
          expense.montant,
          expense.description,
          expense.attachmentBase64,
          expense.attachmentName
        ]);
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
      this.fileLogger.info('Insertion of current expenses finished');
    } catch (error: any) {
      this.fileLogger.error('Error in INSERT_NOTEFRAIS: ' + error.message);
    }
  }

  async deleteAndInsertSpecialities(specialities: any[]): Promise<void> {
    const insertQuery = this.queries.INSERT_SPECIALITY;
    await this.deleteAndInsert('speciality', specialities, insertQuery);
  }

  private async deleteAndInsertLocalities(localities: any[]): Promise<void> {
    this.fileLogger.info("Start updating localities");
    try {
      await this.mDb.query("DELETE FROM locality");
      for (const locality of localities) {
        await this.mDb.run(this.queries.INSERT_LOCALITY, [
          locality.id, 
          locality.name, 
          locality.sectorId
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error:any) {
      this.fileLogger.info("Error in updating localities: " + error.message);
    }
  }

  private async deleteAndInsertUsers(users: any[]): Promise<void> {
    this.fileLogger.info("Start updating users' names");
    try {
      await this.mDb.query("DELETE FROM users");
      for (const user of users) {
        await this.mDb.run(this.queries.INSERT_USERS, [
          user.id, 
          `${user.firstName} ${user.lastName}`, 
          user.delegateId
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error:any) {
      this.fileLogger.info("Error in updating users: " + error.message);
    }
  }

  private async deleteAndInsertGadgets(gadgets: any[]): Promise<void> {
    this.fileLogger.info("Start updating gadgets");
    try {
      await this.mDb.query("DELETE FROM gadget");
      for (const gadget of gadgets) {
        await this.mDb.run(this.queries.INSERT_GADGET, [
          gadget.id, 
          gadget.name, 
          gadget.type
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error:any) {
      this.fileLogger.info("Error in updating gadgets: " + error.message);
    }
  }


  async deleteAndInsertEstablishments(establishments: any[]): Promise<void> {
    const insertQuery = this.queries.INSERT_ESTABLISHMENT;
    await this.deleteAndInsert('establishment', establishments, insertQuery);
  }

  private async deleteAndInsertNotes(notes: any[]): Promise<void> {
    this.fileLogger.info("Insertion of notes started");
    try {
      await this.mDb.query("DELETE FROM note");
      for (const note of notes) {
        const sectors = note.sectors.map((s: any) => s.id).join(',');
        const specialities = note.specialities.map((s: any) => s.id).join(',');
        const activities = note.activity.join(',');
        await this.mDb.run(this.queries.INSERT_NOTES, [
          note.id, 
          sectors, 
          specialities, 
          activities, 
          note.note, 
          note.link
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      this.fileLogger.info("Insertion notes finished");
    } catch (error:any) {
      this.fileLogger.info("Error in handling notes: " + error.message);
    }
  }
  async deleteAndInsertNextActionRules(nextActionRules: any[]): Promise<void> {
    this.fileLogger.info("Start updating next action rules");
    try {
      await this.mDb.run(this.queries.DELETE_ALL_NEXT_ACTION_RULES);
      for (const rule of nextActionRules) {
        await this.mDb.run(this.queries.INSERT_NEXT_ACTION_RULE, [
          rule.totalRevenue,
          rule.period,
          rule.action
        ]);
      }
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      this.fileLogger.info("Next action rules updated successfully");
    } catch (error: any) {
      this.fileLogger.error("Error in updating next action rules: " + error.message);
    }
  }
  
 private async deleteAndInsertUserPermissions(permissions: string[], userId: number): Promise<void> {
   this.fileLogger.info("Start updating user permissions");
   try {
     // Supprimer toutes les permissions existantes
     await this.mDb.run(this.queries.DELETE_ALL_USER_PERMISSIONS);
 
     // Insérer les nouvelles permissions
     for (const permission of permissions) {
       await this.mDb.run(this.queries.INSERT_USER_PERMISSION, [permission, userId]);
     }
 
     if (this.sqliteService.platform === 'web') {
       await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
     }
 
     this.fileLogger.info(`User permissions updated successfully: ${permissions.length} permissions`);
   } catch (error: any) {
     this.fileLogger.error("Error in updating user permissions: " + error.message);
   }
 }
 private async deleteAndInsertContactTypes(contactTypes: any[]): Promise<void> {
  this.fileLogger.info("Start updating contact types")
  try {
    await this.mDb.run(this.queries.DELETE_ALL_CONTACT_TYPES)

    for (const contactType of contactTypes) {
      await this.mDb.run(this.queries.INSERT_CONTACT_TYPE, [
        contactType.id, 
        contactType.name, 
        null,  // action
        null   // icon
      ])
    }

    if (this.sqliteService.platform === "web") {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName)
    }

    this.fileLogger.info(`Contact types updated successfully: ${contactTypes.length} contact types`)
  } catch (error: any) {
    this.fileLogger.error("Error in updating contact types: " + error.message)
  }
}
private async deleteAndInsertPreferences(preferences: any[]): Promise<void> {
  this.fileLogger.info("Start updating preferences")
  try {
    await this.mDb.run(this.queries.DELETE_ALL_PREFERENCES)

    for (const preference of preferences) {
      await this.mDb.run(this.queries.INSERT_PREFERENCE, [preference.id, preference.name])
    }

    if (this.sqliteService.platform === "web") {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName)
    }

    this.fileLogger.info(`Preferences updated successfully: ${preferences.length} preferences`)
  } catch (error: any) {
    this.fileLogger.error("Error in updating preferences: " + error.message)
  }
}
async getPreferences(): Promise<any[]> {
  try {
    const result = await this.mDb.query(this.queries.SELECT_PREFERENCES)
    return (result.values || []).map((row) => ({
      id: row.id,
      name: row.name,
    }))
  } catch (error) {
    console.error("Error getting preferences:", error)
    return []
  }
}

/**
 * Récupère les centres d'intérêt locaux
 */
async getInterests(): Promise<any[]> {
  try {
    const result = await this.mDb.query(this.queries.SELECT_INTERESTS)
    return (result.values || []).map((row) => ({
      id: row.id,
      name: row.name,
    }))
  } catch (error) {
    console.error("Error getting interests:", error)
    return []
  }
}

/**
 * Récupère les types de contact locaux
 */
async getContactTypes(): Promise<any[]> {
  try {
    const result = await this.mDb.query(this.queries.SELECT_CONTACT_TYPES)
    return (result.values || []).map((row) => ({
      id: row.id,
      name: row.name,
      action: row.action,
      icon: row.icon,
    }))
  } catch (error) {
    console.error("Error getting contact types:", error)
    return []
  }
}
private async deleteAndInsertInterests(interests: any[]): Promise<void> {
  this.fileLogger.info("Start updating interests")
  try {
    await this.mDb.run(this.queries.DELETE_ALL_INTERESTS)

    for (const interest of interests) {
      await this.mDb.run(this.queries.INSERT_INTEREST, [interest.id, interest.name])
    }

    if (this.sqliteService.platform === "web") {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName)
    }

    this.fileLogger.info(`Interests updated successfully: ${interests.length} interests`)
  } catch (error: any) {
    this.fileLogger.error("Error in updating interests: " + error.message)
  }
}

 
 /**
  * Traite les permissions utilisateur reçues du serveur
  */
 private async handleUserPermissions(permissions: string[]): Promise<void> {
   try {
     console.log('Processing user permissions:', permissions);
     
     // Récupérer l'ID utilisateur actuel
     const currentUser = await this.commonService.getCurrentUser();
     const userId = currentUser?.user_id;
 
     if (!userId) {
       console.warn('No current user found, skipping permissions update');
       return;
     }
 
     // Sauvegarder les permissions dans la base locale
     await this.deleteAndInsertUserPermissions(permissions, userId);
     
     console.log(`Successfully saved ${permissions.length} permissions for user ${userId}`);
   } catch (error) {
     console.error('Error handling user permissions:', error);
   }
 }
 
 /**
  * Vérifie si l'utilisateur a une permission spécifique
  */
 async hasPermission(userId: number, permission: string): Promise<boolean> {
   try {
     const result = await this.mDb.query(this.queries.CHECK_USER_PERMISSION, [userId, permission]);
     return result.values?.[0]?.count > 0;
   } catch (error) {
     console.error('Error checking permission:', error);
     return false;
   }
 }
 
 /**
  * Récupère les permissions de l'utilisateur
  */
 async getUserPermissions(userId: number): Promise<string[]> {
   try {
     const result = await this.mDb.query(this.queries.SELECT_USER_PERMISSIONS, [userId]);
     return (result.values || []).map(row => row.permission_name);
   } catch (error) {
     console.error('Error getting user permissions:', error);
     return [];
   }
 }
 async getCurrentUserPermissions(): Promise<string[]> {
  try {
    const currentUser = await this.commonService.getCurrentUser();
    const userId = currentUser?.user_id;

    if (!userId) {
      return [];
    }

    return await this.getUserPermissions(userId);
  } catch (error) {
    console.error('Error getting current user permissions:', error);
    return [];
  }
}

/**
 * Vérifie si l'utilisateur actuel a une permission spécifique
 */
async currentUserHasPermission(permission: string): Promise<boolean> {
  try {
    const currentUser = await this.commonService.getCurrentUser();
    const userId = currentUser?.user_id;

    if (!userId) {
      return false;
    }

    return await this.hasPermission(userId, permission);
  } catch (error) {
    console.error('Error checking current user permission:', error);
    return false;
  }
}

// Ajouter ces méthodes dans sync.service.ts

/**
 * Supprime et insère les allocations budgétaires
 */
private async deleteAndInsertBudgetAllocations(budgetAllocations: any[]): Promise<void> {
  this.fileLogger.info("Start updating budget allocations");
  try {
    // Supprimer toutes les allocations existantes
    await this.mDb.run(this.queries.DELETE_ALL_BUDGET_ALLOCATIONS);
    
    // Insérer les nouvelles allocations
    for (const allocation of budgetAllocations) {
      await this.mDb.run(this.queries.INSERT_BUDGET_ALLOCATION, [
        allocation.id,
        allocation.year,
        allocation.monthlyBudget,
        allocation.type
      ]);
    }
    
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
    
    this.fileLogger.info(`Budget allocations updated successfully: ${budgetAllocations.length} allocations`);
  } catch (error: any) {
    this.fileLogger.error("Error in updating budget allocations: " + error.message);
  }
}

/**
 * Récupère les allocations budgétaires locales
 */
async getBudgetAllocations(): Promise<BudgetAllocation[]> {
  try {
    const result = await this.mDb.query(this.queries.SELECT_BUDGET_ALLOCATIONS);
    return (result.values || []).map(row => ({
      id: row.id,
      year: row.year,
      monthlyBudget: row.monthlyBudget,
      type: row.type
    }));
  } catch (error) {
    console.error('Error getting budget allocations:', error);
    return [];
  }
}

/**
 * Récupère les allocations budgétaires par année
 */
async getBudgetAllocationsByYear(year: number): Promise<BudgetAllocation[]> {
  try {
    const result = await this.mDb.query(this.queries.SELECT_BUDGET_ALLOCATION_BY_YEAR, [year]);
    return (result.values || []).map(row => ({
      id: row.id,
      year: row.year,
      monthlyBudget: row.monthlyBudget,
      type: row.type
    }));
  } catch (error) {
    console.error('Error getting budget allocations by year:', error);
    return [];
  }
}


  private async deleteAndInsertRecords(tableName: string, records: any[]): Promise<void> {
    try {
      console.info(`Deleting records from ${tableName}`);
      await this.mDb.run(`DELETE FROM ${tableName}`);
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      console.info(`Fetching schema for ${tableName}`);
      const schemaResult = await this.mDb.query(`PRAGMA table_info(${tableName})`);
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      if (!schemaResult.values) {
        throw new Error(`Failed to retrieve schema for table ${tableName}`);
      }
  
      const validColumns = schemaResult.values.map((col: any) => col.name);
  
      console.info(`Inserting records into ${tableName}`);
      for (const record of records) {
        const keys = Object.keys(record).filter(key => validColumns.includes(key));
        const values = keys.map(key => record[key]);
        const placeholders = keys.map(() => '?').join(', ');
  
        const query = `INSERT INTO ${tableName} (${keys.join(', ')}) VALUES (${placeholders})`;
        await this.mDb.run(query, values);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error(`Error in deleteAndInsertRecords for ${tableName}:`, error);
    }
  }
  
  

  async insertNotifications(notifications: any[]): Promise<void> {
    this.fileLogger.info('Insertion of notifications started');
    
    try {
      for (const notification of notifications) {
        await this.mDb.run(this.queries.INSERT_NOTIFICATIONS, [
          notification.id,
          notification.text,
          notification.creationDay,
          notification.creationMonth,
          notification.creationYear,
          notification.creationHour,
          notification.creationMinutes,
          notification.status
        ]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error: any) {
      this.fileLogger.error(`Error in inserting notifications: ${error.message}`);
    }
  }

  async updateUserConfig(config: any): Promise<void> {
    this.fileLogger.info('Insertion of configuration in table user started');
    try {
      if (config) {
        await this.mDb.query(this.queries.UPDATE_USER_CONFIG, [
          config.autoSync,
          config.lockAfterSync,
          config.multiWholesaler,
          config.syncCycle,
          config.commentsDictionary,
          config.openReportPeriod,
          config.workType,
          config.openExpensePeriod,
          config.workingDays
        ]);
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
      const res = await this.mDb.query("select * from user");
      console.log(res.values);
    } catch (error: any) {
      this.fileLogger.error(`Error in inserting user config: ${error.message}`);
    }
  }

  private async insertDoubleVisits(doubleVisits: any[]): Promise<void> {
    this.fileLogger.info("Insertion of double visit in table visit started");
    try {
      for (const visit of doubleVisits) {
        const visit_date = new Date(visit.visitDate).setHours(0, 0, 0, 0);
        await this.mDb.run(this.queries.INSERT_VISIT, [
          visit.identifier,
          visit_date,
          visit.prospectId,
          visit.generalNote,
          visit.patientNumber,
          visit.gadgetId,
          visit.gadgetQuantity,
          visit.userId,
          visit.userName,
          visit.companionId
        ]);
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
    } catch (error:any) {
      this.fileLogger.info("Error in double visit: " + error.message);
    }
  }
  private async insertVisitProducts(visitsProducts: any[]): Promise<void> {
    this.fileLogger.info('Insertion of visit products started');
    try {
      for (const product of visitsProducts) {
        await this.mDb.run(this.queries.INSERT_VISIT_PRODUCT, [
          product.identifier,
          product.visitId,
          product.productId,
          product.comment,
          product.sampleQuantity,
          product.orderQuantity,
          product.rank,
          product.smily,
          product.saleQuantity,
          product.urgent,
          product.prescriptionQuantity,
          product.freeOrder,
          product.labGratuity,
          product.purchaseOrderId,
        ]);
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
      this.fileLogger.info('Insertion of visit products finished');
    } catch (error: any) {
      this.fileLogger.error('Error in INSERT_VISIT_PRODUCT: ' + error.message);
    }
  }
  

  private async insertDoubleVisitProducts(doubleVisitProducts: any[]): Promise<void> {
    this.fileLogger.info("Insertion of double visits products in table visitProduct started");
    try {
      for (const product of doubleVisitProducts) {
        await this.mDb.run(this.queries.INSERT_VISIT_PRODUCT, [
          product.identifier,
          product.visitId,
          product.productId,
          product.comment,
          product.sampleQuantity,
          product.orderQuantity,
          product.rank,
          product.smily,
          product.saleQuantity,
          product.urgent,
          product.prescriptionQuantity,
          product.freeOrder,
          product.labGratuity,
          product.purchaseOrderId
        ]);
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
    } catch (error:any) {
      this.fileLogger.info("Error in double visit product: " + error.message);
    }
  }
  private async insertVisits(visits: any[]): Promise<void> {
    this.fileLogger.info('Insertion of visit started');
    try {
      for (const visit of visits) {
        // Convert visitDate to a date object and set time to 00:00:00
        const visitDate = new Date(visit.visitDate).setHours(0, 0, 0, 0);
        
        // Execute the insert operation
        await this.mDb.run(this.queries.INSERT_VISIT, [
          visit.identifier,
          visitDate,
          visit.prospectId,
          visit.generalNote,
          visit.patientNumber,
          visit.gadgetId,
          visit.gadgetQuantity,
          visit.userId,
          visit.userName,
          visit.companionId
        ]);
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
    } catch (error: any) {
      this.fileLogger.error('Error in INSERT_VISIT ' + error.message);
    }
  }
  
  async sendRequest(data: any): Promise<any> {
    try {
      // Await the token before using it
      const token = await this.LoginService.getToken();
      
      const headers = new HttpHeaders({
        'Authorization': `${token}`,  // Use the resolved token
        'Content-Type': 'application/json'
      });
  
      const apiUrl = await this.getApiUrl("/synchronise/receive")
      const response = await this.http.post(apiUrl, data, { headers }).toPromise()
      
      console.log('Response:', response);
      return response;
     
    } catch (error) {
      console.error('Error in sendRequest:', error);
    }
  }
  
  async receive() {
    if (this.isSyncronizing) return;
    
    this.isSyncronizing = true;
    
   

    try {
      const existingProducts = await this.getExistingProducts();
      const existingPlanningIds = await this.getExistingPlannings();
      const existingProspects = await this.getExistingProspects();
      const planningWaitingValidation = await this.getPlanningWaitingValidation();
      const expenseWaitingValidation = await this.getExpenseWaitingValidation();
      const maxPredictionDate = await this.getMaxPredictionDate();
      const lastReceiveDate = await this.getLastReceiveDate();
      const firstSync = await this.getFirstSync();
      const currentWeekDate = this.DateService.getMondayOfWeek();
      this.syncStateService.setReceivingState(true, 'Réception des données en cours...');
      const synchronisationReceiveInput = {
        existingProspects,
        existingPlannings: existingPlanningIds,
        newProspectWaitingForValidation: [],
        updatedProspectWaitingForValidation: [],
        planningWaitingValidation,
        expenseWaitingValidation,
        existingProducts,
        maxPredictionDate,
        getOrderPrediction: maxPredictionDate !== null && maxPredictionDate < currentWeekDate,
        lastNotificationId: 0,
        lastReceiveDate,
        firstSync,
        currentWeekDate
      };

      const response: any = await this.sendRequest(synchronisationReceiveInput);
      console.log("SERVER RESPONSE", response)
      await this.handleResponse(response);
     
      
    } catch (error) {
      console.error('Error in receive function', error);
      this.syncStateService.setReceivingState(false, 'Erreur de synchronisation');
      throw error;
    } finally {
      this.isSyncronizing = false;
    }
  }

  async getExistingProducts(): Promise<any[]> {
    const result = await this.mDb.query('SELECT * FROM product'); // Adjust the query according to your schema
    return result.values ?? [];
  }

  async getExistingPlannings(): Promise<any[]> {
    const result = await this.mDb.query('SELECT * FROM planning WHERE planning_date >= ?', [this.DateService.getMondayOfWeek().getTime()]);
    return result.values ?? [];
  }

  async getExistingProspects(): Promise<any[]> {
    const result = await this.mDb.query('SELECT * FROM prospect');
    const existingProspects: any[] = [];
    // Process result.values to populate existingProspects
    return existingProspects;
  }

  async getPlanningWaitingValidation(): Promise<any[]> {
    const result = await this.mDb.query('SELECT * FROM planning_validation');
    if (result.values) {
      return result.values.map(row => row.id);
    } else {
      return [];
    }
  }

  async getExpenseWaitingValidation(): Promise<any[]> {
    const result = await this.mDb.query('SELECT * FROM expense');
    if (result.values) {
      return result.values.map(row => row.id);
    } else {
      return [];
    }
  }

  async getMaxPredictionDate(): Promise<Date | null> {
    const result = await this.mDb.query('SELECT MAX(prediction_date) as maxPredictionDate FROM prospect_order_prediction');
    const maxPredictionDate = result.values?.[0]?.maxPredictionDate;
    return maxPredictionDate ? new Date(maxPredictionDate) : null;
  }

  async getLastReceiveDate(): Promise<Date | null> {
    const result = await this.mDb.query('SELECT last_receive_date FROM user');
    const lastReceiveDate = result.values?.[0]?.last_receive_date;
    return lastReceiveDate ? new Date(lastReceiveDate) : null;
  }

  async getFirstSync(): Promise<boolean> {
    const result = await this.mDb.query('SELECT first_sync FROM user');
    return result.values?.[0]?.first_sync === 0;
  }

  async handleResponse(response: any) {
    try {
      if (response?.interests?.length) {
        console.log("Processing interests:", response.interests.length)
        await this.deleteAndInsertInterests(response.interests)
      }

      if (response?.contactTypes?.length) {
        console.log("Processing contact types:", response.contactTypes.length)
        await this.deleteAndInsertContactTypes(response.contactTypes)
      }

      if (response?.preferences?.length) {
        console.log("Processing preferences:", response.preferences.length)
        await this.deleteAndInsertPreferences(response.preferences)
      }
      // Handle new prospects
      if (response?.prospects?.length) {
        for (const prospect of response.prospects) {
          await this.mDb.run(
            `INSERT OR IGNORE INTO prospect (
                id, firstname, lastname, activity, potential, address, gsm, phone, email, note, secretary, grade, 
                speciality_id, sector_id, locality_id, lat, lng, map_address, status, synchronized, validation, 
                type_id, establishment_id, fiscal_number
            ) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?)`,
            [
                prospect.identifier, 
                prospect.firstName, 
                prospect.lastName, 
                prospect.activity, 
                prospect.potentialDto?.id, 
                prospect.address, 
                prospect.gsm, 
                prospect.phone, 
                prospect.email, 
                prospect.note, 
                prospect.secretary, 
                prospect.grade, 
                prospect.specialityDto?.id, 
                prospect.sectorDto?.id, 
                prospect.localityDto?.id, 
                prospect.latitude, 
                prospect.longitude,  // Correctly added lng here
                prospect.mapAddress, 
                prospect.status, 
                1, // Assuming 1 for 'synchronized' as in your previous code
                1, // Assuming 1 for 'validation'
                prospect.prospectTypeDto?.id, 
                prospect.establishmentDto?.id,
                prospect.taxIdNumber || null,
            ]
        );
        if (prospect.interestIds && prospect.interestIds.length > 0) {
          for (const interestId of prospect.interestIds) {
            try {
              await this.mDb.run(`INSERT OR IGNORE INTO prospect_interest (prospect_id, interest_id) VALUES (?, ?)`, [
                prospect.identifier,
                interestId,
              ])
            } catch (error: any) {
              this.fileLogger.error(`Error inserting prospect interest: ${error.message}`)
            }
          }
        }

        // Insert prospect contact types using contactTypeIds array
        if (prospect.contactTypeIds && prospect.contactTypeIds.length > 0) {
          for (const contactTypeId of prospect.contactTypeIds) {
            try {
              await this.mDb.run(
                `INSERT OR IGNORE INTO prospect_contact_type (prospect_id, contact_type_id) VALUES (?, ?)`,
                [prospect.identifier, contactTypeId],
              )
            } catch (error: any) {
              this.fileLogger.error(`Error inserting prospect contact type: ${error.message}`)
            }
          }
        }

        // Insert prospect preferences using preferenceIds array
        if (prospect.preferenceIds && prospect.preferenceIds.length > 0) {
          for (const preferenceId of prospect.preferenceIds) {
            try {
              await this.mDb.run(
                `INSERT OR IGNORE INTO prospect_preference (prospect_id, preference_id) VALUES (?, ?)`,
                [prospect.identifier, preferenceId],
              )
            } catch (error: any) {
              this.fileLogger.error(`Error inserting prospect preference: ${error.message}`)
            }
          }
        }
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
        }
      }

      const res = await this.mDb.query("select * from prospect");
      console.log(res.values);
  
      // Handle prospect status updates
      if (response?.prospectsValidationResponse?.length) {
        for (const prospectStatus of response.prospectsValidationResponse) {
          await this.mDb.query(
            'UPDATE prospect SET status = ? WHERE id = ?', 
            [prospectStatus.status, prospectStatus.id]
          );

        }
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
  
      // Handle merged prospects
      if (response?.mergedProspects?.length) {
        for (const merged of response.mergedProspects) {
          await this.mDb.query(
            'UPDATE report SET prospect_id = ? WHERE prospect_id = ?', 
            [merged.newId, merged.oldId]
          );
          await this.mDb.query(
            'UPDATE marketing_action SET prospect_id = ? WHERE prospect_id = ?', 
            [merged.newId, merged.oldId]
          );
          await this.mDb.query(
            'UPDATE planning SET prospect_id = ? WHERE prospect_id = ?', 
            [merged.newId, merged.oldId]
          );
          await this.mDb.query(
            'UPDATE opportunity_note SET prospect_id = ? WHERE prospect_id = ?', 
            [merged.newId, merged.oldId]
          );

          await this.mDb.query("UPDATE prospect_interest SET prospect_id = ? WHERE prospect_id = ?", [
            merged.newId,
            merged.oldId,
          ])
          await this.mDb.query("UPDATE prospect_contact_type SET prospect_id = ? WHERE prospect_id = ?", [
            merged.newId,
            merged.oldId,
          ])
          await this.mDb.query("UPDATE prospect_preference SET prospect_id = ? WHERE prospect_id = ?", [
            merged.newId,
            merged.oldId,
          ])

          await this.mDb.query(
            'DELETE FROM prospect WHERE id = ?', 
            [merged.oldId]
          );
          if(this.sqliteService.platform === 'web') {
            await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
          }
        }
      }
  
      // Handle deleted prospects
      if (response?.deletedProspects?.length) {
        const deletedProspectsIds = response.deletedProspects.join(',');
  
        await this.mDb.query(
          `UPDATE prospect SET status = 'NOT_AFFECTED' WHERE id IN (${deletedProspectsIds})`
        );
  
        const visitsToDeleteResult = await this.mDb.query(
          `SELECT id FROM visit WHERE prospect_id IN (${deletedProspectsIds}) AND synchronized = 1`
        );
        const visitIds = visitsToDeleteResult.values?.map(row => row.id) ?? [];
  
        if (visitIds.length > 0) {
          const poToDeleteResult = await this.mDb.query(
            `SELECT id FROM purchase_order WHERE visit_id IN (${visitIds.join(',')}) AND synchronized = 1`
          );
          const poIds = poToDeleteResult.values?.map(row => row.id) ?? [];
  
          if (poIds.length > 0) {
            const attachmentToDeleteResult = await this.mDb.query(
              `SELECT id FROM attachment WHERE purchase_order_id IN (${poIds.join(',')})`
            );
            const attachmentIds = attachmentToDeleteResult.values?.map(row => row.id) ?? [];
  
            await this.mDb.query(
              `DELETE FROM visit WHERE id IN (${visitIds.join(',')})`
            );
            await this.mDb.query(
              `DELETE FROM purchase_order WHERE visit_id IN (${visitIds.join(',')})`
            );
            await this.mDb.query(
              `DELETE FROM attachment WHERE id IN (${attachmentIds.join(',')})`
            );
          }
        }
        await this.mDb.query(`DELETE FROM prospect_interest WHERE prospect_id IN (${deletedProspectsIds})`)
        await this.mDb.query(`DELETE FROM prospect_contact_type WHERE prospect_id IN (${deletedProspectsIds})`)
        await this.mDb.query(`DELETE FROM prospect_preference WHERE prospect_id IN (${deletedProspectsIds})`)
        await this.mDb.query(
          `DELETE FROM prospect WHERE id IN (${deletedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM visit WHERE prospect_id IN (${deletedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM planning WHERE prospectId IN (${deletedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM marketing_action WHERE prospect_id IN (${deletedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM opportunitynote WHERE prospect_id IN (${deletedProspectsIds})`
        );
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
  
      // Handle refused prospects
      if (response?.refusedProspects?.length) {
        const refusedProspectsIds = response.refusedProspects.join(',');
  
        const visitsToDeleteResult = await this.mDb.query(
          `SELECT id FROM visit WHERE prospect_id IN (${refusedProspectsIds}) AND synchronized = 1`
        );
        const visitIds = visitsToDeleteResult.values?.map(row => row.id) ?? [];
  
        if (visitIds.length > 0) {
          const poToDeleteResult = await this.mDb.query(
            `SELECT id FROM purchase_order WHERE visit_id IN (${visitIds.join(',')}) AND synchronized = 1`
          );
          const poIds = poToDeleteResult.values?.map(row => row.id) ?? [];
          if (poIds.length > 0) {
            const attachmentToDeleteResult = await this.mDb.query(
              `SELECT id FROM attachment WHERE purchase_order_id IN (${poIds.join(',')})`
            );
            const attachmentIds = attachmentToDeleteResult.values?.map(row => row.id) ?? [];
  
            await this.mDb.query(
              `DELETE FROM visit WHERE id IN (${visitIds.join(',')})`
            );
            await this.mDb.query(
              `DELETE FROM purchase_order WHERE visit_id IN (${visitIds.join(',')})`
            );
            await this.mDb.query(
              `DELETE FROM attachment WHERE id IN (${attachmentIds.join(',')})`
            );
          }
        }
  
        await this.mDb.query(
          `DELETE FROM prospect WHERE id IN (${refusedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM visit WHERE prospectId IN (${refusedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM planning WHERE prospectId IN (${refusedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM marketing_action WHERE prospect_id IN (${refusedProspectsIds})`
        );
        await this.mDb.query(
          `DELETE FROM opportunitynote WHERE prospect_id IN (${refusedProspectsIds})`
        );
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
  
      // Handle deleted plannings
      if (response?.deletedPlannings?.length) {
        const deletedPlanningIds = response.deletedPlannings.join(',');
        await this.mDb.query(
          `DELETE FROM planning WHERE id IN (${deletedPlanningIds})`
        );
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      }
      // Traitement des permissions utilisateur
    if (response.userPermissions?.length) {
      console.log('Handling user permissions:', response.userPermissions);
      await this.handleUserPermissions(response.userPermissions);
    }
  
      // Handle deleted planning validations
      if (response?.deletedPlanningValidations?.length) {
        const deletedPlanningValidationIds = response.deletedPlanningValidations.join(',');
        await this.mDb.query(
          `DELETE FROM planning_validation WHERE id IN (${deletedPlanningValidationIds})`
        );
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }
      }
      this.insertOtherData(response)
  
    } catch (error) {
      console.error('Error in handleResponse:', error);
      throw error;
    }
  }
  
  async updatePlanningValidation(planningValidationResponse: any[]): Promise<void> {
    try {
      console.info("Updating planning validation");
      for (const item of planningValidationResponse) {
        const timestamp = new Date(item.date).setHours(0, 0, 0, 0);
        await this.mDb.run(this.queries.UPDATE_PLANNING_VALIDATION, 
          [item.notes, item.status, timestamp, item.identifier]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error('Error in UPDATE_PLANNING_VALIDATION:', error);
    }
  }

  // Update expense validation
  async updateExpenseStatus(expenseValidationResponse: any[]): Promise<void> {
    try {
      console.info("Updating expense validation");
      for (const item of expenseValidationResponse) {
        await this.mDb.run(`UPDATE expense SET status = ? WHERE identifier = ?`, 
          [item.status, item.identifier]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error('Error in UPDATE_EXPENSE_STATUS:', error);
    }
  }

  // Update marketing action
  async updateMarketingAction(marketingActionValidationResponse: any[]): Promise<void> {
    try {
      console.info("Updating marketing action");
      for (const item of marketingActionValidationResponse) {
        await this.mDb.run(this.queries.UPDATE_ACTIONMARKETING_STATUS, 
          [item.status, item.identifier]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error('Error in UPDATE_ACTIONMARKETING_STATUS:', error);
    }
  }

  // Update opportunity note
  async updateOpportunityNotes(opportunityNoteValidationResponse: any[]): Promise<void> {
    try {
      console.info("Updating opportunity note validation");
      for (const item of opportunityNoteValidationResponse) {
        await this.mDb.run(this.queries.UPDATE_OPPORTUNITYNOTEN, 
          [item.status, item.id]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error('Error in UPDATE_OPPORTUNITYNOTEN:', error);
    }
  }

  // Insert products
  async insertProduct(products: any[]): Promise<void> {
    try {
      console.info("Insertion of products started");
      for (const product of products) {
        console.log("product", product);
        await this.mDb.run(this.queries.INSERT_PRODUCT,
          [product.id, product.name, product.price, product.buyingPrice, product.version, product.quantityUnit, product.numberOfCapsules, product.stock, product.description]);
        
        // Handle product ranges
        await this.mDb.run(this.queries.DELETE_PRODUCT_RANGE, [product.id]);
        for (const range of product.ranges) {
          await this.mDb.run(this.queries.INSERT_PRODUCT_RANGE, 
            [range.id, product.id]);
        }

        // Handle product documents
        if (product.documentDtoList.length > 0) {
          console.log("PRODUCT DOCUMENTS IS HERE");
          await this.mDb.run(this.queries.DELETE_PRODUCT_DOCUMENTS, [product.id]);
          for (const doc of product.documentDtoList) {
            console.log(doc);
            await this.mDb.run(this.queries.INSERT_PRODUCT_DOCUMENTS, 
              [doc.name, doc.content, product.id]);
          }
        }
        if(this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        }

      }
      console.info("Insertion of products finished");
    } catch (error) {
      console.error('Error in INSERT_PRODUCT:', error);
    }
  }

  // Delete products
  async deleteProductById(deletedProducts: any[]): Promise<void> {
    try {
      console.info("Deletion of products started");
      for (const id of deletedProducts) {
        await this.mDb.run(`DELETE FROM product WHERE id = ?`, [id]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      console.info("Deletion of products finished");
    } catch (error) {
      console.error('Error in DELETE_PRODUCT_BY_ID:', error);
    }
  }

  // Insert other data
  async insertOtherData(response: any): Promise<void> {
    
    try {
      // Implement the logic for other insertions and deletions
      await this.deleteAndInsertWholeSalers(response.wholesalers);
      await this.deleteAndInsertWholesalers(response.wholesalers);
      await this.deleteAndInsertPotentialProducts(response.potentialProducts);
      await this.deleteAndInsertChargePlan(response.chargePlan);
      await this.deleteAndInsertGoalItems(response.goalItems);
      await this.deleteAndInsertGoals(response.goals);
      await this.deleteAndInsertPurchaseOrderTemplates(response.purchaseOrderTemplates);
      await this.deleteAndInsertPurchaseOrderTemplateItems(response.purchaseOrderTemplates);
      await this.deleteAndInsertFreeQuantityRuleItems(response.freeQuantityRuleItems);
      await this.deleteAndInsertExpenseTypes(response.expenseTypes);
      await this.insertCurrentExpenses(response.expenses)
      await this.deleteAndInsertSpecialities(response.specialities);
      await this.deleteAndInsertLocalities(response.localities);
      await this.insertPlannings(response.plannings)
      await this.insertPlanningValidations(response.planningValidationResponse)
      await this.deleteAndInsertUsers(response.users);
      await this.deleteAndInsertGadgets(response.gadgets);
      await this.deleteAndInsertEstablishments(response.establishments);
      await this.deleteAndInsertNotes(response.notes);
      await this.insertNotifications(response.notifications);
      await this.updateUserConfig(response.config);
      await this.insertDoubleVisits(response.doubleVisits);
      await this.insertDoubleVisitProducts(response.doubleVisitProducts);
      await this.insertVisitProducts(response.visitsProducts)
      await this.insertVisits(response.visits)
      await this.deleteAndInsertRecords('sector', response.sectors);
      await this.deleteAndInsertRecords('range', response.ranges);
      await this.deleteAndInsertRecords('activity_type', response.activityTypes);
      await this.deleteAndInsertRecords('potential', response.potential);
      await this.deleteAndInsertRecords('prospect_type', response.prospectType);
      await this.deleteAndInsertNextActionRules(response.nextActionRules);
      await this.insertProspectOrderPrediction(response.prospectsOrderPrediction);
      await this.insertProduct(response.products);
      await this.deleteAndInsertBudgetAllocations(response.budgetAllocations);
   
      
    } catch (error) {
      console.error('Error in insertOtherData:', error);
    }
    finally{
      this.syncStateService.setReceivingState(false, 'Synchronisation terminée');
    }
  }

  // Insert prospect order prediction
  async insertProspectOrderPrediction(prospectsOrderPrediction: any[]): Promise<void> {
    try {
      console.info("Inserting prospect order predictions");
      for (const item of prospectsOrderPrediction) {
        const predictionDate = new Date(item.predictionDate);
        await this.mDb.run(this.queries.INSERT_PROSPECT_ORDER_PREDICTION, 
          [item.id, predictionDate.getTime(), item.prospectId, item.productId, item.orderQuantity]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error('Error in INSERT_PROSPECT_ORDER_PREDICTION:', error);
    }
  }

  // Example for deleteAndInsert methods
  private async deleteAndInsertWholesalers(wholesalers: any[]): Promise<void> {
    try {
      await this.mDb.run(`DELETE FROM wholesaler`);
      for (const wholesaler of wholesalers) {
        await this.mDb.run(this.queries.INSERT_WHOLESALER, 
          [wholesaler.id, wholesaler.name,wholesaler.responsible, wholesaler.phone,wholesaler.address,wholesaler.description]);

      }
      if(this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error('Error in deleteAndInsertWholesalers:', error);
    }
  }


//
// 1) Tagged Users
//
async getTaggedUsersToSend(): Promise<{ identifier: number; visitId: number; userId: number }[]> {
  const res = await this.mDb.query(this.queries.SELECT_TAGED_USER_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier: r.id,
    visitId:   r.visit_id,
    userId:    r.user_id
  }));
}

//
// 2) Visits
//
async getVisitsToSend(userId: number): Promise<{
  identifier: number;
  day: Date;
  visitDate: Date;
  prospectId: number;
  generalNote: string;
  patientNumber: number;
  gadgetId: number;
  gadgetQuantity: number;
  freeOrder: number;
  labGratuity: number;
  companionId: number;
  lat?: number;
  lng?: number;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_VISIT_TO_SEND, [userId]);
  return (res.values || []).map(r => {
    const d = new Date(r.visit_date);
    return {
      identifier:     r.id,
      day:            d,
      visitDate:      d,
      prospectId:     r.prospect_id,
      generalNote:    r.general_note,
      patientNumber:  r.patient_number,
      gadgetId:       r.gadget_id,
      gadgetQuantity: r.gadget_quantity,
      freeOrder:      r.freeOrder,
      labGratuity:    r.lab_gratuity,
      companionId:    r.companion_id,
      lat:            r.lat   ?? undefined,
      lng:            r.lng   ?? undefined,
    };
  });
}

//
// 3) Time Tracking
//
async getTimeTrackingToSend(): Promise<{
  identifier: number;
  startTime: Date;
  endTime: Date;
  documentId: number;
  visitId: number;
  productId: number;
  documentName: string;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_TIME_TRACKING_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier:   r.id,
    startTime:    new Date(r.start_time),
    endTime:      new Date(r.end_time),
    documentId:   r.product_document_id,
    visitId:      r.visit_id,
    productId:    r.product_id,
    documentName: r.documentName
  }));
}

//
// 4) Visit Products
//
async getVisitProductsToSend(userId: number): Promise<{
  identifier: number;
  productId: number;
  visitId: number;
  comment: string;
  orderQuantity: number;
  sampleQuantity: number;
  saleQuantity: number;
  purchaseOrderId: number;
  rank: number;
  smily: number;
  urgent: boolean;
  prescriptionQuantity: number;
  freeOrder: number;
  labTratuity: number;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_VISIT_PRODUCT_TO_SEND, [userId]);
  return (res.values || []).map(r => ({
    identifier:           r.id,
    productId:            r.product_id,
    visitId:              r.visit_id,
    comment:              r.comment,
    orderQuantity:        r.order_quantity,
    sampleQuantity:       r.sample_quantity,
    saleQuantity:         r.sale_quantity,
    purchaseOrderId:      r.purchase_order_id,
    rank:                 r.rank,
    smily:                r.smily,
    urgent:               Boolean(r.urgent),
    prescriptionQuantity: r.prescription_quantity,
    freeOrder:            r.freeOrder,
    labTratuity:          r.lab_gratuity
  }));
}

//
// 5) Prospects
//
async getProspectsToSend(): Promise<{
  identifier: number;
  firstName: string;
  lastName: string;
  activity: string;
  potentialDto: { id: number };
  address: string;
  gsm: string;
  phone: string;
  email: string;
  note: string;
  secretary: string;
  grade: string;
  latitude?: number;
  longitude?: number;
  mapAddress: string;
  status: string;
  validation: number;
  specialityDto: { id: number };
  prospectTypeDto: { id: number };
  establishmentDto: { id: number };
  sectorDto: { id: number };
  localityDto: { id: number };
  taxIdNumber?: string;
  interestIds?: number[];
  contactTypeIds?: number[];
  preferenceIds?: number[];
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_PROSPECT_TO_SEND, []);
  const prospects = (res.values || []).map(r => ({
    identifier:      r.id,
    firstName:       r.firstname,
    lastName:        r.lastname,
    activity:        r.activity,
    potentialDto:    { id: r.potential },
    address:         r.address,
    gsm:             r.gsm,
    phone:           r.phone,
    email:           r.email,
    note:            r.note,
    secretary:       r.secretary,
    grade:           r.grade,
    latitude:        r.lat ?? undefined,
    longitude:       r.lng ?? undefined,
    mapAddress:      r.map_address,
    status:          r.status,
    validation:      r.validation,
    specialityDto:   { id: r.speciality_id },
    prospectTypeDto: { id: r.type_id },
    establishmentDto:{ id: r.establishment_id },
    sectorDto:       { id: r.sector_id },
    localityDto:     { id: r.locality_id },
    taxIdNumber:     r.fiscal_number || null,
    interestIds:     [] as number[],
    contactTypeIds:  [] as number[],
    preferenceIds:   [] as number[]
  }));

  // Add related data for each prospect
  for (const prospect of prospects) {
    // Get interests
    const interestsRes = await this.mDb.query(
      `SELECT interest_id FROM prospect_interest WHERE prospect_id = ?`, 
      [prospect.identifier]
    );
    prospect.interestIds = (interestsRes.values || []).map(r => r.interest_id);

    // Get contact types
    const contactTypesRes = await this.mDb.query(
      `SELECT contact_type_id FROM prospect_contact_type WHERE prospect_id = ?`, 
      [prospect.identifier]
    );
    prospect.contactTypeIds = (contactTypesRes.values || []).map(r => r.contact_type_id);

    // Get preferences
    const preferencesRes = await this.mDb.query(
      `SELECT preference_id FROM prospect_preference WHERE prospect_id = ?`, 
      [prospect.identifier]
    );
    prospect.preferenceIds = (preferencesRes.values || []).map(r => r.preference_id);
  }

  return prospects;
}

//
// 6) Expense Reports
//
async getExpensesToSend(): Promise<{
  identifier: number;
  date: Date;
  description: string;
  montant: number;
  expenseTypeId: number;
  attachmentBase64: string;
  attachmentName: string;
  activityId: number;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_EXPENSE_REPORT_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier:       r.id,
    date:             new Date(r.expense_date),
    description:      r.description,
    montant:          r.montant,
    expenseTypeId:    r.expense_type_id,
    attachmentBase64: r.attachementBase64,
    attachmentName:   r.attachmentName,
    activityId:       r.activity_id
  }));
}

//
// 7) Activities
//
async getActivitiesToSend(): Promise<{
  identifier: number;
  activityTimestamp: string;
  date: Date;
  hourNumber: number;
  typeActivity: number;
  comment: string;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_ACTIVITY_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier:        r.id,
    activityTimestamp: r.activity_date,
    date:              new Date(r.activity_date),
    hourNumber:        r.hour_number,
    typeActivity:      r.activity_type_id,
    comment:           r.comment
  }));
}

//
// 8) Marketing Actions
//
async getActionMarketingsToSend(): Promise<{
  identifier: number;
  date: Date;
  budget: number;
  prospectsIds: string[];
  productsIds: string[];
  name: string;
  description: string;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_ACTIONMARKETING_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier:   r.id,
    date:         new Date(r.marketingAction_date),
    budget:       r.budget,
    prospectsIds: (r.prospect_id  || '').split(','),
    productsIds:  (r.product_id   || '').split(','),
    name:         r.name,
    description:  r.description
  }));
}

//
// 9) Purchase Orders
//
async getPurchaseOrdersToSend(): Promise<{
  identifier: number;
  visitId: number;
  attachmentBase64: string;
  attachmentName: string;
  wholesalerId: number;
  placementMethod: string;
  generatePo: boolean;
  generateDo: boolean;
  status: string;
  purchaseOrderTemplateId: number;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_PURCHASEORDER_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier:                r.id,
    visitId:                   r.visit_id,
    attachmentBase64:          r.attachmentBase64,
    attachmentName:            r.attachmentName,
    wholesalerId:              r.wholesaler_id,
    placementMethod:           r.placement_method,
    generatePo:                r.generate_po === 1 || r.generate_po === 'true',
    generateDo:                r.generate_do === 1 || r.generate_do === 'true',
    status:                    r.status,
    purchaseOrderTemplateId:   r.purchase_order_template_id
  }));
}

//
// 10) Recoveries
//
async getRecoveriesToSend(): Promise<{
  identifier: number;
  purchaseOrderId: number;
  date: string;
  amount: number;
  payment: string;
  attachmentId: number;
  description: string;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_RECOVERY_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier:       r.id,
    purchaseOrderId:  r.purchase_order_id,
    date:             r.date,
    amount:           r.amount,
    payment:          r.payment,
    attachmentId:     r.attachment_id,
    description:      r.description
  }));
}

//
// 11) Attachments
//
async getAttachmentsToSend(): Promise<{
  identifier: number;
  attachmentBase64: string;
  attachmentName: string;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_ATTACHMENT_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier:        r.id,
    attachmentBase64:  r.attachmentBase64,
    attachmentName:    r.attachmentName
  }));
}

//
// 12) Messages
//
async getMessagesToSend(): Promise<{
  identifier: number;
  text: string;
  date: string;
  type: string;
  userId: number;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_MESSAGE_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier: r.id,
    text:       r.text,
    date:       r.date,
    type:       r.type,
    userId:     r.user_id
  }));
}

//
// 13) Opportunity Notes
//
async getOpportunityNotesToSend(): Promise<{
  id: number;
  date: string;
  budget: number;
  prospectsIds: string[];
  productsIds: string[];
  pharmaciesIds: string[];
  name: string;
  description: string;
  attachmentBase64: string;
  attachmentName: string;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_OPPORTUNITYNOTE_TO_SEND, []);
  return (res.values || []).map(r => ({
    id:                 r.id,
    date:               r.expense_timestamp,     // keep same format or convert
    budget:             r.budget,
    prospectsIds:       (r.prospect_id || '').split(','),
    productsIds:        (r.product_id  || '').split(','),
    pharmaciesIds:      (r.pharmacie_id|| '').split(','),
    name:               r.name,
    description:        r.description,
    attachmentBase64:   r.attachementBase64,
    attachmentName:     r.attachmentName
  }));
}

//
// 14) Plannings
//
async getPlanningsToSend(): Promise<{
  identifier: number;
  date: Date;
  prospect: number;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_PLANNING_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier: r.id,
    date:       new Date(r.planning_date),
    prospect:   r.prospect_id
  }));
}

//
// 15) Planning Validations
//
async getPlanningValidationsToSend(): Promise<{
  identifier: number;
  date: Date;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_PLANNING_VALIDATION_TO_SEND, []);
  return (res.values || []).map(r => ({
    identifier: r.id,
    date:       new Date(r.planning_validation_date)
  }));
}

//
// 16) Locations
//
async getLocationsToSend(): Promise<{
  id: number;
  date: Date;
  longitude: number;
  latitude: number;
}[]> {
  const res = await this.mDb.query(this.queries.SELECT_LOCATION_TO_SEND, []);
  return (res.values || []).map(r => ({
    id:        r.id,
    date:      new Date(r.pos_timestamp),
    longitude: r.longitude,
    latitude:  r.latitude
  }));
}
// DELETE LIST HELPERS

/** 1) Visits to delete */
async getVisitsToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_VISIT_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 2) Visit-Products to delete */
async getVisitProductsToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_VISIT_PRODUCT_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 3) Activities to delete */
async getActivitiesToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_ACTIVITY_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 4) Expense Reports to delete */
async getExpenseReportsToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_EXPENSE_REPORT_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 5) Opportunity Notes to delete */
async getOpportunityNotesToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_OPPORTUNITYNOTE_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 6) Plannings to delete */
async getPlanningsToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_PLANNING_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 7) Planning-Validations to delete */
async getPlanningValidationsToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_PLANNING_VALIDATION_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 8) Marketing Actions to delete */
async getMarketingActionsToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_ACTIONMARKETING_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 9) Purchase Orders to delete */
async getPurchaseOrdersToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_PURCHASE_ORDER_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 10) Recoveries to delete */
async getRecoveriesToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_RECOVERY_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}

/** 11) Attachments to delete */
async getAttachmentsToDelete(): Promise<number[]> {
  const res = await this.mDb.query(this.queries.SELECT_ATTACHMENT_TO_DELETE, []);
  return (res.values || []).map(r => r.id);
}
 async send(): Promise<void> {
    // right after you fetch currentUser…
  this.currentUser = await this.commonService.getCurrentUser();
  console.log("currentUser", this.currentUser);
  // you know it’s a number, so…
  this.userId = this.currentUser.user_id;

    if (this.isSyncronizing) return;
    this.isSyncronizing = true;

    const loading = await this.loadingCtrl.create({
      message: 'Sending data…',
    });
    await loading.present();
    
    try {
        console.log("STEP 1: getProspectsToSend()");
        const prospectsChangeRequests = await this.getProspectsToSend();
        console.log(`  → prospectsChangeRequests.length = ${prospectsChangeRequests.length}`);
    
        console.log("STEP 2: getVisitsToSend()", this.userId);
        const visits = await this.getVisitsToSend(this.userId);
        console.log(`  → visits.length = ${visits.length}`);
    
        console.log("STEP 3: getVisitProductsToSend()", this.userId);
        const visitProducts = await this.getVisitProductsToSend(this.userId);
        console.log(`  → visitProducts.length = ${visitProducts.length}`);
    
        console.log("STEP 4: getExpensesToSend()");
        const expenses = await this.getExpensesToSend();
        console.log(`  → expenses.length = ${expenses.length}`);
    
        console.log("STEP 5: getPlanningsToSend()");
        const plannings = await this.getPlanningsToSend();
        console.log(`  → plannings.length = ${plannings.length}`);
    
        console.log("STEP 6: getPlanningValidationsToSend()");
        const planningValidations = await this.getPlanningValidationsToSend();
        console.log(`  → planningValidations.length = ${planningValidations.length}`);
    
        console.log("STEP 7: getOpportunityNotesToSend()");
        const opportunityNotes = await this.getOpportunityNotesToSend();
        console.log(`  → opportunityNotes.length = ${opportunityNotes.length}`);
    
        console.log("STEP 8: getLocationsToSend()");
        const locations = await this.getLocationsToSend();
        console.log(`  → locations.length = ${locations.length}`);
    
        console.log("STEP 9: getActivitiesToSend()");
        const activities = await this.getActivitiesToSend();
        console.log(`  → activities.length = ${activities.length}`);
    
        console.log("STEP 10: getActionMarketingsToSend()");
        const actionMarketings = await this.getActionMarketingsToSend();
        console.log(`  → actionMarketings.length = ${actionMarketings.length}`);
    
        console.log("STEP 11: getPurchaseOrdersToSend()");
        const purchaseOrders = await this.getPurchaseOrdersToSend();
        console.log(`  → purchaseOrders.length = ${purchaseOrders.length}`);
    
        console.log("STEP 12: getRecoveriesToSend()");
        const recoveries = await this.getRecoveriesToSend();
        console.log(`  → recoveries.length = ${recoveries.length}`);
    
        console.log("STEP 13: getAttachmentsToSend()");
        const attachments = await this.getAttachmentsToSend();
        console.log(`  → attachments.length = ${attachments.length}`);
    
        console.log("STEP 14: getMessagesToSend()");
        const messages = await this.getMessagesToSend();
        console.log(`  → messages.length = ${messages.length}`);
    
        console.log("STEP 15: getTaggedUsersToSend()");
        const tagedUsers = await this.getTaggedUsersToSend();
        console.log(`  → tagedUsers.length = ${tagedUsers.length}`);
    
        console.log("STEP 16: getTimeTrackingToSend()");
        const presentationTimeTracking = await this.getTimeTrackingToSend();
        console.log(`  → presentationTimeTracking.length = ${presentationTimeTracking.length}`);
    
        // now the deletes
        console.log("STEP 17: getVisitsToDelete()");
        const visitsToDelete = await this.getVisitsToDelete();
        console.log(`  → visitsToDelete.length = ${visitsToDelete.length}`);
    
        console.log("STEP 18: getVisitProductsToDelete()");
        const visitProductsToDelete = await this.getVisitProductsToDelete();
        console.log(`  → visitProductsToDelete.length = ${visitProductsToDelete.length}`);
    
        console.log("STEP 19: getActivitiesToDelete()");
        const activitiesToDelete = await this.getActivitiesToDelete();
        console.log(`  → activitiesToDelete.length = ${activitiesToDelete.length}`);
    
        console.log("STEP 20: getExpenseReportsToDelete()");
        const expenseReportToDelete = await this.getExpenseReportsToDelete();
        console.log(`  → expenseReportToDelete.length = ${expenseReportToDelete.length}`);
    
        console.log("STEP 21: getOpportunityNotesToDelete()");
        const opportunityNotesToDelete = await this.getOpportunityNotesToDelete();
        console.log(`  → opportunityNotesToDelete.length = ${opportunityNotesToDelete.length}`);
    
        console.log("STEP 22: getPlanningsToDelete()");
        const planningsToDelete = await this.getPlanningsToDelete();
        console.log(`  → planningsToDelete.length = ${planningsToDelete.length}`);
    
        console.log("STEP 23: getPlanningValidationsToDelete()");
        const planningValidationToDelete = await this.getPlanningValidationsToDelete();
        console.log(`  → planningValidationToDelete.length = ${planningValidationToDelete.length}`);
    
        console.log("STEP 24: getMarketingActionsToDelete()");
        const marketingActionsToDelete = await this.getMarketingActionsToDelete();
        console.log(`  → marketingActionsToDelete.length = ${marketingActionsToDelete.length}`);
    
        console.log("STEP 25: getPurchaseOrdersToDelete()");
        const purchaseOrderToDelete = await this.getPurchaseOrdersToDelete();
        console.log(`  → purchaseOrderToDelete.length = ${purchaseOrderToDelete.length}`);
    
        console.log("STEP 26: getRecoveriesToDelete()");
        const recoveryToDelete = await this.getRecoveriesToDelete();
        console.log(`  → recoveryToDelete.length = ${recoveryToDelete.length}`);
    
        console.log("STEP 27: getAttachmentsToDelete()");
        const attachmentToDelete = await this.getAttachmentsToDelete();
        console.log(`  → attachmentToDelete.length = ${attachmentToDelete.length}`);
    

      // 2) Build the exact same DTO as before
      const sendRequestDto = {
        prospectsChangeRequests,
        visits,
        visitProducts,
        purchaseOrders,
        recoveries,
        attachments,
        messages,
        tagedUsers,
        presentationTimeTracking,
        plannings,
        planningValidations,
        actionMarketings,
        notefrais: expenses,
        opportunityNotes,
        locations,
        activities,

        visitsToDelete,
        visitProductToDelete: visitProductsToDelete,
        purchaseOrderToDelete,
        recoveryToDelete,
        attachmentToDelete,
        activitiesToDelete,
        expenseReportToDelete,
        planningValidationToDelete,
        planningsToDelete,
        marketingActionsToDelete,
        opportunityNotestodelete: opportunityNotesToDelete
      };
      // 1) grab your auth token exactly the same way
      const sendApiUrl = await this.getApiUrl("/synchronise/send")
      const token = await this.LoginService.getToken()
      // 2) set up the headers
        const headers = new HttpHeaders({
          'Authorization': `${token}`,
          'Content-Type': 'application/json'
        });
      // 3) POST it
      const response = await this.http.post<any>(sendApiUrl, sendRequestDto, { headers }).toPromise()
      console.log("Sent!", response)
      
      // 4) Update local DB
      await this.handleSendResponse(response);
      // 5) Wipe out the “location” table now that everything’s on the server
      await this.mDb.run(this.queries.DELETE_LOCATION, []);

      // 6) Optionally record “last send” timestamp somewhere
      await this.insertLastSynchronization();

      this.popupService.showToasterAlert(
        'Success',
        'Data sent successfully',
        'success'
      );
    } catch (error) {
      this.handleSynError(error, 'SEND');
    } finally {
      this.isSyncronizing = false;
      await loading.dismiss();
    }
  }

  private async handleSendResponse(resp: any): Promise<void> {
    console.log('handleSendResponse ▶ start', resp);
  
    if (!resp) {
      console.warn('handleSendResponse ⚠ empty or invalid response, skipping local updates');
      return;
    }
  
    // 1) Mark everything that was saved as synchronized
    console.log('handleSendResponse ▶ marking synchronized records…');
    const syncMap: { table: string; ids?: number[] }[] = [
      { table: 'prospect',                   ids: resp.prospectChangesIds },
      { table: 'visit',                      ids: resp.savedVisitIds },
      { table: 'visit_product',              ids: resp.savedVisitProductIds },
      { table: 'purchase_order',             ids: resp.savedPurchaseOrderIds },
      { table: 'expense',                    ids: resp.savedExpenseReportsIds },
      { table: 'marketing_action',           ids: resp.savedMarketingActionsIds },
      { table: 'activity',                   ids: resp.savedActivityIds },
      { table: 'planning',                   ids: resp.savedPlanningIds },
      { table: 'planning_validation',        ids: resp.savedPlanningValidationIds },
      { table: 'recovery',                   ids: resp.savedRecoveryIds },
      { table: 'attachment',                 ids: resp.savedAttachmentIds },
      { table: 'message',                    ids: resp.savedMessageIds },
      { table: 'message_tag',                ids: resp.savedMessageTagIds },
      { table: 'presentation_time_tracking', ids: resp.savedTrackingTimeIds },
    ];
  
    for (const { table, ids } of syncMap) {
      if (ids && ids.length) {
        console.log(`  → [SYNC]  ${table}: updating ${ids.length} row(s) to synchronized`);
        await this.mDb.run(
          `UPDATE ${table} SET synchronized = 1 WHERE id IN (${ids.join(',')})`
        );
        console.log(`  ← [SYNC]  ${table}: done`);
      } else {
        console.log(`  • [SYNC]  ${table}: nothing to update`);
      }
    }
    console.log('handleSendResponse ✔ marking synchronized records complete');
  
    // 2) Physically delete any rows the server says to remove
    console.log('handleSendResponse ▶ deleting server-deleted records…');
    const deleteMap: { table: string; ids?: number[] }[] = [
      { table: 'visit',               ids: resp.deletedVisits },
      { table: 'visit_product',       ids: resp.deletedVisitProducts },
      { table: 'purchase_order',      ids: resp.deletedPurchaseOrders },
      { table: 'expense',             ids: resp.deletedExpenseReports },
      { table: 'marketing_action',    ids: resp.deletedMarketingActions },
      { table: 'activity',            ids: resp.deletedActivities },
      { table: 'planning',            ids: resp.deletedPlannings },
      { table: 'planning_validation', ids: resp.deletedPlanningValidations },
      { table: 'recovery',            ids: resp.deletedRecovery },
      { table: 'attachment',          ids: resp.deletedAttachment },
    ];
  
    for (const { table, ids } of deleteMap) {
      if (ids && ids.length) {
        console.log(`  → [DEL]   ${table}: deleting ${ids.length} row(s)`);
        await this.mDb.run(
          `DELETE FROM ${table} WHERE id IN (${ids.join(',')})`
        );
        console.log(`  ← [DEL]   ${table}: done`);
      } else {
        console.log(`  • [DEL]   ${table}: nothing to delete`);
      }
    }
    console.log('handleSendResponse ✔ deleting server-deleted records complete');
  
    console.log('handleSendResponse ◀ end');
  }
  

  /** Shared error handler */
  private handleSynError(error: any, code: string) {
    console.error('Sync error', code, error);
    this.popupService.showToasterAlert(
      'Error',
      `Synchronization failed (code ${code})`,
      'warning'
    );
    this.isSyncronizing = false;
  }

  /** Stub for your “last sync” logic */
  private async insertLastSynchronization() {
    // e.g. await this.mDb.run(`UPDATE user SET last_send_date = ?`, [Date.now()]);
  }

}
