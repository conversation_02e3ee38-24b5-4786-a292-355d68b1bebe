import { CommonModule } from '@angular/common';
import { Component, Inject, Input, OnInit } from '@angular/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { IonicModule, LoadingController } from '@ionic/angular';
import { COLORS } from 'src/app/constants';
import { CommonService } from 'src/app/services/common-service.service';
import { DbCreationTablesService } from 'src/app/services/db-creation-tables.service';
import { FilterService } from 'src/app/services/filterService.service';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { SelectCustomEvent } from '@ionic/angular';
import { IonicSelectableComponent } from 'ionic-selectable';
@Component({
  selector: 'app-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.scss'],
  standalone: true,
  imports: [ IonicModule,IonicSelectableComponent, FormsModule, CommonModule, ReactiveFormsModule]
})
export class FilterComponent implements OnInit {
    translations: any = {};
  @Input() sectors: any[] = [];
  @Input() localities: any[] = [];
  @Input() specialities: any[] = [];
  @Input() potentials: any[] = [];
  @Input() activities: any[] = [];
  @Input() establishments: any[] = [];
  @Input() LABELS: any;

  @Input() _sectorSelected: any = null;
  @Input() _localitySelected: any = null;
  @Input() _specialitySelected: any = null;
  @Input() _potentialSelected: any = null;
  @Input() _activitySelected: any = null;
  @Input() _establishmentSelected: any = null;
  @Input() keywordsFilter: string ='';
  @Input() showOnlyPlanifiedProspects: boolean = false;
  @Input() showOnlyNotVisited: boolean = false;


  @Input() filterProspects!: Function;
  @Input() syncSelections!: (
    sector: any,
    locality: any,
    establishment: any,
    speciality: any,
    potential: any,
    activity: any
  ) => void;
  @Input() filterLocalitiesBySector!: Function;
  @Input() filterEstablishmentByActivity!: Function;
  @Input() highlightVisitedProspect !: Function;
  private mDb!: SQLiteDBConnection
  constructor(private translationService: TranslationService,
    private filterService: FilterService, 
    private loadingCtrl: LoadingController,
    private commonService: CommonService,
    private dbCreationTablesService: DbCreationTablesService,
    @Inject(COLORS) private colors: { RED: string; GREEN: string; ORANGE: string; WHITE: string; BLACK: string},){

  }
  async ngOnInit() {
    this.loadTranslations('an.json');
    this.mDb =  this.dbCreationTablesService.getDatabase();
}
translate(key: string): string {
  return this.translations[key] || key;
}
filterSectors(event: { component: IonicSelectableComponent, value: any }) {
  console.log('port:', event.value);
}
async onLocalityChange(locality: { id: number; name?: string }) {
  // 1) Fetch the sector row for that locality
  const sectorRow = await this.filterService.getSectorByLocality(locality.id);
  // 2) Pick the SAME object out of your sectors[] (so ngModel matches)
  this._sectorSelected =
    this.sectors.find(s => s.id == sectorRow.id) ||
    { id: sectorRow.id, name: sectorRow.name };
  // 3) **await** & **assign** the NEW locality list
  if (this._sectorSelected.id != null){
  this.localities = await this.filterService.filterLocalitiesBySector(
    this._sectorSelected.id!
  );
}
  else{
    this.localities = await this.filterService.getAllLocalities(true);
  }

  console.log('📦 component localities after service:', this.localities);
  // 3) Rebuild the locality list (keeping id:number)
  console.log("sector", this._sectorSelected.id)
  console.log("sectorRow", sectorRow)
  // 4) Re-select the same locality from the new options
  this._localitySelected =
    this.localities.find(l => l.id == locality.id) ||
    null;

  // 5) Now run the shared sync + filter logic
  this.syncSelections(
    this._sectorSelected,
    this._localitySelected,
    this._establishmentSelected,
    this._specialitySelected,
    this._potentialSelected,
    this._activitySelected
  );
  this.filterProspects(
    this._sectorSelected,
    this._localitySelected,
    this._establishmentSelected,
    this._specialitySelected,
    this._potentialSelected,
    this._activitySelected,
    this.keywordsFilter,
    this.showOnlyPlanifiedProspects,
    this.showOnlyNotVisited,
    null
  );

}

private loadTranslations(lang: string) {
  this.translationService.loadTranslations(lang).subscribe(
    (translations) => {
      this.translations = translations;
      
    },
    (error) => {
      console.error(`Error loading translations for ${lang}`, error);
    }
  );
}

}
