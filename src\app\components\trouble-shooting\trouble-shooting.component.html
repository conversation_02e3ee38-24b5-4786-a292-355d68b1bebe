
<ion-content class="ion-padding trouble-shooting-container">
  <ion-header>
    <ion-toolbar color="primary">
      <ion-title>{{ pageTitle }}</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="request-input">
    <ion-label position="stacked">Request:</ion-label>
    <ion-textarea [(ngModel)]="request" placeholder="Enter your request here"></ion-textarea>
  </div>

  <div class="actions">
    <ion-button expand="block" (click)="execute()">Execute</ion-button>
    <ion-button expand="block" (click)="export()">Export Database</ion-button>
    <ion-button expand="block" (click)="goBack()">Retour</ion-button>
      
  </div>

  <ion-item>
    <ion-label>Afficher la console</ion-label>
    <ion-toggle [(ngModel)]="showConsole" (ionChange)="toggleConsole()"></ion-toggle>
  </ion-item>

  <div *ngIf="result" class="result">
    <h2>Result:</h2>
    <pre>{{ result | json }}</pre>
  </div>


