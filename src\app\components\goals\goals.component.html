<app-header [title]="translate('GOAL')"></app-header>


<ion-content >
  <div class="header-container">
    <ion-item lines="none">
      <ion-label>{{ translate('START_DATE') }} :</ion-label>
      <ion-datetime 
        displayFormat="DD/MM/YYYY" 
        [value]="startDate.toISOString()" 
        (ionChange)="onStartDateChange($event)">
      </ion-datetime>
    </ion-item>
    <ion-item lines="none">
      <ion-label>{{ translate('END_DATE') }} :</ion-label>
      <ion-datetime 
        displayFormat="DD/MM/YYYY" 
        [value]="endDate.toISOString()" 
        (ionChange)="onEndDateChange($event)">
      </ion-datetime>
    </ion-item>
  </div>
  

  <div class="filters-container">
    <ion-item>
      <ion-select [(ngModel)]="selectedKpi.current" (ionChange)="onKpiChange()">
        <ion-select-option *ngFor="let option of kpiOptions" [value]="option">{{ translate(option.label) }}</ion-select-option>
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-select [(ngModel)]="selectedType.type" (ionChange)="onKpiChange()">
        <ion-select-option *ngFor="let type of types" [value]="type">{{ translate(type.name) }}</ion-select-option>
      </ion-select>
    </ion-item>
  </div>

  <div class="stats-container">
    <div class="stat-box ca-total" >
      <span>{{ translate(totalLabel) }} : </span>
      <span>{{ sumGoals }}</span>
    </div>
    
    <div class="stat-box goal-total" >
      <span>{{translate('TOTAL_OBJECTIVE')}} : </span>
      <span>{{ sumCount }}</span>
    </div>
  </div>
  
  <div id="smiley_div" class="smiley-container"></div> <!-- Added div for smiley -->

  <ion-grid>
    <ion-row>
      <ion-col size="6" class="card">
        <ion-card class="custom-card" >
          <ion-card-header>
            <ion-card-title>{{translate('VISITS_MADE_BY_PROTFOLIO')}}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <canvas id="visitsByPortfolioChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
      <ion-col size="6" class="card">
        <ion-card class="custom-card" >
          <ion-card-header>
            <ion-card-title>{{ translate(getCardTitle('BY_ACTIVITY')) }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <canvas id="visitsByActivityChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="6" class="card">
        <ion-card class="custom-card" >
          <ion-card-header>
            <ion-card-title>{{ translate(getCardTitle('SPECIALITY_ORDER')) }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <canvas id="visitsBySpecialityChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
      <ion-col size="6" class="card">
        <ion-card class="custom-card" >
          <ion-card-header>
            <ion-card-title>{{ translate(getCardTitle('BY_PROSPECT_TYPE')) }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <canvas id="visitsByProspectTypeChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="6" class="card">
        <ion-card class="custom-card" >
          <ion-card-header>
            <ion-card-title>{{ translate(getCardTitle('BY_POTENTIAL')) }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <canvas id="visitsByPotentialChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
      <ion-col size="6" class="card">
        <ion-card class="custom-card" >
          <ion-card-header>
            <ion-card-title>{{ translate(getCardTitle('BY_SECTOR')) }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <canvas id="visitsBySectorChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="12" class="card">
        <ion-card class="custom-card" >
          <ion-card-header>
            <ion-card-title>{{ translate(getCardTitle('BY_PRODUCT')) }}</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <canvas id="visitsByProductChart"></canvas>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>