import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { IonicModule, AlertController } from '@ionic/angular';
import { takeUntil } from 'rxjs/operators';
import { MessageService } from 'src/app/services/message.service';
import { Subject, Subscription } from 'rxjs';
import { DateService } from 'src/app/services/date.service';
import { Message } from 'src/app/models/message';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { ThemeService } from 'src/app/services/theme.service';
import { LoginService } from 'src/app/services/login-service.service';
import { HeaderComponent } from '../../header/header.component';


@Component({
  selector: 'app-message',
  templateUrl: './message.component.html',
  styleUrls: ['./message.component.scss'],
  standalone: true,
  imports: [IonicModule, FormsModule, CommonModule, ReactiveFormsModule,HeaderComponent]
})
export class MessageComponent implements OnInit, OnDestroy {
  messageForm!: FormGroup;
  messages: Message[] = [];
  date: Date = new Date();
  messageId: number | null = null;
  destroy$ = new Subject<void>();
  private translationSubscription?: Subscription
  private languageSubscription?: Subscription
  currentLanguage = "an.json"
  translations: any = {};



  LABELS = {
    DATE: 'Date',
    WEEKLY_REPORT: 'Weekly Report',
    MONTHLY_REPORT: 'Monthly Report',
    MESSAGE: 'Message',
    SAVE: 'Save',
    CANCEL: 'Cancel',
    CONFIRM: 'Confirm',
    SUCCESS: 'Success',
    OK: 'OK',
    MESSAGE_SAVED: 'Message saved successfully.',
    DELETE_CONFIRMATION: 'Are you sure you want to delete this message?'
  };

  constructor(
    private formBuilder: FormBuilder,
    private alertController: AlertController,
    private messageService: MessageService,
    private dateService: DateService,
    private translationService: TranslationService,private themeService: ThemeService,
    private LoginService : LoginService
  ) {}

  ngOnInit() {
    this.messageForm = this.formBuilder.group({
      text: ['', Validators.required],
      type: ['', Validators.required]
    });
    // ✅ S'abonner aux changements de traductions
  this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
    console.log("🔄 ActivityCalender - Traductions mises à jour:", translations)
    this.translations = translations
  })

  // ✅ S'abonner aux changements de langue
  this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
    console.log("🌐 ActivityCalender - Langue changée vers:", language)
    this.currentLanguage = language
  })
    this.getMessages();
    ///this.getAllMessages();
  }
  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
    this.destroy$.next()
    this.destroy$.complete()
  }
  
  getAllMessages() {

    this.messageService.getAllMessages()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: messages => this.messages = messages,
        error: error => console.error('Error fetching messages:', error)
      });
  }

  

  async openDatePicker() {
    const alert = await this.alertController.create({
      header: 'Select Date',
      inputs: [
        {
          name: 'date',
          type: 'date',
          value: this.date.toISOString().substring(0, 10),
          cssClass: 'date-picker-custom' // Ajouter une classe personnalisée ici
        }
      ],
      buttons: [
        {
          text: this.LABELS.CANCEL,
          role: 'cancel'
        },
        {
          text: this.LABELS.OK,
          handler: (data) => {
            this.date = new Date(data.date);
            this.getMessages();
          }
        }
      ]
    });

    await alert.present();
  }

  saveMessage() {
    const text = this.messageForm.get('text')!.value;
    const type = this.messageForm.get('type')!.value;

    const message: Message = {
      id: this.messageId || new Date().getTime(),
      text,
      type,
      date: this.date.getTime(),
      user_id: 1, // Replace with actual user ID
      status: 'NEW',
      synchronized: 0
    };

    if (this.messageId) {
      this.messageService.updateMessage(this.messageId,message).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.getMessages();
      });
    } else {
      this.messageService.saveMessage(message).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.getMessages();
      });
    }

    this.messageId = null;
    this.messageForm.reset();
    this.showAlert(this.LABELS.SUCCESS, this.LABELS.MESSAGE_SAVED);
    
  }

  updateMessage(message: Message) {
    this.messageId = message.id;
    this.messageForm.patchValue({
      text: message.text,
      type: message.type
    });

  }

  deleteMessage(id:number) {
    this.showConfirm(this.LABELS.CONFIRM, this.LABELS.DELETE_CONFIRMATION).then(res => {
      if (res) {
        this.messageService.deleteMessage(id).pipe(takeUntil(this.destroy$)).subscribe(() => {
          this.getMessages();
        });
      }
    });
  }

  getMessages() {
    this.messageService.getMessagesByDate(
      this.dateService.getDayFromDate(this.date),
      this.dateService.getMonthFromDate(this.date),
      this.dateService.getYearFromDate(this.date)
    ).then(messages => {
      this.messages = messages;
    }).catch(error => {
      console.error('Failed to fetch messages', error);
    });
  }

  async showAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: [this.LABELS.OK]
    });
    await alert.present();
  }

  async showConfirm(header: string, message: string): Promise<boolean> {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header,
        message,
        buttons: [
          {
            text: this.LABELS.CANCEL,
            role: 'cancel',
            handler: () => resolve(false)
          },
          {
            text: this.LABELS.OK,
            handler: () => resolve(true)
          }
        ]
      });
      await alert.present();
    });
  }
  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ message - Traduction manquante: ${key}`)
    }
    return translation || key
  }

  async changeLanguage(lang: string) {
    try {
      console.log("🌐 message - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ message - Langue changée avec succès")
    } catch (error) {
      console.error("❌ message - Erreur changement langue:", error)
    }
  }

  private loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;
        
      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  }
  logout() {
    this.LoginService.logout();
  }
  onThemeChange(): void {
    this.themeService.switchTheme();
  }
  
}
