import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { AlertController, IonicModule } from '@ionic/angular';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { Activity, ActivityType } from 'src/app/models/activity';
import { Expense, ExpenseType } from 'src/app/models/expense';
import { marketingAction } from 'src/app/models/marketing-action';
import { Planning, Prospect } from 'src/app/models/planing';
import { Visit } from 'src/app/models/visit';
import { ActivityService } from 'src/app/services/activity.service';
import { CommonService } from 'src/app/services/common-service.service';
import { DateService } from 'src/app/services/date.service';
import { ExpenseService } from 'src/app/services/expense.service';
import { ActionMarketingService } from 'src/app/services/action-marketing.service';
import { PlanningService } from 'src/app/services/planning.service';
import { ThemeService } from 'src/app/services/theme.service';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { VisitService } from 'src/app/services/visit.service';
import { LoginService } from 'src/app/services/login-service.service';
import { HeaderComponent } from '../../header/header.component';


@Component({
  selector: 'app-activity-calender',
  templateUrl: './activity-calender.component.html',
  styleUrls: ['./activity-calender.component.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, ReactiveFormsModule, RouterLink, FormsModule,HeaderComponent]
})
export class ActivityCalenderComponent implements OnInit {
  
  //variables
  activityToEdit = new Activity()
  buttonAddActivityOverPeriod: boolean = false
  buttonAddOtherActivities: boolean = false
  startDate: Date = new Date();
  endDate: Date = new Date();
  buttonUpdateActivity: boolean = false;
  planningList: Planning[][] = []
  selectedType = new ActivityType()
  activityList: Activity[][] = []
  visitList: Visit[][] = []
  marketingActionsList: marketingAction[][] = []
  expenseList: Expense[][] = []
  activities: Activity[] = []
  prospects: Prospect[] = []
   destroy$ = new Subject<void>();
  private translationSubscription?: Subscription
  private languageSubscription?: Subscription
  currentLanguage = "an.json"
  translations: any = {};
  selectedDate = new Date()
  dates: Date[] = [];
  types: ExpenseType[] = [];
  startDay = new Date()
  endDay = new Date()
  activityTypes: ActivityType[] = [];
  marketingActions: marketingAction[] = [];
  isDarkTheme = false;
  activityForm!: FormGroup;
  activityToAdd = new Activity()
  dayNumber: number = -1
  hourNumber: number = 0
  errorMessage:string='';
  constructor(private translationService: TranslationService,
    private visitService: VisitService,
    private expenseService: ExpenseService,
    private activityService: ActivityService,
    private formBuilder: FormBuilder,
    private marketingService: ActionMarketingService,
    private planningService: PlanningService,
    private commonService: CommonService,
    private alertController: AlertController,
    private dateService: DateService, private cdr: ChangeDetectorRef,private themeService: ThemeService,
    private LoginService : LoginService,
  ) { }
  ngOnInit() {
  // ✅ S'abonner aux changements de traductions
  this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
    console.log("🔄 ActivityCalender - Traductions mises à jour:", translations)
    this.translations = translations
  })

  // ✅ S'abonner aux changements de langue
  this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
    console.log("🌐 ActivityCalender - Langue changée vers:", language)
    this.currentLanguage = language
  })
    this.selectedDate=this.dateService.getMondayOfWeek();
    console.log("date",this.selectedDate)
    this.initializeForm();
    const currentDate = new Date();
    this.startDay = new Date(currentDate);
    this.endDay = new Date(currentDate);
    this.getDateOfDay();
    this.getAllActivityTypes();
    this.getAllActivities();
    this.getAllProspects();
    this.getAllTypes();
    this.getAllData();
    this.getActivityByDay(this.startDate,this.dayNumber);
  
    const year = this.selectedDate.getFullYear();
  const month = String(this.selectedDate.getMonth() + 1).padStart(2, '0');
  const day = String(this.selectedDate.getDate()).padStart(2, '0');
  const isoDateString = `${year}-${month}-${day}T00:00:00.000Z`;
  
  this.activityForm.get('selectedDate')?.setValue(isoDateString);
  }
  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
    this.destroy$.next()
    this.destroy$.complete()
  }
  /////save et update
  async save() {
    const formValue = this.activityForm.value;
    if (!formValue.comment  || !this.selectedType.id) {
      console.error('Veuillez remplir tous les champs requis.');
      this.errorMessage = 'Veuillez remplir tous les champs requis.';
      return; // Prevent further actions
    } else {
      this.errorMessage = ''; // Reset the error message if the form is valid
    }
    if (this.buttonUpdateActivity) {
      let updatedActivity = {
        id: this.activityToEdit.id,
        activityDate: this.activityToEdit.activityDate,
        comment: formValue.comment,
        hourNumber: formValue.hourNumber,
        status: this.activityToEdit.status,
        activity_type_id: formValue.typeName.id,
        synchronized: this.activityToEdit.synchronized
      };
      try { await this.activityService.updateActivity(updatedActivity) }
      catch (error) {
        console.error('Failed to update activity:', error);
      } finally {
        this.activityForm.reset()
        this.activityToEdit = new Activity();
      }
    }
    if (this.buttonAddOtherActivities) {
      this.activityToAdd = {
        id: this.dates[this.dayNumber].getTime(),
        activityDate: this.dates[this.dayNumber].getTime(),
        hourNumber: this.activityForm.value.hourNumber,
        activityTypeId: this.selectedType.id,
        comment: this.activityForm.value.comment,
        synchronized: 0,
        status: 'new'
      };
      console.log(this.activityToAdd)
      try {
        await this.activityService.getActivity(this.activityToAdd);
        this.activityList[this.dayNumber].unshift(this.activityToAdd);
      } catch (error) {
        console.log("Error saving activity:", error);
      }
    }
    if (this.buttonAddActivityOverPeriod) {
      var days = this.dateService.numberOfDaysBetween2Dates(this.startDate, this.endDate)
      for (let i = 0; i <= days; i++) {
        this.activityToAdd = {
          id: this.dateService.addDays(this.startDate, i).getTime(),
          activityDate: this.dateService.addDays(this.startDate, i).getTime(),
          synchronized: 0,
          status: 'new',
          activityTypeId: this.selectedType.id,
          hourNumber: 9, // adjust to need 
          comment: this.activityForm.value.comment,
        }
        try {
          await this.activityService.getActivity(this.activityToAdd);
          await this.getAllData()
          this.activities.unshift(this.activityToAdd)
          this.getAllActivities()
          console.log(this.activityToAdd);
        } catch (error) {
          console.log("Error saving activity:", error);
        }
      }
    }
    this.activityForm.reset()
    this.selectedType = new ActivityType()
    this.getAllData()
    this.buttonUpdateActivity = false;
    this.buttonAddOtherActivities = false;
    this.buttonAddActivityOverPeriod = false;
  }
  updateActivity(activity: Activity) {
    console.log(activity);
    this.buttonUpdateActivity = true;
    this.buttonAddOtherActivities = false;
    this.buttonAddActivityOverPeriod = false;
    this.cdr.detectChanges();
    if (!this.activityForm.get('hourNumber')) {
      this.activityForm.addControl('hourNumber', new FormControl('', Validators.required));
    }
    if (!this.activityForm.get('comment')) {
      this.activityForm.addControl('comment', new FormControl('', Validators.required));
    }
    this.activityForm.patchValue({
      typeName: this.activityTypes.find((x) => x.id === activity.activityTypeId),
      hourNumber: activity.hourNumber,
      comment: activity.comment,
    });
    this.activityToEdit = activity

  }
  checkOpenActivityPeriod(selectedTabDate: Date) {
    this.commonService.checkOpenPeriod(selectedTabDate, 'ACTIVITY');
  }
  // date picker 
  async onDateChange(event: any) {
    this.selectedDate = new Date(event.detail.value);
    this.getDateOfDay()
    await this.getAllData()
  }
  isMonday = (dateString: string) => {
    const date = new Date(dateString);
    const utcDay = date.getUTCDay();
    return utcDay === 1; // 1 corresponds to Monday
  }
  
  onStartDateChange(event: any) {
    this.startDate = new Date(event.detail.value);
    const day = this.startDate.getDate();
    const month = this.startDate.getMonth() ; 
    const year = this.startDate.getFullYear();
    this.startDate = new Date(year, month, day);
    this.checkOpenActivityPeriod(this.startDate)
  }
  onEndDateChange(event: any) {
      this.endDate = new Date(event.detail.value);
      const day = this.endDate.getDate();
      const month = this.endDate.getMonth() ; 
      const year = this.endDate.getFullYear();
    
      this.endDate = new Date(year, month, day,);
      this.checkOpenActivityPeriod(this.endDate)
    }
  
  getDateOfDay() {
    if (this.selectedDate) {
      for (let i = 0; i < 7; i++) {
        this.dates[i] = this.dateService.addDays(this.selectedDate, i);
      }
    }
  }
  ///// filtre 
  async getExpenseByDay(date: Date, dayNumber: number) {
    this.expenseList[dayNumber] = await this.expenseService.getExpenseByDay(date);
  }
  async getMarketingActionByDay(date: Date, dayNumber: number) {
    this.marketingActionsList[dayNumber] = await this.marketingService.getMarketingActionByDay(date);
  }
  async getActivityByDay(date: Date, dayNumber: number) {
    this.activityList[dayNumber] = await this.activityService.getActivityByDay(date);
  }
  async getPlanningByDay(date: Date, dayNumber: number) {
    this.planningList[dayNumber] = await this.planningService.getPlanningByDay(date);
  }
  async getVisitByDay(date: Date, dayNumber: number) {
    this.visitList[dayNumber] = await this.visitService.getVisitByDay(date);
  }
  async getAllData() {
    for (let dayNumber = 0; dayNumber < 7; dayNumber++) {
      this.getExpenseByDay(this.dates[dayNumber], dayNumber);
      this.getMarketingActionByDay(this.dates[dayNumber], dayNumber);
      this.getActivityByDay(this.dates[dayNumber], dayNumber);
      this.getVisitByDay(this.dates[dayNumber], dayNumber);
      this.getPlanningByDay(this.dates[dayNumber], dayNumber)
    }
  }
 
  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ ActivityCalender - Traduction manquante: ${key}`)
    }
    return translation || key
  }
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 ActivityCalender - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ ActivityCalender - Langue changée avec succès")
    } catch (error) {
      console.error("❌ ActivityCalender - Erreur changement langue:", error)
    }
  }
  loadTranslations(lang: string) {
    this.translationService.loadTranslations(lang).subscribe(
      (translations) => {
        this.translations = translations;
      },
      (error) => {
        console.error(`Error loading translations for ${lang}`, error);
      }
    );
  }
  //// importation a partir de la base de données 
  getAllActivities() {
    this.activityService.getAllActivities()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (activities) => {
          this.activities = activities;
          console.log("activities", this.activities);
        },
        error: error => console.error('Error fetching activities:', error)
      });
  }
  getAllTypes() {
    this.expenseService.getAllExpenseTypes()
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        {
          next: (types) => {
            this.types = types;
            console.log("expenseTypes", this.types);
          },
          error: error => console.error('Error fetching types:', error)
        });
  }
  getAllActivityTypes() {
    this.activityService.getAllActivityTypes()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (activityTypes) => {
          this.activityTypes = activityTypes;
          console.log("activityTypes", this.activityTypes);
        },
        error: error => console.error('Error fetching types:', error)
      });
  }
  getAllProspects() {
    this.planningService.getAllProspects()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (prospects) => {
          this.prospects = prospects;
          console.log("prospects", this.prospects);
        },
        error: error => console.error('Error fetching prospects:', error)
      });
  }
  //initialisation d'ActivityForm
initializeForm() {
  try {
    const currentDate = new Date();
    this.activityForm = this.formBuilder.group({
      selectedDate: [this.selectedDate, Validators.required],
      typeName: ['', Validators.required],
      hourNumber: ['', Validators.required],
      comment: ['', [Validators.required, Validators.maxLength(255)]],
      startDate: [currentDate.toISOString(), Validators.required],
      endDate: [currentDate.toISOString(), Validators.required]
    });
    console.log("initializeForm done");
  } catch (error) {
    console.error('Error setting up form:', error);
  }
}
  // theme dark & light 
  ///// delete 
  async clickShowConfDeleted(id: number) {
    const confirmAlert = await this.alertController.create({
      header: this.translate('CONFIRMATION'),
      message: this.translate('ARE_YOU_SURE_YOU_WANT_TO_DELETE_THIS_ACTIVITY'),
      buttons: [
        {
          text: this.translate('CANCEL'),
          role: 'cancel',
          handler: () => {
            console.log('Cancelled the removal of Activity');
          },
        },
        {
          text: this.translate('OK'),
          handler: () => {
            this.deleteActivity(id);
          },
        },
      ],
    });
    await confirmAlert.present();
  }
  deleteActivity(id: number) {
    this.activityService.deleteActivity(id).subscribe({
      next: () => {
        console.log('activity deleted successfully');
        this.getAllData()
      },
      error: (error) => {
        console.error('Error deleting activity', error);
      }
    });
  }
  ///// for display purposes 
  getNameFromProspectListById(id: number): string {
    const prospect = this.prospects.find((x) => x.id === id);
    if (prospect) {
      return prospect.firstname + prospect.lastname
    } else {
      return ''
    }
  }
  getActivityTypeNameFromListById(id: number): string {
    const type = this.activityTypes.find((x) => x.id === id);
    if (type) {
      return type.name
    } else {
      return ''
    }
  }
  getExpenseTypeNameFromListById(id: number): string {
    const type = this.types.find((x) => x.id === id);
    if (type) {
      return type.name
    } else {
      return ''
    }
  }
  // buttons 
  addActivityOverPeriod() {
    this.cdr.detectChanges();
    this.buttonAddActivityOverPeriod = true;
    this.buttonAddOtherActivities = false;
    this.buttonUpdateActivity = false
    
  }
  async addOtherActivities(dayNumber: number) {
    this.dayNumber = dayNumber - 1
    this.buttonAddActivityOverPeriod = false;
    this.buttonAddOtherActivities = true;
    this.buttonUpdateActivity = false;
  }
  // formulaire 
  typeChange(event: any) {
    this.selectedType = event.detail.value;
  }
  hourNumberChange(event: any) {
    this.hourNumber = event.detail.value;
  }
  
  
 
    logout() {
      this.LoginService.logout();
    }
    onThemeChange(): void {
      this.themeService.switchTheme();
    }
}
