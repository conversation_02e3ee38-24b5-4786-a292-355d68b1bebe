// Configuration pour plein écran
:host {
    --width: 100vw;
    --height: 100vh;
    --max-width: 100vw;
    --max-height: 100vh;
    --border-radius: 0;
  }
  
  .fullscreen-header {
    ion-toolbar {
      --padding-start: 16px;
      --padding-end: 16px;
      --min-height: 60px;
      
      ion-title {
        font-size: 1.3em; // Taille fixe
        font-weight: bold;
      }
      
      .close-button {
        --padding-start: 8px;
        --padding-end: 8px;
        
        ion-icon {
          color: white;
        }
      }
    }
  }
  
  .fullscreen-content {
    --padding-start: 0;
    --padding-end: 0;
    --padding-top: 0;
    --padding-bottom: 0;
    display: flex; // Utilise flexbox pour gérer la hauteur du contenu
    flex-direction: column; // Empile les sections verticalement
    flex-grow: 1; // Permet au contenu de prendre l'espace disponible
  }
  
  .product-info {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--ion-color-light);
    flex-shrink: 0; // Empêche cette section de rétrécir
    
    h2 {
      margin: 0 0 12px 0;
      font-size: 1.8em; // Taille fixe
      font-weight: bold;
      color: var(--ion-color-dark);
    }
    
    p {
      margin: 0 0 12px 0;
      color: var(--ion-color-medium);
      line-height: 1.5;
      font-size: 1.1em; // Taille fixe
    }
    
    .product-details {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      margin-top: 12px;
      
      .price-tag {
        background: var(--ion-color-success);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 1em; // Taille fixe
      }
      
      .stock-info {
        background: var(--ion-color-primary);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.9em; // Taille fixe
      }
    }
  }
  
  .documents-container {
    position: relative;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center; // Centre les éléments horizontalement
    flex-grow: 1; // Permet à ce conteneur de prendre l'espace restant
    overflow-y: auto; // Permet le défilement si le contenu dépasse
    justify-content: flex-start; // Aligne le contenu en haut
  }
  
  .carousel-navigation {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    z-index: 10;
    pointer-events: none;
    transform: translateY(-50%);
    display: flex; // Utilise flexbox pour positionner les boutons
    justify-content: space-between; // Écarte les boutons aux extrémités
    padding: 0 10px; // Un peu de padding pour les boutons
    width: 100%; // Prend toute la largeur du parent
    box-sizing: border-box; // Inclut le padding dans la largeur
    
    .nav-button {
      position: static; // Les boutons sont maintenant positionnés par flexbox
      pointer-events: all;
      width: 50px; // Taille fixe
      height: 50px; // Taille fixe
      border-radius: 50%;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      
      background: var(--ion-color-primary); // Couleur de fond bleue
      
      ion-icon {
        color: white !important; // Icône blanche
      }
      
      &:disabled {
        opacity: 0.4;
        background: var(--ion-color-medium); // Couleur désactivée plus claire
        
        ion-icon {
          color: rgba(255, 255, 255, 0.5) !important;
        }
      }
    }
  }
  
  .slide-container {
    text-align: center;
    width: 100%; // Prend toute la largeur disponible
    display: flex; // Utilise flexbox pour l'image et le nom
    flex-direction: column; // Empile l'image et le nom
    align-items: center; // Centre l'image et le nom horizontalement
    margin-bottom: 20px; // Espace entre l'image/nom et la pagination
  }
  
  .image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px; // Taille fixe pour l'espace de l'image
    width: 100%; // Prend toute la largeur disponible
  }
  
  .document-image {
    width: 100%;
    max-width: 90%; // L'image ne dépassera pas 90% de la largeur du conteneur
    height: auto;
    max-height: 60vh; // L'image ne dépassera pas 60% de la hauteur de la vue
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    background: white;
    display: block;
  }
  
  .document-name {
    margin: 20px 0 0 0;
    font-weight: 600;
    color: var(--ion-color-dark);
    font-size: 1.1em; // Taille fixe
    padding: 0 20px;
    word-break: break-word;
  }
  
  .pagination-dots {
    display: flex;
    justify-content: center;
    margin-top: 0; // Le margin-bottom du slide-container gère l'espacement
    gap: 12px;
    flex-shrink: 0; // Empêche cette section de rétrécir
    
    .dot {
      width: 14px; // Taille fixe
      height: 14px; // Taille fixe
      border-radius: 50%;
      background: rgba(var(--ion-color-primary-rgb), 0.3); // Couleur des points inactifs (bleu clair)
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      
      &.active {
        background: var(--ion-color-primary); // Couleur active (bleu foncé)
        transform: scale(1.3);
        border-color: var(--ion-color-primary-tint);
        box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.3);
      }
      
      &:hover:not(.active) {
        background: rgba(var(--ion-color-primary-rgb), 0.5); // Couleur au survol (bleu moyen)
        transform: scale(1.1);
      }
    }
  }
  
  .slide-counter {
    text-align: center;
    margin-top: 16px;
    font-size: 1.1em; // Taille fixe
    color: var(--ion-color-medium);
    font-weight: 600;
    background: rgba(var(--ion-color-light-rgb), 0.3);
    padding: 8px 16px;
    border-radius: 20px;
    display: inline-block;
    margin-left: auto;
    margin-right: auto;
    flex-shrink: 0; // Empêche cette section de rétrécir
  }
  
  .no-documents {
    text-align: center;
    padding: 80px 20px;
    color: var(--ion-color-medium);
    flex-grow: 1; // Prend l'espace restant
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    ion-icon {
      margin-bottom: 20px;
      opacity: 0.6;
      font-size: 4em; // Taille fixe
    }
    
    p {
      margin: 0;
      font-style: italic;
      font-size: 1.2em; // Taille fixe
    }
  }
  
  .fullscreen-footer {
    ion-toolbar {
      --padding-start: 20px;
      --padding-end: 20px;
      --padding-top: 12px;
      --padding-bottom: 12px;
      --min-height: 70px;
      flex-shrink: 0; // Empêche le footer de rétrécir
      
      .save-button {
        margin: 0;
        font-weight: bold;
        font-size: 1.1em; // Taille fixe
        --padding-top: 14px;
        --padding-bottom: 14px;
        
        ion-icon {
          margin-right: 8px;
        }
      }
    }
  }
  
  // Les media queries pour le padding et les marges générales sont conservées
  // car elles affectent l'espacement global et non les tailles d'éléments spécifiques.
  @media (max-width: 480px) {
    .product-info {
      padding: 16px;
      .product-details {
        flex-direction: column;
        gap: 8px;
      }
    }
    .documents-container {
      padding: 16px;
    }
    .carousel-navigation {
      padding: 0 5px; // Moins de padding sur mobile pour les boutons
    }
  }
  
  @media (min-width: 768px) {
    .product-info {
      padding: 24px 40px; // Plus de padding horizontal sur desktop
    }
    .documents-container {
      padding: 24px 40px; // Plus de padding horizontal sur desktop
    }
    .carousel-navigation {
      padding: 0 20px; // Ajuste le padding des boutons sur desktop
    }
  }
  
  @media (min-width: 1024px) {
    .product-info {
      padding: 32px 60px;
    }
    .documents-container {
      padding: 32px 60px;
    }
    .carousel-navigation {
      padding: 0 30px;
    }
  }
  