import { Component,OnInit} from '@angular/core';
import { Alert<PERSON>ontroller, IonicModule, LoadingController} from '@ionic/angular';
import { DateService } from 'src/app/services/date.service';
import { LoggerService } from 'src/app/services/logger.service';
import { SQLiteService } from 'src/app/services/sqlite.service';
import { CommonModule } from '@angular/common';
import { FormBuilder,Validators, FormGroup, FormsModule , ReactiveFormsModule} from '@angular/forms';
import { SQLiteDBConnection  } from '@capacitor-community/sqlite';
import { environment } from 'src/environments/environment';
import { CommonService } from 'src/app/services/common-service.service';
import { FilterService } from 'src/app/services/filterService.service';
import { ProspectService } from 'src/app/services/prospect-service.service';
import { ChartService } from 'src/app/services/chart-service.service';
import { activitie, establishment, localitie, potential, product, Prospect, sector, specialitie } from 'src/app/models/planning';
import { ThemeService } from 'src/app/services/theme.service';
import { TranslationService } from 'src/app/services/traduction-service.service';
import { LoginService } from 'src/app/services/login-service.service';
import { DbCreationTablesService } from 'src/app/services/db-creation-tables.service';
import { Chart, registerables } from 'chart.js';
import { HeaderComponent } from '../../header/header.component';
import { Subject, Subscription } from 'rxjs';

Chart.register(...registerables);


@Component({
  selector: 'app-planning',
  templateUrl: './planning.component.html',
  styleUrls: ['./planning.component.scss'],
  standalone : true ,
  imports : [IonicModule , CommonModule , FormsModule,ReactiveFormsModule,HeaderComponent],
})
export class PlanningComponent implements OnInit {
  planningForm!: FormGroup;
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  pageTitle: string = "Planning";
  dates: Date[] = [];
  dateSelected: Date = new Date();
  dupplicationDateSelected: Date = new Date();
  planningVisitDate: Date = new Date(); 
  selectedProspect: any;
  prospects:Prospect[] ;
  plannedVisits: any[]= [];
  specialities: specialitie[] = [];
  potentials: potential[] = [];
  localities: localitie[] = [];
  activities: activitie[] = [];
  sectors: sector[] = [];
  translations: any = {};
  private translationSubscription?: Subscription
  private languageSubscription?: Subscription
  currentLanguage = "an.json"
  destroy$ = new Subject<void>();
  establishments: establishment[] = [];
  products: product[] = [];
  isEnabled: boolean = false;
  lockAfterSync: boolean = false;
  showCharts: boolean = false;
  displayAddForm: boolean = false;
  dayIndex: number = 0; 
  sectorSelectedId: any;
  sectorSelected: any;
  localitySelected: any;
  establishmentSelected: any;
  specialitySelected: any;
  potentialSelected: any;
  activitySelected: any;
  productSelected: any;
  potentialProductSelected: any;
  showOnlyNonVistedProspects: boolean = false;
  showRecommendedProspects: boolean = false;
  inputsCurrent = {
    planningDate: 0,
    prospectId: '',
    prospectName: '',
  };
  selectedRow: number = -1;
  isPlanningFilled: boolean = false;
  nbPlannedVisits: number = 0;
  notes: string = '';
  status: string = 'NEW';
  currentProspect: string = "";
  callbackValueModel: any[] = [];
  haveRecommendation: boolean = true;
  turnClearAppData: string = 'Off'; 

  currentUser: any;
  LABELS = {
    PLANNING: 'Planning',
    WEEK_OF: 'Week of',
    STATUS: 'Status',
    NEW: 'New',
    WAITING_FOR_VALIDATION: 'Waiting for Validation',
    ACCEPTED: 'Accepted',
    TO_BE_REVIEWED: 'To Be Reviewed',
    REFUSED: 'Refused',
    PLANNING_IS_SENT: 'Planning has been sent',
    CHECK_PERIOD: 'Check period',
    DAY: 'day',
    AFTER_CURRENT_DATE: 'after the current date',
    DAYS: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    ADD_PLANNING: 'Add Planning',
    DELETE_ALL: 'Delete All',
    DUPLICATE: 'Duplicate',
    SHOW_CHARTS: 'Show Charts',
    COMMENT: 'Comment',
    SELECT_PROSPECTS: 'Select Prospects',
    ADD: 'Add',
    CANCEL: 'Cancel',
    SHOW_RECOMMENDED_PROSPECTS: 'Show Recommended Prospects',
    SHOW_ONLY_PROSPECT_VISITED: 'Show only non-visited prospects',
    PLANNED_VISITS_PORTFOLIO: 'Planned Visits Portfolio',
    PLANNED_BY_ACTIVITY: 'Planned by Activity',
    PLANNED_BY_SECTOR: 'Planned by Sector',
    PLANNED_BY_POTENTIAL: 'Planned by Potential',
    PLANNED_BY_SPECIALITY: 'Planned by Speciality',
    ALL: "All"
  };
  days = [
    { label: 'Monday' }, { label: 'Tuesday' }, { label: 'Wednesday' },
    { label: 'Thursday' }, { label: 'Friday' }, { label: 'Saturday' }
  ];
  openReportPeriod: string = ''; 
  lockAfterPeriod: boolean = false;
itemIndex!: number;

  constructor(
    private commonService: CommonService,
    private filterService: FilterService,
    private prospectService: ProspectService,
    private dateService: DateService,
    private chartService: ChartService,
    private fileLogger: LoggerService,
    private sqliteService: SQLiteService,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private formBuilder: FormBuilder,
    private translationService: TranslationService,private themeService: ThemeService,
    private LoginService : LoginService,
    private dbCreationTablesService: DbCreationTablesService,

  ) {
    this.fileLogger.setStorageFilename('PlanningComponent.log');  
    this.databaseName = environment.databaseNames.find(x => x.name.includes('tables'))?.name || 'default';

    this.prospects =[];
    console.log(this.prospects) 
    
    
  }
  
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    
}

  async ngOnInit() {
    this.mDb =  this.dbCreationTablesService.getDatabase();

   
   this.plannedVisits = [
    [{ prospectId: 1, prospectName: 'Prospect 1' }],
    [{ prospectId: 2, prospectName: 'Prospect 2' }]
  ];
  console.log('Planned Visits:', this.plannedVisits);
  this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
    console.log("🔄 planning - Traductions mises à jour:", translations)
    this.translations = translations
  })

  
  this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
    console.log("🌐 planning - Langue changée vers:", language)
    this.currentLanguage = language
  })
    try {
      this.fileLogger.info('Beginning of PlanningCtrl controller');
      this.dateSelected = this.dateService.getMondayOfWeek();
      this.initializeForm();
        const restorePreviousChoose = true; 
        this.specialities = await this.filterService.getAllSpecialities(restorePreviousChoose);
        this.potentials = await this.filterService.getAllPotential(restorePreviousChoose);
        this.activities = await this.filterService.getAllActivities(restorePreviousChoose);
        this.sectors = await this.filterService.getAllSectors(restorePreviousChoose);
        this.establishments = await this.filterService.getAllEstablishments(restorePreviousChoose);
  
      const currentDatePlanning = new Date();
    this.getStatusAndNotesOfWeek(currentDatePlanning);

      this.dupplicationDateSelected = this.dateService.getMondayOfWeek();

      this.getAllPlannedVisits();
   

      this.getAllProduct();
      
      this.getDateOfDay();
      
    } catch (error: unknown) {
      const e = error as Error;
      this.fileLogger.error(`Error in ngOnInit: ${e.message}`);
    }
    
  }
  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
    this.destroy$.next()
    this.destroy$.complete()
  }
  private initializeForm(): void {
    this.planningForm = this.formBuilder.group({
      dateSelected: [this.dateSelected, Validators.required],
      selectedProspect: [[], Validators.required],
      productSelected: [null],
      sectorSelected: [null],
      localitySelected: [null],
      specialitySelected: [null],
      potentialSelected: [null],
      activitySelected: [null],
      establishmentSelected: [null],
      potentialProductSelected: [null],
      showRecommendedProspects: [false],
      showOnlyNonVistedProspects: [false],
      notes: ['']
    });
  }
  

 /* async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService
      .addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
    // create and/or open the database
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);  
    const isData = await this.mDb.query("select * from sqlite_sequence");
    // create database initial data
    if (isData.values!.length === 0) {
      await this.createInitialData();
    }
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, true, "secret", this.loadToVersion, false);
    } else {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, false, "no-encryption", this.loadToVersion, false);
    }
  }
  private async createInitialData(): Promise<void> {
    // create 
    for (const pros of MOCK_PROSPECTS) {
      try {
        await this.getProspects(pros);
        console.log(`Processing prospect with id: ${pros.id}, firstname: ${pros.firstname}, lastname: ${pros.lastname}`);
      } catch (error) {
        console.error(`Error processing prospect with id ${pros.id}: ${error}`);
      }
    }
  }*/
  
    async openProspectDetails(prospectId: number) {
     
        const relatedData = await this.prospectService.openProspectDetails(prospectId);
    
      
          await this.prospectService.showProspectPopup(relatedData);
          console.log('Prospect details opened successfully for ID:', prospectId);
      


     
    }
    
  async getProspectById(prospectId: number): Promise<Prospect | null> {
    try {
      const prospect = await this.sqliteService.findOneBy(this.mDb, 'prospect', { id: prospectId });
      if (prospect) {
        return prospect as Prospect;
      } else {
        console.log(`No prospect found with ID: ${prospectId}`);
        return null;
      }
    } catch (error) {
      console.error(`Error retrieving prospect with ID: ${prospectId}`, error);
      return null;
    }
  }


  async getDateOfDay() {
    try {
      if (!this.dateSelected) {
        return;
      }

      this.dates = [];
      this.planningVisitDate = new Date(this.dateSelected);

      for (let i = 0; i < 6; i++) {
        let nextDate = new Date(this.planningVisitDate);
        nextDate.setDate(nextDate.getDate() + i);
        this.dates.push(nextDate);
      }
    } catch (error: unknown) {
      const e = error as Error;
      this.fileLogger.error(`Error in getDateOfDay: ${e.message}`);
    }
  }

  async openDatePicker() {
    try {
      const alert = await this.alertController.create({
        header: 'Select Date',
        inputs: [
          {
            name: 'date',
            type: 'date',
            value: this.dateService.getMondayOfWeek().toISOString().split('T')[0]
          }
        ],
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'OK',
            handler: (data) => {
              const selectedDate = new Date(data.date);
            
              // Check if the selected date is a Monday
              if (selectedDate.getDay() !== 1) {
                this.alertController.create({
                  header: 'Invalid Date',
                  message: 'Please select a Monday.',
                  buttons: ['OK']
                }).then(alert => alert.present());
                return; // Exit the handler if the date is not a Monday
              }
  
              // Update the date only if it is valid
              this.dateSelected = selectedDate;
              this.getStatusAndNotesOfWeek(this.dateSelected);
              this.getAllPlannedVisits();
              this.getDateOfDay();
              this.checkOpenPlanningPeriod(this.dateSelected);
              this.showCharts=false;
              this.displayAddForm=false;
              this.inputsCurrent = {
                planningDate: 0,
                prospectId: '',
                prospectName: '',
              };
            }
          }
        ]
      });
  
      await alert.present();
    } catch (error: unknown) {
      const e = error as Error;
      this.fileLogger.error(`Error in openDatePicker: ${e.message}`);
    }
  }
  
async filterLocalitiesBySector(sectorId: number) {
  console.log("sector",sectorId)
   this.localities=await this.filterService.filterLocalitiesBySector(sectorId);
}

  async filterEstablishmentByActivity(activityId: string) {
   this.establishments=await this.filterService.filterEstablishmentsByActivity(activityId);
  }
  async filterProspects(
    sectorSelected: any,
    localitySelected: any,
    establishmentSelected: any,
    specialitySelected: any,
    potentialSelected: any,
    activitySelected: any,
    productSelected: any,
    potentialProductSelected: any,
    showOnlyNonVistedProspect: boolean
  ) {
   try {
      await this.getNormalSearchProspects(
        sectorSelected,
        localitySelected,
        establishmentSelected,
        specialitySelected,
        potentialSelected,
        activitySelected,
        productSelected,
        potentialProductSelected,
        showOnlyNonVistedProspect
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.fileLogger.error(`Error in filterProspects: ${error.message}`);
      } else {
        this.fileLogger.error(`Unknown error in filterProspects: ${String(error)}`);
      }
    }
  }
  
  async getNormalSearchProspects(
    sectorSelected: any, localitySelected: any, establishmentSelected: any,
    specialitySelected: any, potentialSelected: any, activitySelected: any,
    productSelected: any, potentialProductSelected: any, showOnlyNonVisitedProspects: boolean
  ): Promise<void> {
    
    this.sectorSelected = sectorSelected;
    this.localitySelected = localitySelected;
    this.establishmentSelected = establishmentSelected;
    this.specialitySelected = specialitySelected;
    this.potentialSelected = potentialSelected;
    this.activitySelected = activitySelected;
  
    
    if (productSelected) {
      this.isEnabled = productSelected.name !== this.LABELS.ALL;
    }
  
    if (potentialProductSelected && potentialProductSelected.name === this.LABELS.ALL) {
      potentialProductSelected = null;
    }
    
    const selectQuery = await this.commonService.getFilterQuery(
      sectorSelected, localitySelected, establishmentSelected,
      specialitySelected, potentialSelected, activitySelected,
      productSelected, potentialProductSelected, "", this.dateSelected,
      null, false, showOnlyNonVisitedProspects ? "MONTH" : "", "WEEK", false
    );
    const selectParams = await this.commonService.getFilterQueryParams(
      sectorSelected, localitySelected, establishmentSelected,
      specialitySelected, potentialSelected, activitySelected,
      "", productSelected, potentialProductSelected
    );
    const loading = await this.loadingController.create({
      message: 'Loading...'
    });
    await loading.present();
    
    try {
      const rs: any = await this.mDb.query(selectQuery, selectParams);
      this.prospects = rs.values.map((row: any) => ({
        id: row.id,
        firstname: row.firstname,
        lastname: row.lastname,
        name: `${row.firstname} ${row.lastname}`
      }));
      console.log('Prospects:', this.prospects); 
    
    } catch (err: any) {
      this.fileLogger.error(`For the request: ${selectQuery} ${err.message}`);
    } finally {
      loading.dismiss();
    }
  }
  
  async checkIfPlanningIsLocked(isSynchronized: boolean) {
    this.lockAfterSync = false;
  
    const userQuery = 'SELECT * FROM user';
  
    try {
      const result = await this.mDb.query(userQuery);
  
      if (result && result.values && result.values.length > 0) {
        this.currentUser = result.values[0];
  
        if (this.currentUser && this.currentUser.lock_after_sync === 'true' && isSynchronized) {
          this.lockAfterSync = true;
        }
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(`Error in checkIfPlanningIsLocked: ${error.message}`);
      } else {
        console.error('Unknown error occurred in checkIfPlanningIsLocked');
      }
    }
  }
  
  

  checkOpenPlanningPeriod(selectedTabDate: Date) {
    try {
      this.commonService.checkOpenPeriod(selectedTabDate, 'PLANNING');

      if (this.lockAfterSync) {
        this.displayAddForm = false;
      }
    } catch (error: unknown) {
      const e = error as Error;
      this.fileLogger.error(`Error in checkOpenPlanningPeriod: ${e.message}`);
    }
  }
showchart(){
  this.drawCharts()
  this.showCharts = true;
  this.displayAddForm = false;
}
  async drawCharts() {
   
    
    
    var startDateTimestamp = new Date(this.dateSelected).setHours(0, 0, 0, 0);
    var endDateTimestamp = new Date(this.dateSelected).setHours(23, 59, 59, 0);
    endDateTimestamp=this.dateService.addDays(new Date(endDateTimestamp),7).getTime();
    await this.chartService.drawPlannedVsAllProspectChart('planningCoverageChart', startDateTimestamp, endDateTimestamp);
    await this.chartService.drawPlanningChart('planningBySpecialityChart', startDateTimestamp, endDateTimestamp, "SPECIALITY");
    await this.chartService.drawPlanningChart('planningByPotentialChart', startDateTimestamp, endDateTimestamp, "POTENTIAL");
    await this.chartService.drawPlanningChart('planningBySectorChart', startDateTimestamp, endDateTimestamp, "SECTOR");
    await this.chartService.drawPlanningChart('planningByActivityChart', startDateTimestamp, endDateTimestamp, "ACTIVITY");
  }
  async dupplicate() {
    try {
      const alert = await this.alertController.create({
        header: 'Duplicate Planning',
        message: 'Are you sure you want to duplicate the planning?',
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'Duplicate',
            handler: () => {
              this.openDupplicationDatePicker();
            }
          }
        ]
      });
  
      await alert.present();
    } catch (error: unknown) {
      const e = error as Error;
      console.log(`Error in dupplicate: ${e.message}`);  // Log more detailed error
      this.fileLogger.error(`Error in dupplicate: ${e.message}`);
    }
  }
  
  async openDupplicationDatePicker() {
    try {
      const alert = await this.alertController.create({
        header: 'Select Duplication Date',
        inputs: [
          {
            name: 'date',
            type: 'date',
            value: this.dupplicationDateSelected.toISOString().split('T')[0]
          }
        ],
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'OK',
            handler: (data) => {
              const selectedDate = new Date(data.date);
  
              // Check if the selected date is a Monday
              if (selectedDate.getDay() !== 1) {
                // Show an alert if the selected date is not a Monday
                this.alertController.create({
                  header: 'Invalid Date',
                  message: 'Please select a Monday.',
                  buttons: ['OK']
                }).then(alert => alert.present());
                return; // Exit the handler if the date is not a Monday
              }
  
              // Proceed with duplication if the date is a valid Monday
              this.dupplicationDateSelected = selectedDate;
              this.dupplicatePlanning();
            }
          }
        ]
      });
  
      await alert.present();
    } catch (error: unknown) {
      const e = error as Error;
      console.log(`Error in openDupplicationDatePicker: ${e.message}`);
      this.fileLogger.error(`Error in openDupplicationDatePicker: ${e.message}`);
    }
  }
  
  
  async dupplicatePlanning() {
    let currentIndex = 0;
  
    try {
      // Loop through planned visits and save each day
      for (let dayNumber = 0; dayNumber < 6; dayNumber++) {
       
        for (const visit of this.plannedVisits[dayNumber]) {
          console.log('Visit:', visit);
          await this.saveDay(visit.prospectId, dayNumber, this.dupplicationDateSelected, false);
          currentIndex++;
        }
      }
  
      // Show success alert after duplication
      const alert = await this.alertController.create({
        header: 'Schedule Duplication',
        message: 'Schedule duplicated successfully',
        buttons: ['OK']
      });
      await alert.present();
  
      // Prepare data for planning validation
      const formattedDate = this.dupplicationDateSelected.toISOString();
      const insertQuery = `INSERT OR REPLACE INTO planning_validation (id, notes, planning_validation_date, status, synchronized) VALUES (?,?,?,'NEW',1)`;
  
      // Insert the new planning validation
      await this.mDb.run(insertQuery, [ Math.floor(new Date().getTime() / 1000), "", formattedDate]);
  
    } catch (error: unknown) {
      const e = error as Error;
      console.log('Error inserting new planning validation:', e.message);  // More detailed error logging
      this.fileLogger.error(`Error inserting new planning validation: ${e.message}`);
  
      // Show error alert to the user
      const errorAlert = await this.alertController.create({
        header: 'Operation Failed',
        message: 'Planning has not been added',
        buttons: ['OK']
      });
      await errorAlert.present();
    }
  }
  

  
  async deleteAll(): Promise<void> {
    try {
      const alert = await this.alertController.create({
        header: 'Delete All',
        message: 'Are you sure you want to delete all scheduled visits for this week?',
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'Delete',
            handler: async () => {
              const startDate = new Date(this.dateSelected);
              startDate.setHours(0, 0, 0, 0);
              let synchronizedStatus = 0;
              const selectSyncStatusQuery = 'SELECT synchronized FROM planning_validation WHERE planning_validation_date = ?';
  
              // Check synchronization status for the selected date
              const validationResult = await this.mDb.query(selectSyncStatusQuery, [startDate.getTime()]);
  
              if (validationResult.values && validationResult.values.length > 0) {
                synchronizedStatus = validationResult.values[0].synchronized;
              }
  
              let query: string;
              if (synchronizedStatus === 1) {
                // If synchronized, mark the entries as deleted
                query = 'UPDATE planning SET status = "DELETED" WHERE planning_date = ?';
              } else {
                // If not synchronized, delete the entries outright
                query = 'DELETE FROM planning WHERE planning_date = ?';
              }
  
              // Execute the query for each day of the week
              let currentDate = new Date(startDate);
              for (let i = 0; i < 6; i++) {
                await this.mDb.run(query, [currentDate.getTime()]);
                currentDate.setDate(currentDate.getDate() + 1); // Move to the next day
              }
  
              // Handle validation table updates based on synchronization
              const validationQuery = synchronizedStatus === 1
                ? 'UPDATE planning_validation SET status = "DELETED" WHERE planning_validation_date = ?'
                : 'DELETE FROM planning_validation WHERE planning_validation_date = ?';
  
              await this.mDb.run(validationQuery, [startDate.getTime()]);
              if (this.sqliteService.platform === 'web') {
                await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
                this.fileLogger.info('Changes saved to store for web platform');
              }
  
              // Refresh the view and reset charts
              this.getAllPlannedVisits();
              this.showCharts = false;
            }
          }
        ]
      });
  
      await alert.present();
    } catch (error: unknown) {
      const e = error as Error;
      this.fileLogger.error(`Error in deleteAll: ${e.message}`);
    }
  }
  


  
 
  async getAllProduct() {
    try {
      const query = 'SELECT * from product order by name';
      const products: { id: number ; name: string }[] = [];
      
      
      const result = await this.mDb.query(query);

    
      for (let i = 0; i < result.values!.length; i++) {
        products.push({
          id: result.values![i].id,
          name: result.values![i].name,
        });
      }

      this.products = products;
    } catch (error: any) {
      console.error('Error loading products:', error);
    }
  }
  

  async getStatusAndNotesOfWeek(currentDatePlanning: Date) {
    this.notes = '';
    this.status = 'NEW';

    const selectQuery = `
      SELECT vp.status, vp.notes, vp.synchronized 
      FROM planning_validation vp 
      WHERE vp.planning_validation_date = ?
    `;

    const timestamp = currentDatePlanning.setHours(0, 0, 0, 0);

    try {
      
      const result = await this.mDb.query(selectQuery, [timestamp]);

      if (result && result.values) {
        if (result.values.length > 0) {
          const row = result.values[0];
          this.notes = row.notes;
          this.status = row.status;
          const synchronized = row.synchronized;
          this.checkIfPlanningIsLocked(synchronized === 1);

          if (this.status === 'DELETED') {
            this.status = 'NEW';
          }
        }
      }
    } catch (error: unknown) {
      const e = error as Error;
      console.error(`Error in getStatusAndNotesOfWeek: ${e.message}`);
    }
  }

  async getAllPlannedVisits() {
    this.isPlanningFilled = false;
    const currentDatePlannedVisits = new Date(this.dateSelected);
    this.plannedVisits = [];

    for (let dayNumber = 0; dayNumber < 6; dayNumber++) {
      this.plannedVisits[dayNumber] = [];
      this.getPlanningByDay(dayNumber, currentDatePlannedVisits);
      currentDatePlannedVisits.setDate(currentDatePlannedVisits.getDate() + 1);
    }
  }
  
 
  async showProspectsForPlanning(
    dayIndex: number,
    showRecommendedProspects: boolean,
    sectorSelected: any,
    localitySelected: any,
    establishmentSelected: any,
    specialitySelected: any,
    potentialSelected: any,
    activitySelected: any,
    productSelected: any,
    potentialProductSelected: any,
    showOnlyNonVisitedProspects: boolean
  ): Promise<void> {
   this.selectedRow = dayIndex;
    if (showRecommendedProspects) {
      await this.getRecommendedProspects(productSelected);
    } else {
      await this.clickRow(
        dayIndex, sectorSelected, localitySelected, establishmentSelected, specialitySelected,
        potentialSelected, activitySelected, productSelected, potentialProductSelected, showOnlyNonVisitedProspects
      );
    }
  }


 async getRecommendedProspects(productSelected: any) {
    const loading = await this.loadingController.create({
      message: 'Loading...'
    });
    await loading.present();

    let selectQuery = `
      SELECT p.id, p.firstname, p.lastname, 
      SUM(pop.order_quantity * prd.price) AS salesRevenuePrediction 
      FROM prospect_order_prediction pop 
      JOIN prospect p ON pop.prospect_id = p.id 
      JOIN product prd ON pop.product_id = prd.id
    `;
    const params: any[] = [];

    if (productSelected && productSelected.id) {
      selectQuery += ` WHERE pop.product_id = ?`;
      params.push(productSelected.id);
    }
    selectQuery += ` GROUP BY p.id ORDER BY salesRevenuePrediction DESC`;
    try {
      const result = await this.mDb.query(selectQuery, params);
      if (result && result.values) {
        this.prospects = result.values.map((row: any) => ({
          id: row.id,
          firstname: row.firstname,
          lastname: row.lastname,
          name: `${row.firstname} ${row.lastname} (${row.salesRevenuePrediction.toFixed(3)} Dinars)`
        }));
      }
    } catch (error: unknown) {
      const e = error as Error;
      console.error(`Error in getRecommendedProspects: ${e.message}`);
    } finally {
      await loading.dismiss();
    }
  }
  async getProspects(jsonProspect: Prospect): Promise<Prospect> {
    let prospect = await this.sqliteService.findOneBy(this.mDb, "prospect", { id: jsonProspect.id });
  
    if (!prospect) {
      if (jsonProspect.firstname && jsonProspect.lastname) {
        
        let newProspect = new Prospect();
        newProspect.id = jsonProspect.id;
        newProspect.firstname = jsonProspect.firstname;
        newProspect.lastname = jsonProspect.lastname;
  
        await this.sqliteService.save(this.mDb, "prospect", newProspect);
      
        newProspect = await this.sqliteService.findOneBy(this.mDb, "prospect", { id: jsonProspect.id });
        if (newProspect) {
          return newProspect;
        } else {
          return Promise.reject(`failed to getProspects for id ${jsonProspect.id}`);
        }
      } else {
        
        let emptyProspect = new Prospect();
        emptyProspect.id = -1;
        return emptyProspect;
      }
    } else {
      return prospect;
    }
  }
  

async clickRow(
  dayIndex: number,
  sectorSelected: any,
  localitySelected: any,
  establishmentSelected: any,
  specialitySelected: any,
  potentialSelected: any,
  activitySelected: any,
  productSelected: any,
  potentialProductSelected: any,
  showOnlyNonVisitedProspects: boolean
): Promise<void> {
  console.log('sectors ', this.sectors);
  this.displayAddForm = true;
  this.showCharts = false;
  this.selectedRow = dayIndex;

 
  await this.getNormalSearchProspects(
    sectorSelected, localitySelected, establishmentSelected, specialitySelected,
    potentialSelected, activitySelected, productSelected, potentialProductSelected, showOnlyNonVisitedProspects
  );
  this.selectedProspect = [];
    this.planningForm.get('prospectSelect')?.setValue([]);
    
    // Check planning period
    if (this.selectedRow !== -1) {
      const selectedTabDate = await this.dateService.getDateOfDay(this.selectedRow, this.dateSelected);
      await this.checkOpenPlanningPeriod(selectedTabDate);
    } else {
      await this.checkOpenPlanningPeriod(this.dateSelected);
    }

  if (this.selectedRow !== -1) {
    const selectedTabDate = await this.dateService.getDateOfDay(this.selectedRow, this.dateSelected);
    await this.checkOpenPlanningPeriod(selectedTabDate);
  } else {
    await this.checkOpenPlanningPeriod(this.dateSelected);
  }
}

onProspectChange(event: any) {
  console.log('Selected prospect:', event.detail.value);
  this.selectedProspect = event.detail.value; 

}

  async prospectItemsRemoved(callback: { item: any }) {
    const ionAutocompleteElement = document.getElementById("prospect-autocomplete");
    if (ionAutocompleteElement) {
      const searchItems = (ionAutocompleteElement as any).controller('ionAutocomplete').searchItems;
      searchItems.push(callback.item);
  
      for (let i = 0; i < this.selectedProspect.length; i++) {
        if (this.selectedProspect[i].id === callback.item.id) {
          this.selectedProspect.splice(i, 1);
          break;
        }
      }
    }
  }
  onSectorChange(event: any) {
    this.sectorSelected = event.detail.value;
    console.log('Selected sector:', this.sectorSelected.id);

    // Call methods to filter localities and get prospects based on the selected sector
    this.filterLocalitiesBySector(this.sectorSelected.id);
    this.getNormalSearchProspects(
      this.sectorSelected,
      this.localitySelected,
      this.establishmentSelected,
      this.specialitySelected,
      this.potentialSelected,
      this.activitySelected,
      this.productSelected,
      this.potentialProductSelected,
      this.showOnlyNonVistedProspects
    );
  }
  onActivityChange(event: any) {
    this.activitySelected = event.detail.value;
    console.log('Selected activity:', this.activitySelected);

    // Call methods to filter establishments by activity and get prospects
    this.filterEstablishmentByActivity(this.activitySelected.id);
    this.getNormalSearchProspects(
      this.sectorSelected,
      this.localitySelected,
      this.establishmentSelected,
      this.specialitySelected,
      this.potentialSelected,
      this.activitySelected,
      this.productSelected,
      this.potentialProductSelected,
      this.showOnlyNonVistedProspects
    );
  }
  onPotentialChange(event: any) {
    this.potentialSelected = event.detail.value;
    console.log('Selected potential:', this.potentialSelected);

    // Call the method to fetch prospects based on the selected potential
    this.getNormalSearchProspects(
      this.sectorSelected,
      this.localitySelected,
      this.establishmentSelected,
      this.specialitySelected,
      this.potentialSelected,
      this.activitySelected,
      this.productSelected,
      this.potentialProductSelected,
      this.showOnlyNonVistedProspects
    );
  }
  onSpecialityChange(event: any) {
    this.specialitySelected = event.detail.value;
    console.log('Selected speciality:', this.specialitySelected);

    // Call the method to fetch prospects based on the selected speciality
    this.getNormalSearchProspects(
      this.sectorSelected,
      this.localitySelected,
      this.establishmentSelected,
      this.specialitySelected,
      this.potentialSelected,
      this.activitySelected,
      this.productSelected,
      this.potentialProductSelected,
      this.showOnlyNonVistedProspects
    );
  }
  onLocalityChange(event: any) {
    this.localitySelected = event.detail.value;
    console.log('Selected locality:', this.localitySelected);

    // Call the method to fetch prospects based on the selected locality
    this.getNormalSearchProspects(
      this.sectorSelected,
      this.localitySelected,
      this.establishmentSelected,
      this.specialitySelected,
      this.potentialSelected,
      this.activitySelected,
      this.productSelected,
      this.potentialProductSelected,
      this.showOnlyNonVistedProspects
    );
  }
  onEstablishmentChange(event: any) {
    this.establishmentSelected = event.detail.value;
    console.log('Selected establishment:', this.establishmentSelected);

    // Call the method to fetch prospects based on the selected establishment
    this.getNormalSearchProspects(
      this.sectorSelected,
      this.localitySelected,
      this.establishmentSelected,
      this.specialitySelected,
      this.potentialSelected,
      this.activitySelected,
      this.productSelected,
      this.potentialProductSelected,
      this.showOnlyNonVistedProspects
    );
  }
  onProductChange(event: any) {
    this.productSelected = event.detail.value;
    console.log('Selected product:', this.productSelected);

    // Call the method to fetch prospects based on the selected product
    this.getNormalSearchProspects(
      this.sectorSelected,
      this.localitySelected,
      this.establishmentSelected,
      this.specialitySelected,
      this.potentialSelected,
      this.activitySelected,
      this.productSelected,
      this.potentialProductSelected,
      this.showOnlyNonVistedProspects
    );
  }
  async save(): Promise<void> {
    if (this.selectedProspect && this.selectedProspect.length > 0) {
        console.log('Saving selected Prospect:', this.selectedProspect);
        

       
        if (this.selectedRow === -1) {
            let currentIndex = 0;
            let sundayProspectsCount = 0;

            try {
                const rs = await this.mDb.query('SELECT work_type, working_days FROM user', []);
                const currentUser = rs.values && rs.values.length > 0 ? rs.values[0] : null;

                if (currentUser && currentUser.workingDays === 6) {
                    
                    sundayProspectsCount = (this.selectedProspect.length < 11 && this.selectedProspect.length > 5) ? 1 : Math.floor(this.selectedProspect.length / 11);

                 
                    while (currentIndex < sundayProspectsCount) {
                        await this.saveDay(this.selectedProspect[currentIndex], 5, this.dateSelected, true);
                        currentIndex++;
                    }
                }

                const prospectPerDayCount = Math.floor((this.selectedProspect.length - sundayProspectsCount) / 5);
                if (prospectPerDayCount > 0) {
                   
                    for (let i = 0; i < 5; i++) {
                        for (let j = 0; j < prospectPerDayCount; j++) {
                            if (currentIndex < this.selectedProspect.length) {
                                await this.saveDay(this.selectedProspect[currentIndex], i, this.dateSelected, true);
                                currentIndex++;
                            }
                        }
                    }
                }
                let i = 0;
                while (currentIndex < this.selectedProspect.length) {
                    await this.saveDay(this.selectedProspect[currentIndex], i, this.dateSelected, true);
                    currentIndex++;
                    i = (i + 1) % 5; 
                }
                
            } catch (err) {
                console.error('Error saving selected prospects:', err);
            }
        } else {
         
            let currentIndex = 0;
            try {
                for (let j = 0; j < this.selectedProspect.length; j++) {
                    await this.saveDay(this.selectedProspect[currentIndex], this.selectedRow, this.dateSelected, true);
                    currentIndex++;
                }
            } catch (err) {
                console.error('Error saving day:', err);
            }
        }

        const selectIdOfValidation = 'SELECT pv.id FROM planning_validation pv WHERE pv.status <> "DELETED" AND pv.planning_validation_date = ?';
        try {
            const planningValidationDate = new Date(this.dateSelected).setHours(0, 0, 0, 0); 
            const rs = await this.mDb.query(selectIdOfValidation, [planningValidationDate]);
            if (rs.values && rs.values.length === 0) {
                await this.mDb.run('INSERT INTO planning_validation (id, notes, status, planning_validation_date, synchronized) VALUES (?, ?, ?, ?, ?)', [Date.now().toString(), '', 'NEW', planningValidationDate, 0]);
            } else if (rs.values && rs.values.length > 0) {
                await this.mDb.run('UPDATE planning_validation SET synchronized = ?, status = ? WHERE id = ?', [0, 'NEW', rs.values[0].id.toString()]);
            }
        } catch (err) {
            console.error('Error updating planning validation:', err);
        }
        if (this.sqliteService.platform === 'web') {
          try {
              await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
              console.log('Database saved to store for web platform.');
          } catch (err) {
              console.error('Error saving database to store for web platform:', err);
          }
      }
    } else {
        console.error('No valid Prospect selected to save');
    }
  this.displayAddForm =false;
}

async saveDay(prospectId: any, selectedRow: number, dateSelected: Date, updateCalendarView: boolean): Promise<void> {
  console.log('Saving day for Prospect ID:', prospectId, 'on Row:', selectedRow);

  this.isPlanningFilled = true;

  // Reset inputsCurrent
  this.inputsCurrent = {
    planningDate: 0,
    prospectId: '',
    prospectName: ''
  };

  // Ensure prospectId is a number, extract from object if necessary
  if (typeof prospectId === 'object' && prospectId.id) {
    prospectId = prospectId.id; // Extract id if prospectId is an object
  }

  if (typeof prospectId !== 'number') {
    console.error('Invalid prospect ID:', prospectId);
    return;  // Stop execution if prospectId is not valid
  }

  // Calculate the planning date based on the selected date and row
  const planningVisitDate = new Date(dateSelected);
  planningVisitDate.setDate(planningVisitDate.getDate() + selectedRow);
  const planningDate = planningVisitDate.getTime();

  // Generate a custom ID for planning
  const id = new Date().getTime() + (prospectId * Math.floor(Math.random() * 100));

  try {
    // Ensure all values passed to the SQL query are primitive types
    const status = 'NEW';
    const synchronized = 0;

    // Insert the new planning entry into the database
    await this.mDb.run(
      'INSERT INTO planning (id, prospect_id, planning_date, status, synchronized) VALUES (?, ?, ?, ?, ?)',
      [id, prospectId, planningDate, status, synchronized]
    );

    if (updateCalendarView) {
      // Fetch the prospect name from the database
      const result = await this.mDb.query('SELECT firstname, lastname FROM prospect WHERE id = ?', [prospectId]);

      if (result && result.values && result.values.length > 0) {
        const prospectName = result.values[0].firstname + ' ' + result.values[0].lastname;

        // Update inputsCurrent with the retrieved prospect name
        this.inputsCurrent = {
          planningDate,
          prospectId: prospectId.toString(),
          prospectName
        };

        // Initialize or update planned visits for the selected row
        if (!this.plannedVisits[selectedRow]) {
          this.plannedVisits[selectedRow] = [];
        }

        this.plannedVisits[selectedRow].push({
          ...this.inputsCurrent,
          id // Use the generated ID
        });

        console.log('Planned Visits:', this.plannedVisits);

        // Reset inputsCurrent
        this.inputsCurrent = {
          planningDate: 0,
          prospectId: '',
          prospectName: ''
        };
      } else {
        throw new Error('Failed to fetch prospect name');
      }
    }
    if (this.sqliteService.platform === 'web') {
      try {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        console.log('Database saved to store for web platform.');
      } catch (err) {
        console.error('Error saving database to store for web platform:', err);
      }
    }
  } catch (err) {
    // Handle errors gracefully and show an alert
    const alert = await this.alertController.create({
      header: 'Operation Failed',
      message: 'Planning has not been added',
      buttons: ['OK']
    });
    await alert.present();
    console.error('Error saving day:', err);
  }
}


   
  checkValidity(): boolean {
    return !(this.inputsCurrent.prospectId && this.inputsCurrent.prospectId.length > 0);
  }

  cancel() {
    this.displayAddForm = false;
    this.inputsCurrent = {
      planningDate: 0,
      prospectId: '',
      prospectName: ''
    };
    this.selectedProspect = [];
    this.planningForm.reset();

  }
  async getPlanningByDay(dayNumber: number, currentDatePlanning: Date) {
    try {
      const selectQuery = `
        SELECT DISTINCT(pr.firstname || ' ' || pr.lastname) AS prospectName, p.id, pr.id AS prospectId, p.planning_date 
        FROM planning p
        JOIN prospect pr ON p.prospect_id = pr.id
        WHERE (p.planning_date >= ?) AND (p.planning_date <= ?) AND (p.status <> 'DELETED')
        ORDER BY prospectName
      `;
      
      const timestamp1 = new Date(currentDatePlanning).setHours(0, 0, 0, 0);
      const timestamp2 = new Date(currentDatePlanning).setHours(23, 59, 59, 0);

      const result = await this.mDb.query(selectQuery, [timestamp1, timestamp2]);

      if (result && result.values) {
        for (let i = 0; i < result.values.length; i++) {
          const item = result.values[i];
          if (!this.plannedVisits[dayNumber]) {
            this.plannedVisits[dayNumber] = [];
          }
          this.plannedVisits[dayNumber].push(item);
        }
  
        if (result.values.length > 0) {
          this.isPlanningFilled = true;
        }
      }
    } catch (error: unknown) {
      const e = error as Error;
      this.fileLogger.error(`Error in getPlanningByDay: ${e.message}`);
    }
  }

  calculateTotalPlannedProspect() {
    this.nbPlannedVisits = 0;
    for (let i = 0; i < this.plannedVisits.length; i++) {
      this.nbPlannedVisits += this.plannedVisits[i].length;
    }
  }
  async clickShowConfDeleted(dayIndex: number, itemIndex: number, item: any) {
    console.log('clickShowConfDeleted - item:', item); 

    const confirmPopup = await this.alertController.create({
      header: 'Confirmation',
      message: 'Are you sure you want to delete this schedule?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
        },
        {
          text: 'OK',
          handler: () => this.deleteItem(dayIndex, itemIndex, item),
        },
      ],
    });
    await confirmPopup.present();
  }
  
  async deleteItem(dayIndex: number, itemIndex: number, planning: any) {
    console.log('deleteItem - planning:', planning);
    try {
      if (!planning || !planning.id) {
        throw new Error('Invalid planning object or missing id');
      }

      const selectQuery = "SELECT synchronized FROM planning WHERE id = ?";
      const result = await this.mDb.query(selectQuery, [planning.id]);
  
      if (result && result.values && result.values.length > 0) {
        const synchronized = result.values[0].synchronized;
        let deletePlanningQuery = '';
  
        if (synchronized === 0) {
          deletePlanningQuery = "DELETE FROM planning WHERE id = ?";
        } else {
          deletePlanningQuery = "UPDATE planning SET status = 'DELETED' WHERE id = ?";
        }
  
        await this.mDb.query(deletePlanningQuery, [planning.id]);
        this.fileLogger.info('Planned visit deleted successfully');
  
        if (dayIndex >= 0 && dayIndex < this.plannedVisits.length) {
          this.plannedVisits[dayIndex].splice(itemIndex, 1);
        }
  
        let emptyDaysCount = 0;
        for (let dayNumber = 0; dayNumber < this.plannedVisits.length; dayNumber++) {
          if (this.plannedVisits[dayNumber].length === 0) {
            emptyDaysCount++;
          }
        }
  
        if (emptyDaysCount === this.plannedVisits.length) {
          this.status = "NEW";
  
          const selectValidationQuery = "SELECT synchronized FROM planning_validation WHERE planning_validation_date = ?";
          const validationResult = await this.mDb.query(selectValidationQuery, [this.dateSelected.setHours(0, 0, 0, 0)]);
  
          let planningValidationToDelete = '';
  
          if (validationResult && validationResult.values && validationResult.values.length > 0) {
            const validationSynchronized = validationResult.values[0].synchronized;
  
            if (validationSynchronized === 0) {
              planningValidationToDelete = "DELETE FROM planning_validation WHERE planning_validation_date = ?";
            } else {
              planningValidationToDelete = "UPDATE planning_validation SET status = 'DELETED' WHERE planning_validation_date = ?";
            }
  
            await this.mDb.query(planningValidationToDelete, [this.dateSelected.setHours(0, 0, 0, 0)]);
            this.fileLogger.info('Planning validation deleted successfully');
          }
        } else {
          const updateStatusQuery = "UPDATE planning_validation SET synchronized = 0 WHERE planning_validation_date = ?";
          await this.mDb.query(updateStatusQuery, [this.dateSelected.setHours(0, 0, 0, 0)]);
          this.fileLogger.info('Planning validation updated successfully after deletion of a planning item');
        }
        if (this.sqliteService.platform === 'web') {
          await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
          this.fileLogger.info('Changes saved to store for web platform');
        }
        this.showCharts=false;
      }
    } catch (error: unknown) {
      const e = error as Error;
      this.fileLogger.error(`Error in deleteItem: ${e.message}`);
    }
  }
  
  
    logout() {
      this.LoginService.logout();
    }
    onThemeChange(): void {
      this.themeService.switchTheme();
    }
    translate(key: string): string {
      const translation = this.translations[key]
      if (!translation) {
        console.warn(`⚠️ planning - Traduction manquante: ${key}`)
      }
      return translation || key
    }
  
    async changeLanguage(lang: string) {
      try {
        console.log("🌐 planing - Demande changement langue vers:", lang)
        await this.translationService.changeLanguage(lang)
        console.log("✅ planing - Langue changée avec succès")
      } catch (error) {
        console.error("❌ planing - Erreur changement langue:", error)
      }
    }
  
    private loadTranslations(lang: string) {
      this.translationService.loadTranslations(lang).subscribe(
        (translations) => {
          this.translations = translations;
          
        },
        (error) => {
          console.error(`Error loading translations for ${lang}`, error);
        }
      );
    }
  
 }