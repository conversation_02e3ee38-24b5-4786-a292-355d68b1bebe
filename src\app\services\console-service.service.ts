import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface ConsoleMessage {
  message: string;
  type: 'log' | 'error' | 'info' | 'warn' | 'debug' | 'trace';
  timestamp: Date;
  color: string;
}

@Injectable({
  providedIn: 'root'
})
export class ConsoleService {
  private showConsoleSubject = new BehaviorSubject<boolean>(false);
  private messagesSubject = new BehaviorSubject<ConsoleMessage[]>([]);
  
  public showConsole$ = this.showConsoleSubject.asObservable();
  public messages$ = this.messagesSubject.asObservable();
  
  private originalConsole: any = {};
  private messages: ConsoleMessage[] = [];

  constructor() {
    this.loadConsoleState();
    this.initializeConsoleInterception();
  
  }

  private initializeConsoleInterception(): void {
    // Sauvegarder les méthodes originales
    this.originalConsole.log = console.log.bind(console);
    this.originalConsole.error = console.error.bind(console);
    this.originalConsole.info = console.info.bind(console);
    this.originalConsole.warn = console.warn.bind(console);
    this.originalConsole.debug = console.debug.bind(console);
    this.originalConsole.trace = console.trace.bind(console);

    // Intercepter les méthodes console
    console.log = (...args: any[]) => {
      this.originalConsole.log(...args);
      this.addMessage(args, 'log', 'black');
    };

    console.error = (...args: any[]) => {
      this.originalConsole.error(...args);
      this.addMessage(args, 'error', 'red');
    };

    console.info = (...args: any[]) => {
      this.originalConsole.info(...args);
      this.addMessage(args, 'info', 'darkblue');
    };

    console.warn = (...args: any[]) => {
      this.originalConsole.warn(...args);
      this.addMessage(args, 'warn', 'chocolate');
    };

    console.debug = (...args: any[]) => {
      this.originalConsole.debug(...args);
      this.addMessage(args, 'debug', 'darkmagenta');
    };

    console.trace = (...args: any[]) => {
      this.originalConsole.trace(...args);
      this.addMessage(args, 'trace', 'black');
    };
  }

  private addMessage(args: any[], type: ConsoleMessage['type'], color: string): void {
    // TOUJOURS ajouter le message, même si la console n'est pas visible
    const messageText = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ');

    const message: ConsoleMessage = {
      message: messageText,
      type,
      timestamp: new Date(),
      color
    };

    this.messages.push(message);
    
    // Limiter le nombre de messages
    if (this.messages.length > 1000) {
      this.messages = this.messages.slice(-500);
    }
    
    // TOUJOURS émettre les messages
    this.messagesSubject.next([...this.messages]);

    // Sauvegarder dans localStorage seulement si la console est visible
    if (this.showConsoleSubject.value) {
      const logEntry = `${message.timestamp.toISOString()} [${type.toUpperCase()}] ${messageText}`;
      const existingLog = localStorage.getItem('BirdNoteLog.txt') || '';
      localStorage.setItem('BirdNoteLog.txt', existingLog + logEntry + '\n');
    }
  }

  public toggleConsole(): void {
    const newState = !this.showConsoleSubject.value;
    this.showConsoleSubject.next(newState);
    localStorage.setItem('show_console', newState.toString());
    
    // Debug avec console original pour éviter la récursion
    this.originalConsole.log('Console toggled to:', newState);
  }

  public setConsoleVisibility(visible: boolean): void {
    this.showConsoleSubject.next(visible);
    localStorage.setItem('show_console', visible.toString());
    
    // Debug
    this.originalConsole.log('Console visibility set to:', visible);
  }

  public getConsoleVisibility(): boolean {
    return this.showConsoleSubject.value;
  }

  public clearMessages(): void {
    this.messages = [];
    this.messagesSubject.next([]);
  }

  public copyAllMessages(): string {
    return this.messages.map(msg => 
      `${msg.timestamp.toISOString()} [${msg.type.toUpperCase()}] ${msg.message}`
    ).join('\n');
  }

  private loadConsoleState(): void {
    const savedState = localStorage.getItem('show_console');
    const isVisible = savedState === 'true';
    this.showConsoleSubject.next(isVisible);
    
    // Debug avec console original
    if (this.originalConsole.log) {
      this.originalConsole.log('Console state loaded from localStorage:', isVisible);
    }
  }
}