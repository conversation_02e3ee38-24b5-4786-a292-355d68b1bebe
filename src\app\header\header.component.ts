import { Component, EventEmitter, Input, Output,  On<PERSON>ni<PERSON>,  <PERSON><PERSON><PERSON><PERSON> } from "@angular/core"
import { IonicModule } from "@ionic/angular"
import { CommonModule } from "@angular/common"
import  { ThemeService } from "../services/theme.service"
import  { TranslationService } from "../services/traduction-service.service"
import  { LoginService } from "../services/login-service.service"
import  { Subscription } from "rxjs"

@Component({
  selector: "app-header",
  templateUrl: "./header.component.html",
  styleUrls: ["./header.component.scss"],
  standalone: true,
  imports: [IonicModule, CommonModule],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Input() title = ""
  @Input() showBackButton = false
  @Input() backHref = "/"
  @Input() closeLabel?: string
  @Input() closeSlot: "start" | "end" = "start"
  @Input() showUtilities = true
  @Output() close = new EventEmitter<void>()

  translations: any = {}
  private translationSubscription?: Subscription
  // ✅ Subscription pour écouter les changements de langue
  private languageSubscription?: Subscription

  currentLanguage = "an.json"

  constructor(
    private themeService: ThemeService,
    private translationService: TranslationService,
    private loginService: LoginService,
  ) {}

  async ngOnInit() {
    // ✅ S'abonner aux changements de traductions
    this.translationSubscription = this.translationService.translations$.subscribe((translations) => {
      console.log("🔄 HeaderComponent - Traductions mises à jour:", translations)
      this.translations = translations
    })

    // ✅ S'abonner aux changements de langue
    this.languageSubscription = this.translationService.currentLanguage$.subscribe((language) => {
      console.log("🌐 HeaderComponent - Langue changée vers:", language)
      this.currentLanguage = language
    })

    console.log("✅ HeaderComponent - Initialisé avec langue:", this.translationService.getCurrentLanguage())
  }

  ngOnDestroy() {
    if (this.translationSubscription) {
      this.translationSubscription.unsubscribe()
    }
    // ✅ Nettoyer la subscription de langue
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe()
    }
  }

  onClose() {
    this.close.emit()
  }

  switchTheme() {
    this.themeService.switchTheme()
  }

  // ✅ Changement de langue simplifié - utilise le service centralisé
  async changeLanguage(lang: string) {
    try {
      console.log("🌐 HeaderComponent - Demande changement langue vers:", lang)
      await this.translationService.changeLanguage(lang)
      console.log("✅ HeaderComponent - Langue changée avec succès")
    } catch (error) {
      console.error("❌ HeaderComponent - Erreur changement langue:", error)
    }
  }

  translate(key: string): string {
    const translation = this.translations[key]
    if (!translation) {
      console.warn(`⚠️ HeaderComponent - Traduction manquante: ${key}`)
    }
    return translation || key
  }

  logout() {
    this.loginService.logout()
  }
}
