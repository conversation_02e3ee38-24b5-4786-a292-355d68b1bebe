import { Component, OnInit } from '@angular/core';
import { TranslationService } from './services/traduction-service.service';
import { ThemeService } from './services/theme.service';
import { ConsoleService } from './services/console-service.service';
import { NavigationEnd, Router, Event } from '@angular/router';
import { filter } from "rxjs/operators";

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnInit {
  buttonLabel: string = 'Dark Mode';
  showFooter = true

  
  private hiddenFooterRoutes = ["/login","/trouble-shooting"]
  constructor(private translationService: TranslationService,
    private themeService: ThemeService,
    private consoleService: ConsoleService,
    private router: Router,
  ) {}

  ngOnInit() {
    this.translationService.loadTranslations('an.json').subscribe();
    console.log('Application démarrée');
    this.router.events
    .pipe(filter((event: Event): event is NavigationEnd => event instanceof NavigationEnd))
    .subscribe((event: NavigationEnd) => {
      this.checkFooterVisibility(event.urlAfterRedirects)
    })

    // Vérifier la route actuelle au démarrage
    this.checkFooterVisibility(this.router.url)
  }

  changeLanguage(language: string) {
    this.translationService.loadTranslations(language).subscribe();
    document.documentElement.dir = language === 'ar.json' ? 'rtl' : 'ltr';
  }

  translate(key: string): string {
    return this.translationService.translate(key);
  }
  getButtonLabel(): string {
    if (this.themeService.theme === 'light-theme') {
      return 'Light Mode';
    }
    return 'Dark Mode';
  }
  switchTheme(): void {
    this.themeService.switchTheme();
  }
  private checkFooterVisibility(url: string) {
    // Cacher le footer si on est sur une des routes spécifiées
    this.showFooter = !this.hiddenFooterRoutes.some((route) => url.includes(route))
  }
}



  
