<app-header [title]="translate('BUDGET_ALLOCATION')"></app-header>


<ion-content [class.dark-theme]="isDarkTheme">
  <div style="width: 70%; margin: auto;">
    <ion-grid>

      <ion-row style="background-color: #007bff; color: white; text-align: center; font-weight: bold;">
        <ion-col size="2">{{ translate('YEAR') }}</ion-col>
        <ion-col size="5">{{ translate('MONTHLY_BUDGET') }}</ion-col>
        <ion-col size="5">{{ translate('TYPE') }}</ion-col>
      </ion-row>


      <ion-row *ngFor="let budget of budgetAllocations; let i = index">
        <ion-col size="2" [ngStyle]="{
          'text-align': 'center',
          'background-color': i % 2 === 0 ? '#f4f5f8' : '#d9e0f5'
        }">
          <span style="color:black">{{ budget.year }}</span>
        </ion-col>
        <ion-col size="5" [ngStyle]="{
          'text-align': 'center',
          'background-color': i % 2 === 0 ? '#f4f5f8' : '#d9e0f5'
        }">
          <span style="color:black">{{ budget.monthlyBudget }}</span>
        </ion-col>
        <ion-col size="5" [ngStyle]="{
          'text-align': 'center',
          'background-color': i % 2 === 0 ? '#f4f5f8' : '#d9e0f5'
        }">
          <span style="color:black">{{ budget.type }}</span>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
</ion-content>
